// Optimized Genre Categories Component - Tailwind @apply with enhanced UI

// Container
.genre-container {
  @apply bg-white dark:bg-neutral-800 mt-2 overflow-y-auto;
  @apply w-full md:w-[30rem] lg:w-[42rem] xl:w-[44rem] 2xl:w-[50rem];
  @apply max-h-96 lg:max-h-[32rem];
  @apply rounded-xl shadow-xl backdrop-blur-sm border border-neutral-200 dark:border-neutral-700;
  @apply p-4 space-y-4 z-50;
}

// Header
.genre-header {
  @apply space-y-3;
}

.genre-title {
  @apply flex items-center gap-2;
}

.genre-title-icon {
  @apply w-4 h-4 text-primary-100;
}

.genre-title-text {
  @apply text-sm font-semibold text-neutral-700 dark:text-neutral-300;
}

// Search
.genre-search {
  @apply relative;
}

.search-input-wrapper {
  @apply relative flex items-center;
}

.search-icon {
  @apply absolute left-3 w-4 h-4 text-neutral-400 dark:text-neutral-500 pointer-events-none;
}

.search-input {
  @apply w-full h-10 pl-10 pr-10 text-sm rounded-lg border;
  @apply bg-neutral-50 dark:bg-neutral-700 border-neutral-200 dark:border-neutral-600;
  @apply text-neutral-900 dark:text-white placeholder-neutral-500 dark:placeholder-neutral-400;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-100/50 focus:border-primary-100;
  @apply focus:bg-white dark:focus:bg-neutral-600;
}

.clear-search-btn {
  @apply absolute right-2 p-1 rounded-md;
  @apply text-neutral-400 hover:text-neutral-600 dark:text-neutral-500 dark:hover:text-neutral-300;
  @apply hover:bg-neutral-100 dark:hover:bg-neutral-600;
}

.clear-icon {
  @apply w-4 h-4;
}

// Genre Content
.genre-content {
  @apply space-y-3;
}



.section-header {
  @apply flex items-center gap-2;
}

.section-title {
  @apply text-base font-semibold text-neutral-800 dark:text-neutral-200 whitespace-nowrap;
}

.section-divider {
  @apply flex-1 h-px bg-gradient-to-r from-neutral-300 to-transparent dark:from-neutral-600;
}
// Genre Grid
.genre-grid {
  @apply flex flex-wrap gap-2;
}
// Genre Chips
.genre-chip {
  @apply relative inline-flex items-center gap-1.5 px-1.5 py-0.5 rounded-lg text-xs font-medium;
  @apply border cursor-pointer select-none;
  @apply hover:scale-105 active:scale-95;

  // Normal state
  &.genre-normal {
    @apply bg-neutral-50 dark:bg-neutral-700 border-neutral-200 dark:border-neutral-600;
    @apply text-neutral-700 dark:text-neutral-300;
    @apply hover:bg-neutral-100 dark:hover:bg-neutral-600 hover:border-primary-100;
    @apply hover:text-primary-100 hover:shadow-md;
  }

  // Active state
  &.genre-active {
    @apply   border-primary-100 dark:border-primary-100;
    @apply text-primary-100 dark:text-primary-100;
    @apply shadow-md;
  }

  // Disabled state
  &.genre-disabled {
    @apply  border-red-200 ;
    @apply text-red-600 dark:text-red-400;
    @apply cursor-not-allowed opacity-75;
    @apply hover:scale-100;
  }
}

.genre-name {
  @apply truncate max-w-24;
}

.genre-check-icon {
  @apply w-3 h-3 flex-shrink-0;
}

// No Results
.no-results {
  @apply flex flex-col items-center justify-center py-8 text-center;
}

.no-results-icon {
  @apply w-12 h-12 text-neutral-400 dark:text-neutral-500 mb-3;
}

.no-results-text {
  @apply text-sm text-neutral-600 dark:text-neutral-400 mb-3;
}

.clear-search-link {
  @apply text-xs text-primary-100 hover:text-primary-100 font-medium;
  @apply hover:underline ;
}

// Genre Hover Info
.genre-hover-info {
  @apply mt-4 pt-4 border-t border-neutral-200 dark:border-neutral-700;
  @apply transition-all duration-300 ease-in-out;
}

.hover-divider {
  @apply w-full h-px bg-gradient-to-r from-transparent via-neutral-300 to-transparent;
  @apply dark:via-neutral-600 mb-3;
}

.hover-content {
  @apply flex gap-3 items-start;
}

.hover-icon {
  @apply flex-shrink-0 p-1 rounded-md bg-primary-100 dark:bg-primary-100/30;
}

.info-icon {
  @apply w-4 h-4 text-primary-100 dark:text-primary-100;
}

.hover-text {
  @apply flex-1 min-w-0;
}

.hover-title {
  @apply text-sm font-semibold text-neutral-800 dark:text-neutral-200 mb-1;
}

.hover-description {
  @apply text-xs text-neutral-600 dark:text-neutral-400 leading-relaxed;
}