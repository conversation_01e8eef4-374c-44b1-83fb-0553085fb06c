<!-- End Chapter Navigation -->
<div class="end-chapter-navigation">
  <div class="end-chapter-content">
    <button
      class="end-nav-button end-nav-prev"
      (click)="onNavigate('next')"
      aria-label="Chương trước"
      [disabled]="isLoading"
      [class.end-nav-active]="canNavigatePrev()"
    >
      <svg class="end-nav-icon" viewBox="0 0 24 24">
        <polyline points="15 18 9 12 15 6" />
      </svg>
      <span class="end-nav-text">Chương trước</span>
    </button>

    <div class="end-chapter-info">
      <h3 class="end-chapter-title"><PERSON><PERSON><PERSON> thú<PERSON> ch<PERSON></h3>
      <p class="end-chapter-subtitle">{{ navigationTitle() }}</p>
    </div>

    <button
      class="end-nav-button end-nav-next"
      (click)="onNavigate('prev')"
      aria-label="Chương tiếp theo"
      [disabled]="isLoading"
      [class.end-nav-active]="canNavigateNext()"
    >
      <span class="end-nav-text">Chương tiếp</span>
      <svg class="end-nav-icon" viewBox="0 0 24 24">
        <polyline points="9 18 15 12 9 6" />
      </svg>
    </button>
  </div>
</div>
