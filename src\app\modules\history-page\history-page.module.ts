import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HistoryPageComponent } from './history-page.component';
import { RouterModule } from '@angular/router';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { EmptyComponent } from '@components/common/empty/empty.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';



@NgModule({
  declarations: [HistoryPageComponent],
  imports: [
    CommonModule,
    PaginationComponent,
    SpinnerComponent,
    GridComicComponent,
    EmptyComponent,
    BreadcrumbComponent,
    RouterModule.forChild([
      {
        path: '',
        component: HistoryPageComponent
      }
    ])
  ]
})
export class HistoryPageModule { }
