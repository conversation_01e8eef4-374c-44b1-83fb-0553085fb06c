<ng-container *ngIf="isVisible">
  <div class="user-info-modal" (appClickOutside)="isVisible = false">
    @if (!isLoadInfoUser) {
      <!-- Loading State -->
      <div class="user-info-loading">
        <div class="loading-spinner">
          <div class="loading-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"/>
              <path d="M12 6v6l4 2"/>
            </svg>
          </div>
          <p class="loading-text">Đang tải thông tin...</p>
        </div>
      </div>
    } @else {
      <!-- Header -->
      <div class="user-info-header">
        <div class="user-info-title">
          <img
            loading="lazy"
            src="/favicon.png"
            class="header-icon"
            alt="favicon-icon"
          />
          <span class="header-text">[Thông tin]</span>
        </div>
        <button
          class="user-info-close"
          (click)="this.setVisible(false); isLoadInfoUser = false"
          aria-label="Đóng popup"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="user-info-content">
        <!-- Avatar Section -->
        <div class="user-info-avatar">
          <div class="avatar-wrapper">
            <img
              loading="lazy"
              [src]="UserInfo?.avatar"
              class="user-avatar-image"
              alt="{{ UserInfo?.username }}"
              onerror="this.src='/default_avatar.jpg'"
            />
            <div class="user-info-level">
              <svg class="level-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              <span class="level-text">{{ UserInfo?.levelInfo?.level || 5 }}</span>
            </div>
          </div>
        </div>

        <!-- Details Section -->
        <div class="user-info-details">
          <!-- User ID -->
          <div class="detail-row">
            <span class="detail-label">ID:</span>
            <span class="detail-value">#{{ UserInfo!.id }}</span>
          </div>

          <!-- Username -->
          <div class="detail-row username-row">
            <span class="username-text">{{ UserInfo?.username }}</span>
          </div>

          <!-- Join Date -->
          <div class="detail-row">
            <span class="detail-label">Ngày tham gia:</span>
            <span class="detail-value">{{ UserInfo?.createAt | date : "dd/MM/yyyy" }}</span>
          </div>

          <!-- Experience -->
          <div class="detail-row">
            <span class="detail-label">{{ UserInfo?.typeLevel === 0 ? "Tinh hoa:" : "Kinh nghiệm:" }}</span>
            <span class="detail-value experience-value">{{ UserInfo?.experience | number }}</span>
          </div>

          <!-- Level -->
          <div class="detail-row">
            <span class="detail-label">{{ UserInfo?.typeLevel === 0 ? "Cảnh giới:" : "Cấp độ:" }}</span>
            <span class="detail-value level-value">{{ UserInfo?.levelInfo?.level }}</span>
          </div>

          <!-- Motto -->
          <div class="motto-section">
            <div class="motto-text">{{ UserInfo?.maxim || "Chưa cập nhật châm ngôn" }}</div>
          </div>
        </div>
      </div>
    }
  </div>
</ng-container>
