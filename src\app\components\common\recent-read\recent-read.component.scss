// Modern Recent Read Component - Comic Website Design
.recent-read-container {
  @apply bg-white dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-sm overflow-hidden;
}

// Enhanced Header
.recent-read-header {
  @apply flex items-center justify-between p-3 bg-neutral-50 dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700;
}

.header-title-section {
  @apply flex items-center gap-2;
}

.header-icon {
  @apply w-4 h-4 text-primary-100;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.header-title {
  @apply text-base font-bold text-neutral-900 dark:text-white;
}

.header-count {
  @apply px-2 py-1 bg-primary-100 text-white text-xs font-bold rounded-full;
}

.header-actions {
  @apply flex items-center;
}

.view-more-button {
  @apply flex items-center gap-2 px-2 py-1 text-neutral-600 dark:text-neutral-400 hover:text-primary-100 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded-lg;
}



.view-more-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Content Area
.recent-read-content {
  @apply bg-white dark:bg-dark-bg;
}

// Empty State
.empty-state {
  @apply p-8 text-center;
}

.empty-state-content {
  @apply space-y-4;
}

.empty-state-icon {
  @apply w-16 h-16 mx-auto text-neutral-300 dark:text-neutral-600;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.empty-state-title {
  @apply text-lg font-semibold text-neutral-900 dark:text-white;
}

.empty-state-description {
  @apply text-sm text-neutral-500 dark:text-neutral-400;
}

// Comic List
.comic-list {
  @apply space-y-1.5 p-2;
}

.comic-item {
}

.comic-item-content {
  @apply flex items-center gap-2 p-2 bg-neutral-50 dark:bg-neutral-800/50 hover:bg-neutral-100 dark:hover:bg-neutral-700/50 rounded-lg border border-neutral-200 dark:border-neutral-700;
}

// Comic Image
.comic-image-container {
  @apply relative flex-shrink-0;
}

.comic-image-link {
  @apply relative block overflow-hidden rounded-lg;
}

.comic-image {
  @apply w-12 h-16 object-cover;
}

.image-overlay {
  @apply absolute inset-0 bg-black/0 hover:bg-black/10;
}

// Comic Info
.comic-info {
  @apply flex-1 min-w-0 space-y-2;
}

.comic-main-info {
  @apply space-y-1;
}

.comic-title {
  @apply text-sm font-bold text-neutral-900 dark:text-white line-clamp-1;
}

.comic-title-link {
  @apply hover:text-primary-100;
}

.comic-chapter {
  @apply flex items-center gap-2 text-xs text-neutral-600 dark:text-neutral-400;
}

.chapter-icon {
  @apply w-4 h-4 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.comic-chapter-link {
  @apply hover:text-primary-100 hover:underline line-clamp-1;
}

// Comic Meta
.comic-meta {
  @apply flex flex-row items-start gap-1 justify-between;
}

.comic-rating {
  @apply flex items-center gap-1 text-xs text-orange-500 dark:text-orange-400;
}

.rating-icon {
  @apply w-3 h-3;
  fill: currentColor;
  stroke: none;
}

.rating-value {
  @apply font-medium;
}

.comic-update-time {
  @apply flex items-center gap-1 text-xs text-neutral-500 dark:text-neutral-400;
}

.time-icon {
  @apply w-3 h-3;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.time-value {
  @apply font-medium;
}


