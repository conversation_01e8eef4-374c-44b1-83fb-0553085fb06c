import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { INotification, IServiceResponse, IUser, VoteInfo } from '@schema';
import { BehaviorSubject, Observable } from 'rxjs';
import { StorageService } from './storage.service';
import { UrlService } from './url.service';

@Injectable({
  providedIn: 'root',
})
export class AccountService {
  userSubject = new BehaviorSubject<IUser | undefined>(this.storageService.GetUserData());

  constructor(private httpClient: HttpClient,
    private storageService: StorageService,
    private urlService: UrlService) { }
  AddComment(chapterId: number, content: string, replyfromUser?: number, replyfromCmt?: number | null) {
    return this.httpClient.post(`${this.urlService.apiUrl}/user/comment`, {
      chapterId,
      content,
      replyfromUser,
      replyfromCmt
    });
  }
  getAuthorizationToken(): string | undefined {
    return this.userSubject.value?.token;
  }
  isAuthenticated(): boolean {
    return  !!this.userSubject.value;
  }

  GetLocalUser() {
    return this.userSubject.asObservable()
  }
  GetRemoteUser() {
    return this.httpClient.get(`${this.urlService.apiUrl}/user/me`);
  }
  GetUserById(id: number) {
    return this.httpClient.get<IServiceResponse<IUser>>(`${this.urlService.apiUrl}/user/${id}`);
  }
  Follow(comicid: number, isFollow: boolean) {
    return this.httpClient.post<IServiceResponse<number>>(
      `${this.urlService.apiUrl}/user/follow?comicid=${comicid}&follow=${isFollow}`,
      {},
    );
  }
  GetFollowedComics(page = 1, size = 28) {
    const searchParams = new URLSearchParams({ page: page.toString(), size: size.toString() }).toString();

    return this.httpClient.get(`${this.urlService.apiUrl}/user/followed-comics?${searchParams}`);
  }
  Login(email: string, password: string) {
    return this.httpClient.post(
      `${this.urlService.apiUrl}/auth/login`, { email, password },
    );
  }
  LoginWithSocial(user: any) {

    return this.httpClient.post(
      `${this.urlService.apiUrl}/auth/social-login`, user,
    );
  }
  SendEmailConfirm(email: string, userid: number) {
    return this.httpClient.get(`${this.urlService.apiUrl}/auth/send-confirm-email?email=${email}&userId=${userid}`)
  }
  GetCommentsByComicId(comicId: number, page: number, step: number) {
    return this.httpClient.get(
      `${this.urlService.apiUrl}/comments/comic/${comicId}?page=${page}&size=${step}`,
    );
  }
  Logout() {
    this.storageService.Logout();
    this.userSubject.next(undefined);
  }
  Register(name: string, email: string, password: string) {
    return this.httpClient.post(`${this.urlService.apiUrl}/auth/register`, {
      name,
      email,
      password,
    });
  }
  ForgetPassword(email: string) {
    return this.httpClient.get(
      `${this.urlService.apiUrl}/auth/forgot?email=${email}`,
    );
  }
  UpdateAvatar(avatar: FormData) {
    return this.httpClient.post(
      `${this.urlService.apiUrl}/user/update/avatar`,
      avatar,
    );
  }
  SaveUser(user: IUser) {
    this.storageService.SetUserData(user);
    this.userSubject.next(user);
  }
  GetUserDeconfirm() {
    return this.storageService.GetUserDeconfirm();
  }
  SaveUserDeconfirm(infoUser: any) {
    this.storageService.SetUserDeconfirm(infoUser);
  }
  GetRememberMeData() {
    return this.storageService.GetRememberMeData();
  }
  SetRememberMeData(rememberMeData: any) {
    this.storageService.SetRememberMeData(rememberMeData);
  }
  UpdateInfo(user: IUser) {
    return this.httpClient.post(`${this.urlService.apiUrl}/user/update`, user);
  }
  UpdatePassword(newPassword: string) {
    return this.httpClient.post(
      `${this.urlService.apiUrl}/user/update/password`,
      newPassword,
    );
  }
  UpdateTypeLevel(typeLevel: number) {
    return this.httpClient.post(`${this.urlService.apiUrl}/user/update/typelevel/${typeLevel}`, {});
  }
  UpdateMaxim(maxim: string | null) {
    return this.httpClient.post(
      `${this.urlService.apiUrl}/user/update/maxim?maxim=${maxim}`,
      {},
    );
  }

  getUserNotify() {
    return this.httpClient.get<IServiceResponse<INotification[]>>(`${this.urlService.apiUrl}/user/notify`);
  }
  updateUserNotify(idNotify: number | null, IsRead: boolean | null) {
    return this.httpClient.post(`${this.urlService.apiUrl}/user/notify/update`, {
      ID: idNotify,
      IsRead: IsRead,
    });
  }
  deleteUserNotify(idNotify: number | null) {
    return this.httpClient.delete(
      `${this.urlService.apiUrl}/user/notify/delete/${idNotify}`,
    );
  }
  voteComic(
    comicId: number,
    votePoint: number,
  ): Observable<IServiceResponse<VoteInfo>> {
    const url = `${this.urlService.apiUrl}/user/vote/update`;
    const body = { comicId, votePoint };
    return this.httpClient.post(url, body) as Observable<
      IServiceResponse<VoteInfo>
    >;
  }


}
