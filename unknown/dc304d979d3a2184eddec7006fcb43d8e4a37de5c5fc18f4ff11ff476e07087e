// tutorial.service.ts

import { isPlatformBrowser } from "@angular/common";
import { Inject, Injectable, Optional, PLATFORM_ID, REQUEST } from "@angular/core";
import globalConfig from "GlobalConfig";

@Injectable({ providedIn: 'root' })
export class UrlService {

    baseUrl = this.getBaseUrl();
    apiUrl = this.getApiUrl();


    constructor(@Inject(PLATFORM_ID) private platformId: object,
        @Optional() @Inject(REQUEST) private request: Request) {
    }

    buildUrl(path: string) {
        return this.apiUrl + path;
    }

    getApiUrl(): string {
        if (isPlatformBrowser(this.platformId) && !window.location.origin.includes('localhost')) {
            return window.location.origin + '/api';
        }
        return globalConfig.BASE_API_URL + '/api';
    }
    
    getBaseUrl() {
        if (isPlatformBrowser(this.platformId)) {
            return window.location.origin;
        }
        const host = this.request.headers.get("host");
        if (!host) return globalConfig.BASE_URL;
        if (host.includes(':')) return globalConfig.BASE_URL
        return `https://${host}`;

    }
}

