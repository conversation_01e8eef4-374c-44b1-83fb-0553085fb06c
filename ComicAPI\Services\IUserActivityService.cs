using ComicAPI.Models;

namespace ComicAPI.Services
{
    /// <summary>
    /// Interface for tracking user activity and managing user sessions
    /// </summary>
    public interface IUserActivityService
    {
        /// <summary>
        /// Track user activity (called from middleware)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="ipAddress">User's IP address</param>
        /// <param name="userAgent">User's browser/device info</param>
        /// <param name="endpoint">API endpoint accessed</param>
        Task TrackUserActivityAsync(int userId, string? ipAddress, string? userAgent, string? endpoint);

        /// <summary>
        /// Track user login
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="ipAddress">Login IP address</param>
        Task TrackUserLoginAsync(int userId, string? ipAddress);

        /// <summary>
        /// Get user's last activity
        /// </summary>
        /// <param name="userId">User ID</param>
        Task<DateTime?> GetLastActivityAsync(int userId);

        /// <summary>
        /// Check if user is currently online (active within last 5 minutes)
        /// </summary>
        /// <param name="userId">User ID</param>
        Task<bool> IsUserOnlineAsync(int userId);

        /// <summary>
        /// Get online users count
        /// </summary>
        Task<int> GetOnlineUsersCountAsync();

        /// <summary>
        /// Get user activity statistics
        /// </summary>
        /// <param name="userId">User ID</param>
        Task<UserActivityStats> GetUserActivityStatsAsync(int userId);

        /// <summary>
        /// Cleanup old activity records (called by background service)
        /// </summary>
        Task CleanupOldActivitiesAsync();
    }

    /// <summary>
    /// User activity statistics
    /// </summary>
    public class UserActivityStats
    {
        public DateTime? LastActivity { get; set; }
        public DateTime? LastLogin { get; set; }
        public string? LastLoginIp { get; set; }
        public int TotalSessions { get; set; }
        public TimeSpan AverageSessionDuration { get; set; }
        public int ActivitiesLast24Hours { get; set; }
        public int ActivitiesLast7Days { get; set; }
        public List<string> RecentEndpoints { get; set; } = new();
    }
}
