import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { PrivacyPolicyComponent } from './privacy-policy.component';



@NgModule({
  declarations: [PrivacyPolicyComponent],
  imports: [
    RouterModule,
    CommonModule,
    RouterModule.forChild([
      {
        path: '', component: PrivacyPolicyComponent,
      }
    ])
  ]
})
export class PrivacyPolicyModule { }
