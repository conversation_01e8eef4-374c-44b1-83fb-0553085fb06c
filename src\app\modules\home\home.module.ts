import {  NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { CarouselLandingComponent } from './carousel-landing/carousel-landing.component';
import { HomePageComponent } from './home-page.component';
import { RecentReadComponent } from '@components/common/recent-read/recent-read.component';
import { TopListComponent } from '@components/common/top-list/top-list.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { AnouncementComponent } from '@components/common/anouncement/anouncement.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { TopUsersComponent } from '@components/common/top-users/top-users.component';

@NgModule({
  declarations: [
    HomePageComponent,
  ],

  imports: [
    RouterModule.forChild([{ path: '', component: HomePageComponent }]),
    CommonModule,
    TopListComponent,
    PaginationComponent,
    AnouncementComponent,
    CarouselLandingComponent,
    GridComicComponent,
    RecentReadComponent,
    TopUsersComponent

  ],
})
export class HomeModule {

}
