<ng-container>
  <app-breadcrumb
    class="z-10 my-2 container mx-auto flex"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Đang theo dõi', url: '' }
    ]"
  >
  </app-breadcrumb>
  <div class="text-center absolute top-1/2 left-1/2" *ngIf="isLoading; else loadedContent">
    <app-spinner [sizeSpinner]="'40'"></app-spinner>
  </div>
  <ng-template #loadedContent>
    <div id="comics" class="md:container mx-auto" *ngIf="comics.length > 0; else empty">
      <app-grid-comic
        [listComics]="comics"
        [nPreview]="28"
        [title]="'Đang theo dõi'"
        [_class]="
          'grid gap-[12px] grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 mx-3 lg:mx-0'
        "
      >
        <ng-template #iconTemplate>
          <svg
            class="size-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path d="M12 20l-7 -7a4 4 0 0 1 6.5 -6a.9 .9 0 0 0 1 0a4 4 0 0 1 6.5 6l-7 7" />
          </svg>
        </ng-template>
        <ng-template #actionTemplate let-comic="comic">
          <button
            (click)="onUnFollowClick([comic.id])"
            class="bg-red-500 hover:bg-primary-200 m-1.5 rounded-md absolute top-0 right-0 z-10 text-white"
          >
            <svg class="mx-1.5 my-1 size-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              />
            </svg>
          </button>
        </ng-template>
      </app-grid-comic>
      <app-pagination
        [totalpage]="totalpage"
        (OnChange)="OnChangePage($event)"
        [currentPage]="currentPage"
      >
      </app-pagination>
    </div>
    <ng-template #empty>
      <div class="w-1/3 mt-20 lg:min-h-1/2 lg:w-40 flex justify-center items-center mx-auto">
        <app-empty [message]="'Không có truyện đang theo dõi'"></app-empty>
      </div>
    </ng-template>
  </ng-template>
</ng-container>
