<ng-container>
  <app-breadcrumb
    class="z-10 my-2 container mx-auto flex"
    [Links]="[
      { label: 'Trang chủ', url: '/' },
      { label: 'Đang theo dõi', url: '' }
    ]"
  >
  </app-breadcrumb>
  <div class="text-center absolute top-1/2 left-1/2" *ngIf="isLoading; else loadedContent">
    <app-spinner [sizeSpinner]="'40'"></app-spinner>
  </div>
  <ng-template #loadedContent>
    <div id="comics" class="md:container mx-auto" *ngIf="comics.length > 0; else empty">
      <app-grid-comic
        [listComics]="comics"
        [nPreview]="28"
        [title]="'Đang theo dõi'"
        [_class]="
          'grid gap-[12px] grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 mx-3 lg:mx-0'
        "
      >
        <ng-template #iconTemplate>
          <svg
            class="size-6"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            stroke-width="2"
            stroke="currentColor"
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path stroke="none" d="M0 0h24v24H0z" />
            <path d="M12 20l-7 -7a4 4 0 0 1 6.5 -6a.9 .9 0 0 0 1 0a4 4 0 0 1 6.5 6l-7 7" />
          </svg>
        </ng-template>
                <ng-template #actionTemplate let-comic="comic">
          <button
            (click)="onUnFollowClick([comic.id])"
            class="bg-red-500 hover:bg-primary-200 m-1.5 rounded-md absolute top-0 right-0 z-10 text-white"
          >
            <svg
              fill="currentColor"
              class="size-4 mx-1.5 my-1"
              viewBox="0 0 32 32"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
              <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
              <g id="SVGRepo_iconCarrier">
                <title>trash-can</title>
                <path
                  d="M30 7.249h-5.598l-3.777-5.665c-0.137-0.202-0.366-0.334-0.625-0.334h-8c-0 0-0.001 0-0.001 0-0.259 0-0.487 0.131-0.621 0.331l-0.002 0.003-3.777 5.665h-5.599c-0.414 0-0.75 0.336-0.75 0.75s0.336 0.75 0.75 0.75v0h3.315l1.938 21.319c0.036 0.384 0.356 0.682 0.747 0.682 0 0 0 0 0.001 0h16c0 0 0.001 0 0.001 0 0.39 0 0.71-0.298 0.745-0.679l0-0.003 1.938-21.319h3.316c0.414 0 0.75-0.336 0.75-0.75s-0.336-0.75-0.75-0.75v0zM12.401 2.75h7.196l2.999 4.499h-13.195zM23.314 29.25h-14.63l-1.863-20.5 18.358-0.001zM11 11.25c-0.414 0-0.75 0.336-0.75 0.75v0 14c0 0.414 0.336 0.75 0.75 0.75s0.75-0.336 0.75-0.75v0-14c-0-0.414-0.336-0.75-0.75-0.75v0zM16 11.25c-0.414 0-0.75 0.336-0.75 0.75v0 14c0 0.414 0.336 0.75 0.75 0.75s0.75-0.336 0.75-0.75v0-14c-0-0.414-0.336-0.75-0.75-0.75v0zM21 11.25c-0.414 0-0.75 0.336-0.75 0.75v0 14c0 0.414 0.336 0.75 0.75 0.75s0.75-0.336 0.75-0.75v0-14c-0-0.414-0.336-0.75-0.75-0.75v0z"
                ></path>
              </g>
            </svg>
          </button>
        </ng-template>
      </app-grid-comic>
      <app-pagination
        [totalpage]="totalpage"
        (OnChange)="OnChangePage($event)"
        [currentPage]="currentPage"
      >
      </app-pagination>
    </div>
    <ng-template #empty>
      <div class="w-1/3 mt-20 lg:min-h-1/2 lg:w-40 flex justify-center items-center mx-auto">
        <app-empty [message]="'Không có truyện đang theo dõi'"></app-empty>
      </div>
    </ng-template>
  </ng-template>
</ng-container>
