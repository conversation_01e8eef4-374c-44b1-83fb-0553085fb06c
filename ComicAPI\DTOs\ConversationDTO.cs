using ComicAPI.Enums;
using ComicAPI.Models;

namespace ComicAPI.DTOs
{
    public class ConversationDTO
    {
        public Guid ID { get; set; }
        public string? Name { get; set; }
        public DateTime UpdatedAt { get; set; }
        public List<MessageDTO> Messages { get; set; } = new List<MessageDTO>();
        public ChatChannel Channel { get; set; }
        public string Icon { get; set; } = string.Empty;
        public int HostId { get; set; }

        public ConversationDTO() { }



        public ConversationDTO(Conversation conversation)
        {
            ID = conversation.ID;
            Name = conversation.Name;
            UpdatedAt = conversation.UpdatedAt;
            Channel = conversation.Channel;
            Messages = conversation.Messages?.Select(m => new MessageDTO(m)).ToList() ?? new List<MessageDTO>();
            Icon = conversation.Icon ?? string.Empty;
            HostId = conversation.HostId ?? 0;
        }
    }

}
