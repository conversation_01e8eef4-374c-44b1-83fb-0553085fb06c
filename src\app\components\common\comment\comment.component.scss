// Enhanced Comment Component Styles - Matching Comic Website Design

// Container Styles
.comment-section-container {
  @apply md:container mx-auto mt-8 space-y-6;
}

// Login Prompt Styles
.login-prompt-wrapper {
  @apply mb-6;
}

.login-prompt-card {
  @apply flex items-center gap-4 p-6 bg-white dark:bg-neutral-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm;
}

.login-icon {
  @apply w-12 h-12 text-primary-100 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.login-prompt-content {
  @apply flex-1;
}

.login-prompt-title {
  @apply text-lg font-bold text-gray-900 dark:text-white mb-1;
}

.login-prompt-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.login-prompt-button {
  @apply flex items-center gap-2 px-6 py-3 bg-primary-100 hover:bg-primary-200 text-white font-medium rounded-lg transition-colors duration-200;
}

.login-button-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Comments Header Styles
.comments-header {
  @apply bg-white dark:bg-neutral-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6;
}

.comments-title-section {
  @apply space-y-2;
}

.comments-title-wrapper {
  @apply flex items-center gap-3;
}

.comments-title-icon {
  @apply w-6 h-6 text-primary-100;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.comments-title {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.comments-count-badge {
  @apply px-3 py-1 bg-primary-100 text-white text-sm font-bold rounded-full;
}

.comments-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// Comments Content Styles
.comments-content {
  @apply space-y-6;
}

// Comment Form Styles
.comment-form-card {
  @apply bg-white dark:bg-neutral-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm;
}

.comment-form {
  @apply space-y-4;
}

.comment-form-header {
  @apply flex items-center gap-3 p-4 border-b border-gray-100 dark:border-gray-700;
}

.form-user-avatar {
  @apply relative;
}

.form-avatar-img {
  @apply w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600;
}

.avatar-status-indicator {
  @apply absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-neutral-800 rounded-full;
}

.form-user-info {
  @apply flex flex-col;
}

.form-username {
  @apply text-sm font-semibold text-gray-900 dark:text-white;
}

.form-user-role {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

.comment-form-body {
  @apply p-4;
}

.comment-textarea {
  @apply w-full h-24 p-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:ring-2 focus:ring-primary-100 focus:border-primary-100 transition-colors duration-200;
}

.comment-form-footer {
  @apply flex items-center justify-between p-4 bg-gray-50 dark:bg-neutral-700/50;
}

.form-tools {
  @apply flex items-center gap-2;
}

.tool-button {
  @apply flex items-center gap-2 px-3 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-neutral-600 rounded-lg transition-colors duration-200 border-none cursor-pointer;
}

.tool-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.tool-label {
  @apply text-sm font-medium;
}

.emoji-picker-container {
  @apply relative;
}

.emoji-picker-dropdown {
  @apply absolute top-full right-0 mt-2 z-50 translate-x-[200px];
}

.form-actions {
  @apply flex items-center gap-3;
}

.cancel-button {
  @apply px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 rounded-lg transition-colors duration-200 cursor-pointer;
}

.submit-button {
  @apply flex items-center gap-2 px-6 py-2 bg-primary-100 hover:bg-primary-200 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &:disabled {
    @apply cursor-not-allowed;
  }
}

.submit-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Comments List Styles
.comments-list {
  @apply space-y-2;
}

.comment-item {
  @apply space-y-1;
}

// Comment Block Styles
.comment-block {
  @apply bg-white dark:bg-neutral-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden;

  &.reply-comment {
    @apply bg-gray-50 dark:bg-neutral-700/50 border-gray-100 dark:border-gray-600;
  }
}

.comment-main {
  @apply p-3 pb-1;
}

.comment-avatar-section {
  @apply flex-shrink-0;
}

.comment-user-avatar {
  @apply relative cursor-pointer;
}

.comment-avatar-img {
  @apply w-12 h-12 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 hover:border-primary-100 transition-colors duration-200;
}

.avatar-status {
  @apply absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-neutral-800 rounded-full;
}

.comment-body {
  @apply flex-1 space-y-2;
}

.comment-header {
  @apply flex items-start justify-between gap-3;
}

.comment-user-info {
  @apply flex items-center gap-2;
}

.comment-username {
  @apply text-sm font-bold text-gray-900 dark:text-white;
}

.comment-user-badge {
  @apply px-2 py-0.5 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs font-medium rounded-full;
}

.comment-meta {
  @apply flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400;
}

.comment-date {
  @apply font-medium;
}

.comment-chapter-link {
  @apply text-primary-100 hover:text-primary-200 font-medium hover:underline;
}

.comment-content-text {
  @apply text-sm text-gray-700 dark:text-gray-300 leading-relaxed break-words;
}

.comment-footer {
  @apply flex items-center justify-between;
}

.comment-reactions {
  @apply flex items-center gap-1;
}

.reaction-button {
  @apply flex items-center gap-1 px-2 py-1 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-neutral-600 rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &.like-button:hover {
    @apply text-green-600 dark:text-green-400;
  }

  &.dislike-button:hover {
    @apply text-red-600 dark:text-red-400;
  }
}

.reaction-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.reaction-count {
  @apply text-xs font-medium;
}

.comment-actions {
  @apply flex items-center gap-2;
}

.action-button {
  @apply flex items-center gap-1 px-3 py-1 text-gray-600 dark:text-gray-400 hover:text-primary-100 hover:bg-primary-100/10 rounded-lg transition-colors duration-200 border-none cursor-pointer;
}

.action-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.view-replies-section {
  @apply pt-2 border-t border-gray-100 dark:border-gray-700;
}

.view-replies-button {
  @apply flex items-center gap-2 text-primary-100 hover:text-primary-200 text-sm font-medium hover:underline border-none cursor-pointer;
}

.view-replies-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Replies Styles
.replies-container {
  @apply ml-12 space-y-3 overflow-hidden transition-all duration-300;
  height: 0;

  &.replies-expanded {
    @apply overflow-visible;
    height: auto;
  }
}

.replies-list {
  @apply space-y-3;
}

// Reply Form Styles
.reply-form-card {
  @apply bg-gray-50 dark:bg-neutral-700/50 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden;
}

.reply-form {
  @apply space-y-3;
}

.reply-form-header {
  @apply flex gap-3 p-3;
}

.reply-user-avatar {
  @apply flex-shrink-0;
}

.reply-avatar-img {
  @apply w-8 h-8 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600;
}

.reply-form-content {
  @apply flex-1;
}

.reply-textarea {
  @apply w-full p-2 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-neutral-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:ring-2 focus:ring-primary-100 focus:border-primary-100 transition-colors duration-200;
}

.reply-form-footer {
  @apply flex items-center justify-between px-3 pb-3;
}

.reply-tools {
  @apply flex items-center gap-2;
}

.reply-actions {
  @apply flex items-center gap-2;
}

.cancel-reply-button {
  @apply px-3 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium border border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 rounded-lg transition-colors duration-200 cursor-pointer;
}

.submit-reply-button {
  @apply flex items-center gap-1 px-4 py-1 bg-primary-100 hover:bg-primary-200 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;
}

// Pagination Styles
.comments-pagination {
  @apply pt-6 border-t border-gray-200 dark:border-gray-700;
}

// Responsive Design
@media (max-width: 768px) {
  .comment-section-container {
    @apply mt-4 space-y-4;
  }

  .login-prompt-card {
    @apply flex-col text-center gap-3;
  }

  .comments-header {
    @apply p-4;
  }

  .comment-form-card {
    @apply rounded-lg;
  }

  .comment-form-header {
    @apply p-3;
  }

  .comment-form-body {
    @apply p-3;
  }

  .comment-form-footer {
    @apply flex-col items-stretch gap-3 p-3;
  }

  .form-actions {
    @apply justify-end;
  }

  .comment-block {
    @apply rounded-lg;
  }

  .comment-main {
    @apply p-2;
  }

  .comment-header {
    @apply flex-col items-start gap-2;
  }

  .comment-meta {
    @apply flex-col items-start gap-1;
  }

  .replies-container {
    @apply ml-8;
  }
}
