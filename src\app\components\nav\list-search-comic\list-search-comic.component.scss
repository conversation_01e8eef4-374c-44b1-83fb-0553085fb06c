.custom-transition {
    @apply w-2/4 rounded-lg pl-4 pr-8 py-2  outline-none transition-[width];

    &:focus-within {
        @apply border-primary-100 border-2;
    }

    &:hover .search-icon {
        @apply opacity-0;
    }
}

.search-icon {
    @apply h-7 w-7 text-primary-100 p-1;
}

.clear-icon {
    @apply opacity-100 duration-500;
}
.btn-search
{ @apply h-full flex bg-neutral-200 rounded-full cursor-pointer md:hidden dark:bg-neutral-700 items-center justify-center; 
} 
.search-frame
{ @apply hidden absolute top-0 right-0 left-0 h-16 md:relative md:flex justify-end md:h-8 md:w-[32rem]; 
} 
.search-input
{ @apply border-[1px] dark:border-neutral-500 dark:bg-neutral-700 bg-gray-200 focus:bg-white dark:focus:bg-neutral-600 text-black  absolute right-0 h-8 dark:text-white; 
} 
.search-result-panel
{ @apply transition-[width] absolute rounded-xl z-[20] flex mt-16 md:mt-10 flex-col bg-white dark:bg-neutral-800 px-4 py-3 shadow-xl; 
} 
.item-search-skeleton
{ @apply rounded-lg bg-gray-100 dark:bg-neutral-700 my-2 hover:bg-gray-100 animate-pulse; 
} 
.item-search-result
{ @apply rounded-lg bg-gray-100 dark:bg-neutral-700 mb-2 hover:bg-gray-200 dark:hover:bg-neutral-600 dark:text-white; 
} 

