<!-- Sync Progress Component -->
<div class="sync-progress-container" [class.active]="isActive()" [class.completed]="isCompleted()" [class.error]="hasError()">
  <!-- Progress Header -->
  <div class="progress-header">
    <div class="progress-icon" [attr.data-color]="stageColor()">
      <!-- Clock Icon -->
      <svg *ngIf="stageIcon() === 'clock'" class="icon" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10"/>
        <polyline points="12,6 12,12 16,14"/>
      </svg>
      
      <!-- WiFi Icon -->
      <svg *ngIf="stageIcon() === 'wifi'" class="icon" viewBox="0 0 24 24">
        <path d="M5 12.55a11 11 0 0 1 14.08 0"/>
        <path d="M1.42 9a16 16 0 0 1 21.16 0"/>
        <path d="M8.53 16.11a6 6 0 0 1 6.95 0"/>
        <circle cx="12" cy="20" r="1"/>
      </svg>
      
      <!-- Download Icon -->
      <svg *ngIf="stageIcon() === 'download'" class="icon" viewBox="0 0 24 24">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="7,10 12,15 17,10"/>
        <line x1="12" y1="15" x2="12" y2="3"/>
      </svg>
      
      <!-- Compare Icon -->
      <svg *ngIf="stageIcon() === 'compare'" class="icon" viewBox="0 0 24 24">
        <path d="M18 6H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h13l4-3.5L18 6z"/>
        <path d="M6 18H19a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H6l-4 3.5L6 18z"/>
      </svg>
      
      <!-- Sync Icon -->
      <svg *ngIf="stageIcon() === 'sync'" class="icon" viewBox="0 0 24 24">
        <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
        <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
      </svg>
      
      <!-- Check Icon -->
      <svg *ngIf="stageIcon() === 'check'" class="icon" viewBox="0 0 24 24">
        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      
      <!-- Alert Icon -->
      <svg *ngIf="stageIcon() === 'alert'" class="icon" viewBox="0 0 24 24">
        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
        <line x1="12" y1="9" x2="12" y2="13"/>
        <line x1="12" y1="17" x2="12.01" y2="17"/>
      </svg>
    </div>
    
    <div class="progress-info">
      <h3 class="progress-title">{{ progress().message }}</h3>
      <div class="progress-meta">
        <span *ngIf="progress().currentSite" class="current-site">{{ progress().currentSite }}</span>
        <span *ngIf="startTime()" class="elapsed-time">{{ formatElapsedTime(elapsedTime()) }}</span>
      </div>
    </div>
    
    <div class="progress-percentage">
      <span class="percentage-text">{{ progressPercentage() }}%</span>
    </div>
  </div>

  <!-- Progress Bar -->
  <div class="progress-bar-container">
    <div class="progress-bar">
      <div 
        class="progress-fill" 
        [style.width.%]="progressPercentage()"
        [attr.data-color]="stageColor()"
      ></div>
    </div>
  </div>

  <!-- Stage Steps (if showDetails is true) -->
  <div *ngIf="showDetails" class="progress-stages">
    <div class="stages-container">
      <div 
        *ngFor="let stage of stages(); let i = index; trackBy: trackByStageKey"
        class="stage-item"
        [class]="getStageClass(stage.key)"
      >
        <div class="stage-indicator">
          <div class="stage-circle">
            <svg *ngIf="stage.completed" class="stage-icon completed" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span *ngIf="!stage.completed" class="stage-number">{{ i + 1 }}</span>
          </div>
          <div *ngIf="i < stages().length - 1" class="stage-connector"></div>
        </div>
        <div class="stage-label">{{ stage.label }}</div>
      </div>
    </div>
  </div>

  <!-- Current Action Details -->
  <div *ngIf="progress().currentAction && isActive()" class="current-action">
    <div class="action-indicator">
      <div class="action-spinner"></div>
    </div>
    <span class="action-text">{{ progress().currentAction }}</span>
  </div>

  <!-- Action Buttons -->
  <div class="progress-actions" *ngIf="isActive() || hasError()">
    <button 
      *ngIf="isActive()" 
      class="action-button cancel"
      (click)="cancelSync()"
    >
      <svg class="button-icon" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10"/>
        <line x1="15" y1="9" x2="9" y2="15"/>
        <line x1="9" y1="9" x2="15" y2="15"/>
      </svg>
      <span class="button-text">Hủy bỏ</span>
    </button>
    
    <button 
      *ngIf="hasError()" 
      class="action-button retry"
      (click)="retrySync()"
    >
      <svg class="button-icon" viewBox="0 0 24 24">
        <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
      </svg>
      <span class="button-text">Thử lại</span>
    </button>
  </div>
</div>
