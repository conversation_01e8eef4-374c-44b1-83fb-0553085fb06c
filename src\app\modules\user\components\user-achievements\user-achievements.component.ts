import { Component, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-user-achievements',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="user-achievements-container">
      <div class="page-header">
        <h2 class="page-title">
          <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"/>
            <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"/>
            <path d="M4 22h16"/>
            <path d="M10 14.66V17c0 .*********** 1.21C12.04 18.75 14 20 14 20s1.96-1.25 3.03-1.79c.5-.23.97-.66.97-1.21v-2.34"/>
            <path d="M18 2H6v7a6 6 0 0 0 12 0V2z"/>
          </svg>
          Th<PERSON>nh tích
        </h2>
        <p class="page-description"><PERSON><PERSON> các huy hiệu và thành tựu đã đạt được</p>
      </div>

      <div class="content-card">
        <div class="empty-state">
          <div class="empty-illustration">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"/>
              <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"/>
              <path d="M4 22h16"/>
              <path d="M10 14.66V17c0 .*********** 1.21C12.04 18.75 14 20 14 20s1.96-1.25 3.03-1.79c.5-.23.97-.66.97-1.21v-2.34"/>
              <path d="M18 2H6v7a6 6 0 0 0 12 0V2z"/>
            </svg>
          </div>
          <h3 class="empty-title">Chưa có thành tích</h3>
          <p class="empty-description">
            Hãy đọc truyện và tham gia hoạt động để mở khóa các thành tích thú vị!
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .user-achievements-container {
      @apply space-y-6;
    }

    .page-header {
      @apply space-y-2;
    }

    .page-title {
      @apply flex items-center gap-3 text-2xl font-bold text-gray-900 dark:text-white;
    }

    .title-icon {
      @apply w-8 h-8 text-orange-500;
      fill: none;
      stroke: currentColor;
      stroke-width: 2;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .page-description {
      @apply text-gray-600 dark:text-gray-400;
    }

    .content-card {
      @apply bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 shadow-sm p-12;
      backdrop-filter: blur(20px);
      background: rgba(255, 255, 255, 0.95);
      
      .dark & {
        background: rgba(31, 41, 55, 0.95);
      }
    }

    .empty-state {
      @apply text-center space-y-6;
    }

    .empty-illustration {
      @apply flex justify-center;
    }

    .empty-icon {
      @apply w-24 h-24 text-gray-300 dark:text-gray-600;
      fill: none;
      stroke: currentColor;
      stroke-width: 1;
      stroke-linecap: round;
      stroke-linejoin: round;
    }

    .empty-title {
      @apply text-xl font-semibold text-gray-900 dark:text-white;
    }

    .empty-description {
      @apply text-gray-600 dark:text-gray-400 max-w-md mx-auto leading-relaxed;
    }
  `],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserAchievementsComponent {
}
