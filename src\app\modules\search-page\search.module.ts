import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { SearchPageComponent } from './search-page.component';
import { GenreCatagoriesComponent } from '@components/common/genre-catagories/genre-catagories.component';
import { TopListComponent } from '@components/common/top-list/top-list.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { EmptyComponent } from '@components/common/empty/empty.component';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { Selection2Component } from '@components/common/selection-2/selection-2.component';

@NgModule({
    declarations: [SearchPageComponent],
    imports: [
        RouterModule.forChild([{ path: '', component: SearchPageComponent }]),
        CommonModule,
        ReactiveFormsModule,
        FormsModule,
        GenreCatagoriesComponent,
        TopListComponent,
        PaginationComponent,
        SpinnerComponent,
        EmptyComponent,
        BreadcrumbComponent,
        GridComicComponent,
        ClickOutsideDirective,
        Selection2Component
    ],
})
export class SearchModule { }
