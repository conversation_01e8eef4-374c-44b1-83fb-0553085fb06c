// generate-version.ts
const fs = require('fs');
const path = require('path');

// You don't need the 'interface VersionInfo' declaration in a Node.js script
// that's being run as CommonJS. Just define the object directly.

const appPackage = require('./package.json');



// Define the versionInfo object directly
const versionInfo = {
  version: new Date().toISOString(),
};

const filePath = path.join(__dirname, 'public/version.json');
const envFilePath = path.join(__dirname, 'src/app/environment.ts');

fs.writeFileSync(envFilePath, `export const environment = {
  version: '${versionInfo.version}'
};`);

fs.writeFileSync(filePath, JSON.stringify(versionInfo, null, 2));

console.log(`Version file generated at ${filePath}:`);
console.log(JSON.stringify(versionInfo, null, 2));
