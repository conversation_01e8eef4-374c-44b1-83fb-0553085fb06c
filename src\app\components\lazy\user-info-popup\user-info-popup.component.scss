
// Modal Container
.user-info-modal {
  @apply fixed max-[376px]:w-[80%] max-sm:w-[300px] bg-white text-black dark:text-white dark:bg-neutral-800;
  @apply shadow-2xl left-1/2 top-1/2 z-50 w-[400px] block rounded-2xl;
  @apply transform -translate-x-1/2 -translate-y-1/2 overflow-hidden;
  @apply border border-neutral-200 dark:border-neutral-700;
}

// Loading State
.user-info-loading {
  @apply flex items-center justify-center p-8;
}

.loading-spinner {
  @apply flex flex-col items-center gap-4;
}

.loading-icon {
  @apply w-10 h-10 text-primary-100 animate-spin;

  svg {
    @apply w-full h-full;
  }
}

.loading-text {
  @apply text-neutral-600 dark:text-neutral-400 font-medium;
}

// Header
.user-info-header {
  @apply flex justify-between items-center p-4 border-b border-neutral-200 dark:border-neutral-700;
  @apply bg-primary-100 text-white;
}

.user-info-title {
  @apply flex items-center gap-2 font-bold text-lg;
}

.header-icon {
  @apply h-8 w-8 rounded-lg bg-white/20 p-1;
}

.header-text {
  @apply font-bold;
}

.user-info-close {
  @apply w-8 h-8 rounded-full bg-white/20 hover:bg-white/30;
  @apply flex items-center justify-center transition-colors duration-200;
  @apply border-none cursor-pointer;

  svg {
    @apply w-5 h-5;
  }

  &:focus {
    @apply outline-none ring-2 ring-white/50;
  }
}

// Content
.user-info-content {
  @apply flex flex-col sm:flex-row justify-between p-4;
  @apply border-b-2 border-dashed border-neutral-200 dark:border-neutral-700;
}

// Avatar Section
.user-info-avatar {
  @apply w-full m-auto max-sm:mt-2 sm:w-[30%] flex flex-col items-center;
}

.avatar-wrapper {
  @apply relative;
}

.user-avatar-image {
  @apply rounded-xl w-24 h-36 object-cover shadow-lg;
  @apply border-2 border-neutral-200 dark:border-neutral-600;
  @apply transition-transform duration-200 hover:scale-105;
}

.user-info-level {
  @apply absolute -top-2 -right-5 w-12 h-12 rounded-full;
  @apply bg-primary-100 text-white;
  @apply flex items-center justify-center shadow-lg;
  @apply border-2 border-white dark:border-neutral-800;
}

.level-icon {
  @apply w-4 h-4 mr-1;
}

.level-text {
  @apply font-bold text-sm;
}

// Details Section
.user-info-details {
  @apply w-full sm:w-[60%] max-sm:ml-2 flex flex-col items-start mt-4 sm:mt-0 sm:pl-4;
  @apply space-y-3;
}

.detail-row {
  @apply flex justify-center w-full text-sm;

  &.username-row {
    @apply mb-2;
  }
}

.detail-label {
  @apply text-neutral-600 dark:text-neutral-400 min-w-[100px];
}

.detail-value {
  @apply ml-3 font-semibold text-neutral-900 dark:text-white;

  &.experience-value {
    @apply text-sky-600 dark:text-sky-400;
  }

  &.level-value {
    @apply text-primary-100  font-bold;
  }
}

.username-text {
  @apply uppercase font-bold text-xl text-neutral-900 dark:text-white;
  @apply bg-primary-50 bg-clip-text text-transparent;
}

// Motto Section
.motto-section {
  @apply mt-4 pt-3 border-t border-neutral-200 dark:border-neutral-700 w-full;
}

.motto-text {
  @apply text-center font-medium text-sm text-neutral-700 dark:text-neutral-300;
  @apply italic bg-neutral-50 dark:bg-neutral-700/50 p-3 rounded-lg;
  @apply border-l-4 border-red-500;
  @apply line-clamp-3 w-full;
}

// Animations
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

// Responsive adjustments
@screen sm {
  .user-info-modal {
    @apply w-[450px];
  }

  .detail-row {
    @apply justify-start;
  }
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .user-info-modal {
    background: rgba(31, 41, 55, 0.98);
  }

  .username-text {
    @apply text-white;
    background: linear-gradient(to right, #ef4444, #dc2626);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}