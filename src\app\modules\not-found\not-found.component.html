<!-- 404 Not Found Page - Modern Comic-themed Design -->
<div class="not-found-container">

  <!-- Main Content -->
  <div class="main-content">
    <!-- 404 Hero Section -->
    <div class="hero-section">
      <!-- Animated 404 Number -->
      <div class="error-number-container">
        <div class="error-number">
          <span class="digit digit-4-1">4</span>
          <span class="digit digit-0">0</span>
          <span class="digit digit-4-2">4</span>
        </div>
        <div class="error-glow"></div>
      </div>

      <!-- Comic Character Illustration -->
      <div class="character-illustration">
        <div class="character-container">
          <!-- Simple SVG Comic Character -->
          <svg class="character-svg" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
            <!-- Character Body -->
            <circle cx="100" cy="120" r="60" fill="#F86E4C" opacity="0.1"/>
            <!-- Character Head -->
            <circle cx="100" cy="80" r="35" fill="#F86E4C" opacity="0.8"/>
            <!-- Eyes -->
            <circle cx="90" cy="75" r="3" fill="#fff"/>
            <circle cx="110" cy="75" r="3" fill="#fff"/>
            <!-- Mouth (sad) -->
            <path d="M85 90 Q100 85 115 90" stroke="#fff" stroke-width="2" fill="none"/>
            <!-- Speech Bubble -->
            <ellipse cx="150" cy="50" rx="30" ry="20" fill="#fff" stroke="#F86E4C" stroke-width="2"/>
            <path d="M130 60 L125 70 L140 65 Z" fill="#fff"/>
            <text x="150" y="55" text-anchor="middle" font-size="12" fill="#F86E4C">Oops!</text>
          </svg>
        </div>
      </div>

      <!-- Error Message -->
      <div class="error-message">
        <h1 class="error-title">
          <span class="title-main">Trang Không Tồn Tại</span>
        </h1>
        <p class="error-description">
          Trang bạn đang tìm kiếm không tồn tại. Vui lòng kiểm tra lại đường dẫn hoặc quay lại trang chủ.
        </p>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-section">
      <div class="primary-actions">
        <a routerLink="/" class="btn btn-primary">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
          </svg>
          Về trang chủ
        </a>
        <button (click)="goBack()" class="btn btn-secondary">
          <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
          Quay lại
        </button>
      </div>
    </div>

  
  </div>
</div>
