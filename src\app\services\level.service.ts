import { Injectable } from "@angular/core";
import { Level } from "@schema";

interface LevelInfo {
    exp: number;
    level: string;
}

@Injectable({
    providedIn: 'root',
})
export class LevelService {


    private levels: LevelInfo[][] = [
        this.generateLevels(true),
        this.generateLevels(false)
    ];

    private generateLevels(useDefaultNames: boolean): LevelInfo[] {
        return [
            { exp: Level.LuyenKhiKy, level: useDefaultNames ? "Luyện Khí K<PERSON>" : "Cấp 1" },
            { exp: Level.TruCoKy, level: useDefaultNames ? "Trúc <PERSON>ơ <PERSON>ỳ" : "Cấp 2" },
            { exp: Level.KetDanKy, level: useDefaultNames ? "Kết Đ<PERSON>" : "Cấp 3" },
            { exp: Level.NguyenAnhKy, level: useDefaultNames ? "Nguyên <PERSON>" : "Cấp 4" },
            { exp: Level.HoaThan<PERSON>y, level: useDefaultNames ? "<PERSON><PERSON><PERSON><PERSON>" : "Cấp 5" },
            { exp: Level.Luyen<PERSON>, level: useDefaultNames ? "Luyệ<PERSON>" : "Cấp 6" },
            { exp: Level.HopTheky, level: useDefaultNames ? "Hợp Thể Kỳ" : "Cấp 7" },
            { exp: Level.DaiThuaKy, level: useDefaultNames ? "Đại Thừa Kỳ" : "Cấp 8" },
            { exp: Level.ChanTien, level: useDefaultNames ? "Chân Tiên" : "Cấp 9" },
            { exp: Level.KiemTien, level: useDefaultNames ? "Kiếm Tiên" : "Cấp 10" },
            { exp: Level.ThaiAtKiemTien, level: useDefaultNames ? "Thái Ất Kiếm Tiên" : "Cấp 11" },
            { exp: Level.DaiLa, level: useDefaultNames ? "Đại La" : "Bán Đặc Cấp" },
            { exp: Level.DaoTo, level: useDefaultNames ? "Đạo Tổ" : "Đặc Cấp" },
        ];
    }

    public getLevel(exp: number, typeLevel: number): string {
        const levels = this.levels[typeLevel];
        if (exp >= Level.DaoTo) {
            return levels[levels.length - 1].level;
        }

        for (let i = levels.length - 1; i >= 0; i--) {
            if (exp >= levels[i].exp) {
                return levels[i].level;
            }
        }

        return levels[0].level;
    }

    public getLevelUser(exp: number, typeLevel: number): { percent: number, level: string, nextLevel: string } {
        const levels = this.levels[typeLevel];
        if (exp >= Level.DaoTo) {
            return { percent: Math.round((exp / Level.DaoTo)*100), level: levels[levels.length - 1].level, nextLevel: 'Max' };
        }

        for (let i = levels.length - 1; i >= 0; i--) {
            if (exp >= levels[i].exp) {
                return { percent: Math.round((exp / levels[i + 1].exp)*100), level: levels[i].level, nextLevel: levels[i + 1].level };
            }
        }

        return { percent: Math.round((exp / Level.TruCoKy)*100), level: levels[0].level, nextLevel: levels[1].level };
    }
}