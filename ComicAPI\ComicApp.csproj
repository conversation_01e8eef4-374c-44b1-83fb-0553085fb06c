<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.16" />
    <PackageReference Include="Microsoft.AspNetCore.ResponseCompression" Version="2.3.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.11.0" />
    <PackageReference Include="Microsoft.AspNetCore.WebUtilities" Version="8.0.16" />
  </ItemGroup>

</Project>
