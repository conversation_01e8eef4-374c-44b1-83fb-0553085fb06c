using ComicApp.Models.SyncTracking;
using ComicAPI.DTOs.SyncTracking;

namespace ComicAPI.Services.SyncTracking
{
    public interface ISyncTrackingService
    {
        /// <summary>
        /// Start sync process and return session ID
        /// </summary>
        Task<string> StartSyncAsync(SyncCredentialsDTO credentials);

        /// <summary>
        /// Get sync progress for a specific session
        /// </summary>
        Task<SyncProgress?> GetSyncProgressAsync(string sessionId);


        /// <summary>
        /// Cancel sync process
        /// </summary>
        Task<bool> CancelSyncAsync(string sessionId);

        /// <summary>
        /// Clean up completed sessions
        /// </summary>
        Task CleanupSessionsAsync();
    }
}
