<ng-container *ngIf="isVisible">
  <div class="rating-overlay">
    <div class="rating-modal">
      <!-- Header -->
      <div class="rating-header">
        <h3 class="rating-title"><PERSON><PERSON><PERSON> g<PERSON><PERSON> tru<PERSON>n</h3>
        <p class="rating-subtitle"><PERSON><PERSON><PERSON> cảm thấy truyện này như thế nào?</p>
        <button class="rating-close" (click)="cancelVote()">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <!-- Rating Content -->
      <div class="rating-content">
        <!-- Star Rating -->
        <div class="star-rating-container">
          <div class="star-rating-grid">
            <ng-container *ngFor="let star of stars; let i = index">
              <button
                class="star-button"
                [class.star-active]="i < tempRating"
                [class.star-hover]="i < tempRating"
                (click)="tempRating = i + 1"
                (mouseenter)="rateStar(i + 1)"
                [attr.aria-label]="'Đánh giá ' + (i + 1) + ' sao'"
              >
                <svg class="star-icon" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                <div class="star-glow"></div>
              </button>
            </ng-container>
          </div>

          <!-- Rating Text -->
          <div class="rating-text">
            <span class="rating-score">{{ tempRating }}/5</span>
            <span class="rating-label">{{ getRatingLabel(tempRating) }}</span>
          </div>
        </div>

        <!-- Rating Description -->
        <div class="rating-description">
          <p>{{ getRatingDescription(tempRating) }}</p>
        </div>
      </div>

      <!-- Actions -->
      <div class="rating-actions">
        <button class="action-button action-button--secondary" (click)="cancelVote()">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
          Hủy bỏ
        </button>
        <button class="action-button action-button--primary" (click)="confirmVote()">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polyline points="20,6 9,17 4,12"/>
          </svg>
          Gửi đánh giá
        </button>
      </div>
    </div>
  </div>
</ng-container>
