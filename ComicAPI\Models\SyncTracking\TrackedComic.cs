using System.ComponentModel.DataAnnotations;

namespace ComicApp.Models.SyncTracking
{
    public class TrackedComic
    {
        [Required]
        public string Id { get; set; } = string.Empty;

        [Required]
        public string Title { get; set; } = string.Empty;
        [Required]
        public string Slug { get; set; } = string.Empty;

        [Required]
        public string Url { get; set; } = string.Empty;

        public int TotalChapters { get; set; } = 0;

        [Required]
        public ComicSource Source { get; set; }
    }

    public enum ComicSource
    {
        NetTruyen,
        TruyenQQ
    }
}
