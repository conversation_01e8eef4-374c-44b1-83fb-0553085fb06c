using System.Collections.Concurrent;
using System.Text;
using System.Text.Json;
using ComicApp.Models.SyncTracking;
using ComicAPI.DTOs.SyncTracking;
using ComicAPI.Reposibility;
using ComicAPI.Models;

namespace ComicAPI.Services.SyncTracking
{
    public class SyncTrackingService : ISyncTrackingService
    {
        private readonly ILogger<SyncTrackingService> _logger;
        private readonly ConcurrentDictionary<string, SyncProgress> _activeSessions;
        // private readonly List<SyncResult> _syncHistory;
        private readonly IServiceProvider _serviceProvider;

        public SyncTrackingService(IServiceProvider serviceProvider, ILogger<SyncTrackingService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _activeSessions = new ConcurrentDictionary<string, SyncProgress>();
        }

        public async Task<string> StartSyncAsync(SyncCredentialsDTO credentials)
        {

            await Task.CompletedTask; // Make it async for consistency

            var sessionId = Guid.NewGuid().ToString();
            _logger.LogInformation($"Starting sync process with sessionId: {sessionId}");

            var progress = new SyncProgress
            {
                SessionId = sessionId,
                Stage = SyncStage.Connecting,
                Progress = 0,
                Message = "Initializing sync process...",
                StartTime = DateTime.UtcNow,
                Errors = new List<string>()
            };

            _activeSessions.TryAdd(sessionId, progress);

            // Start sync process in background
            _ = Task.Run(async () =>
            {
                try
                {
                    await PerformSyncAsync(sessionId, credentials);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Sync process error");
                    UpdateProgress(sessionId, new SyncProgress
                    {
                        Stage = SyncStage.Error,
                        Progress = 0,
                        Message = $"Sync failed: {ex.Message}"
                    });
                }
                finally
                {
                    await Task.Delay(2000);
                    _activeSessions.TryRemove(sessionId, out _);
                }
            });

            return sessionId;
        }

        private async Task PerformSyncAsync(string sessionId, SyncCredentialsDTO credentials)
        {
            using var scope = _serviceProvider.CreateScope();
            var _crawlerService = scope.ServiceProvider.GetRequiredService<ICrawlerService>();
            var _comicReposibility = scope.ServiceProvider.GetRequiredService<IComicReposibility>();
            var _userReposibility = scope.ServiceProvider.GetRequiredService<IUserReposibility>();

            var startTime = DateTime.UtcNow;
            var sourceComics = new List<TrackedComic>();
            var syncedCount = 0;
            var skippedCount = 0;
            var errorCount = 0;

            try
            {
                // Stage 1: Connecting and testing credentials
                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Connecting,
                    Progress = 10,
                    Message = $"Kiểm tra kết nối đến {credentials.sourceFrom}..."
                });
                var source = credentials.sourceFrom.ToLower() == "nettruyen" ? ComicSource.NetTruyen : ComicSource.TruyenQQ;
                var isValidCredentials = await _crawlerService.TestLoginAsync(source, credentials);

                if (!isValidCredentials)
                {
                    throw new Exception($"Không thể xác thực tài khoản {credentials.sourceFrom}");
                }

                // Stage 2: Fetching comics from source
                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Fetching,
                    Progress = 25,
                    Message = $"Đang lấy danh sách truyện từ {credentials.sourceFrom}..."
                });
                sourceComics = await _crawlerService.FetchComicsAsync(source,credentials);
                await Task.Delay(1000);

                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Fetching,
                    Progress = 50,
                    Message = $"Đã tìm thấy {sourceComics.Count} truyện từ {credentials.sourceFrom}",
                    TotalComics = sourceComics.Count
                });

                // Stage 3: Comparing and preparing sync
                await Task.Delay(1000);
                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Comparing,
                    Progress = 60,
                    Message = "Đang phân tích truyện..."
                });


                // Stage 4: Syncing comics to local database
                await Task.Delay(1000);
                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Syncing,
                    Progress = 75,
                    Message = "Đang đồng bộ truyện vào thư viện..."
                });

                await Task.Delay(1000);

                for (int i = 0; i < sourceComics.Count; i++)
                {
                    var rawComic = sourceComics[i];

                    try
                    {
                        Comic? dbComic = await _comicReposibility.GetComicBySlug(rawComic.Slug);
                        // var syncSuccess = await _crawlerService.SyncComicToLocalAsync(comic);

                        if (dbComic != null)
                        {
                            await _userReposibility.FollowComic(credentials.UserId,dbComic.ID);
                            syncedCount++;
                        }
                        else
                        {
                            skippedCount++;
                        }

                        // Update progress
                        var progressPercent = 75 + (int)Math.Round((double)(i + 1) / sourceComics.Count * 25);
                        UpdateProgress(sessionId, new SyncProgress
                        {
                            Stage = SyncStage.Syncing,
                            Progress = progressPercent,
                            Message = $"Đồng bộ truyện {i + 1}/{sourceComics.Count}: {rawComic.Title}",
                            ProcessedComics = i + 1
                        });

                        // Small delay to prevent overwhelming the system
                        await Task.Delay(100);
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        UpdateProgress(sessionId, new SyncProgress
                        {
                            Stage = SyncStage.Syncing,
                            Progress = 75,
                            Message = $"Đồng bộ truyện {i + 1}/{sourceComics.Count}: {rawComic.Title}",
                            ProcessedComics = i + 1,
                            Errors = new List<string> { ex.Message }
                        });
                    }
                }

                // Stage 5: Completed
                await Task.Delay(1000);

                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Completed,
                    Progress = 100,
                    Message = $"Đông bộ truyện trành công {syncedCount} truyện.",
                    EndTime = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                var duration = (int)(DateTime.UtcNow - startTime).TotalSeconds;

                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Error,
                    Progress = 0,
                    Message = $"Sync failed: {ex.Message}",
                    EndTime = DateTime.UtcNow
                });
                await Task.Delay(2000);
               
            }
        }

        private void UpdateProgress(string sessionId, SyncProgress updates)
        {
            if (_activeSessions.TryGetValue(sessionId, out var current))
            {
                var updated = new SyncProgress
                {
                    SessionId = current.SessionId,
                    Stage = updates.Stage != SyncStage.Idle ? updates.Stage : current.Stage,
                    Progress = updates.Progress > 0 ? updates.Progress : current.Progress,
                    Message = !string.IsNullOrEmpty(updates.Message) ? updates.Message : current.Message,
                    StartTime = current.StartTime,
                    EndTime = updates.EndTime ?? current.EndTime,
                    TotalComics = updates.TotalComics ?? current.TotalComics,
                    ProcessedComics = updates.ProcessedComics ?? current.ProcessedComics,
                    Errors = current.Errors
                };

                _activeSessions.TryUpdate(sessionId, updated, current);
            }
        }

        public async Task<SyncProgress?> GetSyncProgressAsync(string sessionId)
        {
            await Task.CompletedTask; // Make it async for consistency
            return _activeSessions.TryGetValue(sessionId, out var progress) ? progress : null;
        }

        


        public async Task<bool> CancelSyncAsync(string sessionId)
        {
            await Task.CompletedTask; // Make it async for consistency

            if (_activeSessions.TryGetValue(sessionId, out var progress))
            {
                UpdateProgress(sessionId, new SyncProgress
                {
                    Stage = SyncStage.Error,
                    Progress = 0,
                    Message = "Sync cancelled by user",
                    EndTime = DateTime.UtcNow
                });

                return true;
            }

            return false;
        }

        public async Task CleanupSessionsAsync()
        {
            await Task.CompletedTask; // Make it async for consistency

            var now = DateTime.UtcNow;
            var oneHour = TimeSpan.FromHours(1);

            var expiredSessions = _activeSessions
                .Where(kvp => kvp.Value.EndTime.HasValue && (now - kvp.Value.EndTime.Value) > oneHour)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var sessionId in expiredSessions)
            {
                _activeSessions.TryRemove(sessionId, out _);
            }

            _logger.LogInformation($"Cleaned up {expiredSessions.Count} expired sessions");
        }
    }
}
