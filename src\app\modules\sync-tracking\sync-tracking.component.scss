// ===== SYNC TRACKING PAGE =====
// Main page layout with beautiful design

// ===== PAGE LAYOUT =====
.sync-tracking-page {
  @apply min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 
         dark:from-neutral-900 dark:via-neutral-800 dark:to-neutral-900;
}

// ===== PAGE HEADER =====
.page-header {
  @apply bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm 
         border-b border-gray-200 dark:border-neutral-700 
         sticky top-0 z-40;
}

.header-container {
  @apply max-w-7xl mx-auto px-6 py-6 space-y-6;
}

// ===== BREADCRUMB =====
.breadcrumb {
  @apply flex items-center gap-2 text-sm;
}

.breadcrumb-link {
  @apply text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 
         transition-colors duration-200;
}

.breadcrumb-separator {
  @apply w-4 h-4 text-gray-400 dark:text-gray-500;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.breadcrumb-current {
  @apply text-gray-600 dark:text-gray-400 font-medium;
}

// ===== PAGE TITLE =====
.page-title-section {
  @apply text-center space-y-3;
}

.page-title {
  @apply text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 
         bg-clip-text text-transparent;
}

.page-subtitle {
  @apply text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto;
}

// ===== STEP INDICATOR =====
.step-indicator {
  @apply flex justify-center;
}

.steps-container {
  @apply flex items-center space-x-8;
}

.step-item {
  @apply flex items-center;
  
  &:last-child .step-connector {
    @apply hidden;
  }
}

.step-button {
  @apply flex flex-col items-center space-y-2 p-2 rounded-lg 
         transition-all duration-200 disabled:cursor-not-allowed;
}

.step-circle {
  @apply w-12 h-12 rounded-full border-2 flex items-center justify-center 
         transition-all duration-300;

  .step-item.pending & {
    @apply border-gray-300 dark:border-neutral-600 bg-white dark:bg-neutral-800;
  }

  .step-item.active & {
    @apply border-blue-500 bg-blue-50 dark:bg-blue-900/30;
  }

  .step-item.completed & {
    @apply border-green-500 bg-green-500;
  }
}

.step-icon {
  @apply w-6 h-6 text-white;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.step-number {
  @apply text-sm font-semibold;

  .step-item.pending & {
    @apply text-gray-500 dark:text-gray-400;
  }

  .step-item.active & {
    @apply text-blue-600 dark:text-blue-400;
  }
}

.step-spinner {
  @apply w-6 h-6 border-2 border-blue-200 border-t-blue-600 rounded-full;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.step-label {
  @apply text-sm font-medium transition-all duration-300;

  .step-item.pending & {
    @apply text-gray-500 dark:text-gray-400;
  }

  .step-item.active & {
    @apply text-blue-600 dark:text-blue-400;
  }

  .step-item.completed & {
    @apply text-green-600 dark:text-green-400;
  }
}

.step-connector {
  @apply w-16 h-0.5 ml-4 transition-all duration-300;

  .step-item.completed & {
    @apply bg-green-500;
  }

  .step-item.active & {
    @apply bg-blue-500;
  }

  .step-item.pending & {
    @apply bg-gray-300 dark:bg-neutral-600;
  }
}

// ===== MAIN CONTENT =====
.page-content {
  @apply py-4;
}

.content-container {
  @apply max-w-7xl mx-auto px-6;
}

.step-content {
  @apply transition-all duration-500 ease-in-out;
}

.form-step {
  @apply animate-fade-in;
}

.progress-step {
  @apply animate-slide-up;
}

.results-step {
  @apply animate-slide-up;
}

// ===== ANIMATIONS =====
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

// ===== PAGE footer-sync =====
.page-footer-sync {
  @apply bg-white dark:bg-neutral-800 
         border-t border-gray-200 dark:border-neutral-700 
         mt-16;
}

.footer-sync-container {
  @apply max-w-7xl mx-auto px-6 py-12;
}

.footer-sync-content {
  @apply grid grid-cols-1 md:grid-cols-3 gap-8 mb-8;
}

.footer-sync-section {
  @apply space-y-4;
}

.footer-sync-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.footer-sync-list {
  @apply space-y-2;
}

.footer-sync-item {
  @apply text-sm text-gray-600 dark:text-gray-400 
         flex items-start gap-2;

  &::before {
    content: '•';
    @apply text-blue-500 font-bold mt-0.5 flex-shrink-0;
  }
}

.footer-sync-bottom {
  @apply pt-8 border-t border-gray-200 dark:border-neutral-700 
         text-center;
}

.footer-sync-copyright {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .header-container {
    @apply px-4 py-4 space-y-4;
  }

  .page-title {
    @apply text-2xl;
  }

  .page-subtitle {
    @apply text-base;
  }

  .steps-container {
    @apply space-x-4;
  }

  .step-circle {
    @apply w-10 h-10;
  }

  .step-connector {
    @apply w-8;
  }

  .content-container {
    @apply px-4;
  }

  .footer-sync-container {
    @apply px-4 py-8;
  }

  .footer-sync-content {
    @apply grid-cols-1 gap-6;
  }
}

@media (max-width: 480px) {
  .steps-container {
    @apply flex-col space-x-0 space-y-4;
  }

  .step-item {
    @apply flex-col;
  }

  .step-connector {
    @apply hidden;
  }
}
