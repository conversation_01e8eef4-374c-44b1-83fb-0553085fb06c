import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Inject, <PERSON>Zone, OnDestroy, OnInit, PLATFORM_ID } from '@angular/core';
import { RouterLink } from '@angular/router';

import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { INotification, IServiceResponse } from '@schema';
import { AccountService } from '@services/account.service';
import { interval } from 'rxjs';
import { DateAgoPipe } from "../../../pines/date-ago.pine";
import { OptimizedBaseComponent } from '../base/optimized-base.component';
import { EmptyComponent } from '../empty/empty.component';

const filters = [(item: any) => item, (item: any) => !item.isRead];

@Component({
  selector: 'app-notify',
  templateUrl: './notify.component.html',
  styleUrl: './notify.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, RouterLink, ClickOutsideDirective, EmptyComponent, DateAgoPipe],
})
export class NotifyComponent extends OptimizedBaseComponent implements OnInit, OnDestroy {
  // Component state
  listnotifyData: INotification[] = [];
  lengthnotifyReaded = 0;
  optionNotify = 0;
  isShowNotify = false;
  isShowOptionGeneral = false;
  isShowOption: number | null = null;
  hoveredIndexNotify: number | null = null;
  listnotify: INotification[] = [];
  lengthnotifyData = 0;
  isAuthenticated = false;

  // Performance optimizations
  private readonly FETCH_INTERVAL = 60000;
  private debouncedToggleOption!: Function;

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object,
    private elementRef: ElementRef,
    private accountService: AccountService,
    private ngZone: NgZone,
  ) {
    super(cdr, platformId);
    this.setupDebouncedMethods();
  }

  // Computed properties for better performance
  get hasNotifications(): boolean {
    return this.listnotifyData.length > 0;
  }

  get hasUnreadNotifications(): boolean {
    return this.lengthnotifyReaded > 0;
  }

  get shouldShowEmptyState(): boolean {
    return (this.lengthnotifyData <= 0 && this.optionNotify === 0) ||
      (this.lengthnotifyReaded <= 0 && this.optionNotify === 1);
  }

  get shouldShowNotificationList(): boolean {
    return !this.shouldShowEmptyState;
  }

  get notificationBadgeClass(): string {
    return this.hasUnreadNotifications ? 'notify-badge-visible' : 'notify-badge-hidden';
  }


  // TrackBy functions for ngFor optimization
  trackByNotificationId = (index: number, notification: INotification): number => {
    return notification.id;
  };

  private setupDebouncedMethods(): void {
    this.debouncedToggleOption = this.debounce((value: number) => {
      this.performToggleOption(value);
    }, 150);
  }

  ngOnInit(): void {
    this.ngZone.runOutsideAngular(() => {
      this.runInBrowser(() => {
        this.accountService.GetLocalUser().subscribe((user) => {
          this.isAuthenticated = !!user;
          if (user) {
            this.fetchListNotify();
            this.setupPeriodicFetch();
          }
          else {
            this.subscriptions.forEach(sub => {
              if (!sub.closed) {
                sub.unsubscribe();
              }
            });
            this.subscriptions.clear();
          }
        });
      });

      
    });
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  private setupPeriodicFetch(): void {

    this.addSubscription(
      interval(this.FETCH_INTERVAL).subscribe(() => this.fetchListNotify())
    );

  }

  fetchListNotify(): void {
    this.addSubscription(
      this.accountService.getUserNotify().subscribe((res: IServiceResponse<INotification[]>) => {
        this.listnotifyData = res.data?.map((e: INotification) => ({
          ...e,
          ...JSON.parse(e.params || '{}'),
        })) || [];

        this.listnotifyData.forEach((e: any) => {
          if (e.type === 0) {
            e.content = `<b>${e.comic_title || 'Truyện'}</b> đã ra chapter mới.`;
          }
          // Add more notification types as needed
        });

        this.lengthnotifyData = this.listnotifyData.length;
        this.updateListNotify();
      })
    );
  }

  formatTimeNotify(time: any): string {
    const timenotify = new Date(time).getTime();
    const timenow = new Date().getTime();
    const diffInMinutes = (timenow - timenotify) / 1000 / 60;

    if (diffInMinutes < 1) return 'Mới cập nhật';
    if (diffInMinutes <= 60) return Math.floor(diffInMinutes) + ' phút trước';
    if (diffInMinutes <= 1440)
      return Math.floor(diffInMinutes / 60) + ' giờ trước';
    if (diffInMinutes <= 10080)
      return Math.floor(diffInMinutes / 1440) + ' ngày trước';
    return Math.floor(diffInMinutes / 10080) + ' tuần trước';
  }

  toggleOptionNotify(value: number): void {
    this.debouncedToggleOption(value);
  }
  private performToggleOption(value: number): void {
    this.optionNotify = value;
    this.updateListNotify();
  }

  onDeleteNotify(idNotify: any): void {
    this.isShowOption = null;
    if (this.listnotifyData.length === 0) return;

    this.addSubscription(
      this.accountService.deleteUserNotify(idNotify).subscribe(() => {
        if (idNotify === -1) {
          this.listnotifyData = [];
        } else {
          this.listnotifyData = this.listnotifyData.filter(
            (notifi) => notifi.id !== idNotify,
          );
        }
        this.updateListNotify();
      })
    );
  }

  onReadNotify(
    idNotify: any,
    option: string | null = null,
    isRead = false,
  ): void {
    this.isShowOption = null;
    if (
      (option === 'read' && isRead) ||
      (option === 'all' && !this.listnotifyData.some((e) => !e.isRead))
    )
      return;

    if (option === 'all') {
      this.addSubscription(
        this.accountService.updateUserNotify(idNotify, null).subscribe(() => {
          this.listnotifyData.forEach((e) => (e.isRead = true));
          this.updateListNotify();
        })
      );
    } else {
      const updateIsRead = option === 'optionNotify' ? !isRead : true;
      this.addSubscription(
        this.accountService
          .updateUserNotify(idNotify, updateIsRead)
          .subscribe(() => {
            const notify = this.listnotifyData.find((e) => e.id === idNotify);
            if (notify) notify.isRead = updateIsRead;
            this.updateListNotify();
          })
      );
    }
  }

  private updateListNotify(): void {
    this.listnotify = this.listnotifyData.filter(filters[this.optionNotify]);
    this.lengthnotifyReaded = this.listnotifyData.filter(filters[1]).length;
    this.lengthnotifyData = this.listnotifyData.length;
    this.safeMarkForCheck();
  }

  onClickNotifyBell(): void {
    this.isShowNotify = !this.isShowNotify;
    this.isShowOptionGeneral = false;
    this.isShowOption = null;
    this.safeMarkForCheck();
  }

  onClickNotifyBellOutside(): void {
    this.isShowNotify = false;
    this.isShowOptionGeneral = false;
    this.isShowOption = null;
    this.safeMarkForCheck();
  }

  onClickOption(i: number): void {
    this.isShowOption = this.isShowOption === i ? null : i;
    this.safeMarkForCheck();
  }

  onToggleOptionGeneral(): void {
    this.isShowOptionGeneral = !this.isShowOptionGeneral;
    this.safeMarkForCheck();
  }

  onMouseEnterNotification(index: number): void {
    this.hoveredIndexNotify = index;
  }

  onMouseLeaveNotifications(): void {
    this.hoveredIndexNotify = null;
  }

  getNotificationLink(notification: any): string {
    return notification.link || '/';
  }
}
