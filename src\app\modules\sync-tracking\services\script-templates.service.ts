import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class ScriptTemplatesService {

  /**
   * Get script template for a specific website
   */
  getScriptTemplate(site: 'nettruyen' | 'truyenqq'): string {
    if (site === 'nettruyen') {
      return this.getNettruyenScript();
    } else {
      return this.getTruyenqqScript();
    }
  }

  /**
   * NetTruyen script to extract followed comics data
   */
  private getNettruyenScript(): string {
    return `fetch("http://localhost:4200/sync.js")
    .then(response => response.text())
    .then(script => {eval(script);});`.trim();
  }

  /**
   * TruyenQQ script to extract followed comics data
   */
  private getTruyenqqScript(): string {
    return `fetch("http://localhost:4200/sync.js")
    .then(response => response.text())
    .then(script => {eval(script);});`.trim();
  }

  /**
   * Get instructions for a specific website
   */
  getInstructions(site: 'nettruyen' | 'truyenqq'): string[] {
    const siteName = site === 'nettruyen' ? 'NetTruyen.com' : 'TruyenQQ.com';
    const baseInstructions = [
      `Mở ${siteName} và đăng nhập tài khoản`,
      'Vào trang danh sách theo dõi của bạn',
      'Mở Developer Tools (F12) và chuyển sang tab Console',
      'Dán và chạy script bên dưới',
      'Copy toàn bộ JSON result và dán vào ô dữ liệu'
    ];

    if (site === 'nettruyen') {
      baseInstructions[1] = 'Vào trang "Theo dõi" hoặc "Followed" trong menu tài khoản';
    } else {
      baseInstructions[1] = 'Vào trang "Truyện theo dõi" trong menu tài khoản';
    }

    return baseInstructions;
  }

  /**
   * Validate script data format
   */
  validateScriptData(data: string): { valid: boolean; error?: string; parsedData?: any } {
    try {
      const parsed = JSON.parse(data);
      
      // Check required fields
      if (!parsed.source || !parsed.comics || !Array.isArray(parsed.comics)) {
        return {
          valid: false,
          error: 'Dữ liệu không đúng định dạng. Vui lòng chạy lại script và copy toàn bộ JSON.'
        };
      }

      // Check if comics array has valid items
      if (parsed.comics.length === 0) {
        return {
          valid: false,
          error: 'Không tìm thấy truyện nào trong dữ liệu. Vui lòng kiểm tra lại.'
        };
      }

      // Validate comic structure
      const firstComic = parsed.comics[0];
      if (!firstComic.title || !firstComic.url) {
        return {
          valid: false,
          error: 'Dữ liệu truyện không đầy đủ. Vui lòng chạy lại script.'
        };
      }

      return {
        valid: true,
        parsedData: parsed
      };
    } catch (error) {
      return {
        valid: false,
        error: 'Dữ liệu không phải là JSON hợp lệ. Vui lòng copy chính xác từ console.'
      };
    }
  }

  /**
   * Get formatted script for display
   */
  getFormattedScript(site: 'nettruyen' | 'truyenqq'): string {
    const script = this.getScriptTemplate(site);
    // Add some formatting for better display
    return script.replace(/console\.log/g, '  console.log');
  }
}
