
.toast-container {
  @apply fixed top-4 right-4 z-[1055] space-y-3;
  @apply w-80 pointer-events-none;
}

// Toast Base
.toast {
  @apply relative overflow-hidden rounded-xl shadow-2xl;
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700;
  @apply pointer-events-auto;
}

// Toast Types
.toast--success {
  @apply border-l-4 border-l-green-500;

  .toast-progress-bar {
    @apply bg-green-500;
  }
}

.toast--error {
  @apply border-l-4 border-l-red-500;

  .toast-progress-bar {
    @apply bg-red-500;
  }
}

.toast--warning {
  @apply border-l-4 border-l-yellow-500;

  .toast-progress-bar {
    @apply bg-yellow-500;
  }
}

.toast--info {
  @apply border-l-4 border-l-blue-500;

  .toast-progress-bar {
    @apply bg-blue-500;
  }
}

// Progress Bar
.toast-progress {
  @apply absolute top-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700;
  @apply overflow-hidden;
}

.toast-progress-bar {
  @apply h-full w-full origin-left;
  animation: toastProgress var(--duration, 3s) linear forwards;
}

// Main Content
.toast-main {
  @apply flex items-start gap-3 p-4;
}

// Icon
.toast-icon {
  @apply flex-shrink-0 w-6 h-6 rounded-full p-1;
  @apply flex items-center justify-center;

  svg {
    @apply w-4 h-4;
  }

  &--success {
    @apply bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400;
  }

  &--error {
    @apply bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400;
  }

  &--warning {
    @apply bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400;
  }

  &--info {
    @apply bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400;
  }
}

// Content
.toast-content {
  @apply flex-1 min-w-0;
}

.toast-title {
  @apply font-semibold text-gray-900 dark:text-white text-sm mb-1;
}

.toast-message {
  @apply text-gray-700 dark:text-gray-300 text-sm leading-relaxed;
  @apply break-words;
}

// Action Button
.toast-action {
  @apply mt-2 px-3 py-1 text-xs font-medium rounded-md;
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-200 dark:hover:bg-gray-600;
  @apply cursor-pointer;

}

// Dismiss Button
.toast-dismiss {
  @apply flex-shrink-0 w-6 h-6 rounded-full;
  @apply flex items-center justify-center;
  @apply text-gray-400 dark:text-gray-500;
  @apply  cursor-pointer;

  svg {
    @apply w-4 h-4;
  }

  &:focus {
    @apply outline-none ring-2 ring-gray-500/50;
  }
}

// Animations
@keyframes toastProgress {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

// 
