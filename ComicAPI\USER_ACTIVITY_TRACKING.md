# 📊 User Activity Tracking System

## 🎯 Overview
Hệ thống tracking hoạt động user đ<PERSON><PERSON><PERSON> thiết kế để theo dõi và phân tích hành vi người dùng một cách hiệu quả với performance cao và xử lý bất đồng bộ.

## 🏗️ Architecture

### Components
1. **UserActivityService** - Core service xử lý tracking
2. **UserActivityBackgroundService** - Background service cập nhật database mỗi 10 phút
3. **UserMiddleware** - Middleware tự động track mọi request
4. **UserActivity Model** - Entity lưu trữ chi tiết hoạt động
5. **User Model** - Thêm fields LastActivity, LastLoginIp

### Data Flow
```
User Request → UserMiddleware → UserActivityService → Queue → Background Service → Database
```

## 📋 Features

### ✅ Implemented Features
- **Automatic Activity Tracking** - Tự động track mọi API request
- **Login Tracking** - Đặc biệt track login events
- **Background Processing** - Xử lý bất đồng bộ với task queue
- **Performance Optimized** - In-memory cache + batch processing
- **IP Address Tracking** - Support proxy/load balancer headers
- **Session Management** - Group activities by session
- **Cleanup System** - Tự động xóa data cũ (30 ngày)

### 🎯 Activity Types
- `login` - User đăng nhập
- `page_view` - Truy cập trang/API
- `comic_view` - Xem comic
- `chapter_read` - Đọc chapter
- `search` - Tìm kiếm
- `comment` - Bình luận
- `vote` - Vote comic
- `follow` - Follow comic

## 🔧 Configuration

### Database Schema
```sql
-- Users table additions
ALTER TABLE users 
ADD COLUMN lastactivity TIMESTAMP NULL,
ADD COLUMN lastloginip VARCHAR(50) NULL;

-- User Activity table
CREATE TABLE user_activity (
    id BIGSERIAL PRIMARY KEY,
    userid INTEGER NOT NULL,
    activitytype VARCHAR(50) NOT NULL DEFAULT 'page_view',
    endpoint VARCHAR(255) NULL,
    ipaddress VARCHAR(50) NULL,
    useragent VARCHAR(500) NULL,
    sessionid VARCHAR(100) NULL,
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    duration INTEGER NULL,
    metadata TEXT NULL
);
```

### Service Registration
```csharp
// Program.cs
builder.Services.AddScoped<IUserActivityService, UserActivityService>();
builder.Services.AddHostedService<UserActivityBackgroundService>();
```

## 📊 API Endpoints

### User Endpoints
- `GET /api/useractivity/stats` - Thống kê hoạt động user hiện tại
- `GET /api/useractivity/last-activity` - Thời gian hoạt động cuối
- `GET /api/useractivity/is-online` - Kiểm tra user có online không
- `GET /api/useractivity/online-count` - Số user đang online (public)

### Admin Endpoints
- `GET /api/useractivity/admin/user/{userId}/stats` - Thống kê user theo ID
- `POST /api/useractivity/admin/cleanup` - Cleanup data cũ

## 🚀 Performance Features

### In-Memory Caching
```csharp
// Cache recent activities before batch insert
private static readonly ConcurrentQueue<UserActivity> _pendingActivities = new();
private static readonly ConcurrentDictionary<int, DateTime> _userLastActivity = new();
```

### Background Processing
- **Batch Insert**: Process 100 activities per batch
- **10-minute Interval**: Cập nhật database mỗi 10 phút
- **Graceful Shutdown**: Final sync khi shutdown
- **Error Recovery**: Re-queue failed activities

### Smart Filtering
```csharp
// Skip tracking for noise endpoints
var skipPaths = new[] {
    "/health", "/metrics", "/favicon.ico", 
    "/api/image/", "/static/", "/swagger"
};
```

## 📈 Analytics & Insights

### UserActivityStats
```csharp
public class UserActivityStats {
    public DateTime? LastActivity { get; set; }
    public DateTime? LastLogin { get; set; }
    public string? LastLoginIp { get; set; }
    public int ActivitiesLast24Hours { get; set; }
    public int ActivitiesLast7Days { get; set; }
    public List<string> RecentEndpoints { get; set; }
}
```

### Online Status
- **Online Threshold**: 5 phút không hoạt động = offline
- **Real-time Count**: Số user online hiện tại
- **Session Tracking**: Group activities theo session

## 🔒 Security & Privacy

### IP Address Handling
```csharp
// Support proxy headers
var forwardedFor = context.Request.Headers["X-Forwarded-For"];
var realIp = context.Request.Headers["X-Real-IP"];
var remoteIp = context.Connection.RemoteIpAddress;
```

### Data Retention
- **30 Days**: Tự động xóa activities cũ hơn 30 ngày
- **GDPR Compliance**: User có thể request xóa data
- **Anonymization**: Option để anonymize IP addresses

## 🛠️ Usage Examples

### Manual Tracking
```csharp
// Track specific activity
await _userActivityService.TrackUserActivityAsync(
    userId: 123,
    ipAddress: "*************", 
    userAgent: "Chrome/91.0",
    endpoint: "/api/comic/456"
);

// Track login
await _userActivityService.TrackUserLoginAsync(userId, ipAddress);
```

### Check User Status
```csharp
// Check if user is online
var isOnline = await _userActivityService.IsUserOnlineAsync(userId);

// Get last activity
var lastActivity = await _userActivityService.GetLastActivityAsync(userId);

// Get activity stats
var stats = await _userActivityService.GetUserActivityStatsAsync(userId);
```

## 📊 Monitoring & Metrics

### Background Service Logs
```
[INFO] User Activity Background Service started
[DEBUG] User activities processed successfully
[INFO] Processed 150 user activities
[INFO] Cleaned up 1250 old user activities
```

### Performance Metrics
- **Processing Time**: Average batch processing time
- **Queue Size**: Number of pending activities
- **Error Rate**: Failed processing percentage
- **Memory Usage**: In-memory cache size

## 🔧 Troubleshooting

### Common Issues
1. **High Memory Usage**: Adjust batch size or processing interval
2. **Database Locks**: Optimize indexes or use read replicas
3. **Missing Activities**: Check middleware registration order
4. **Performance Issues**: Enable query logging and optimize

### Configuration Tuning
```csharp
// Adjust processing interval
private readonly TimeSpan _processingInterval = TimeSpan.FromMinutes(10);

// Adjust batch size
if (activitiesToProcess.Count >= 100) break;

// Adjust online threshold
var onlineThreshold = DateTime.UtcNow.AddMinutes(-5);
```

## 🚀 Future Enhancements

### Planned Features
- [ ] **Real-time Analytics Dashboard**
- [ ] **User Behavior Patterns**
- [ ] **Anomaly Detection**
- [ ] **Geographic Analytics**
- [ ] **Device/Browser Analytics**
- [ ] **A/B Testing Support**

### Performance Improvements
- [ ] **Redis Integration** for distributed caching
- [ ] **Event Sourcing** for better scalability
- [ ] **Stream Processing** with Apache Kafka
- [ ] **Machine Learning** for user insights

---
**Last Updated**: {DateTime.Now}
**Version**: 1.0.0
