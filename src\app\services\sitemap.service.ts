import { Injectable } from '@angular/core';

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
  images?: Array<{
    loc: string;
    title?: string;
    caption?: string;
  }>;
}

@Injectable({
  providedIn: 'root'
})
export class SitemapService {
  private readonly baseUrl = 'https://metruyenmoi.com';

  constructor() { }

  /**
   * Generate XML sitemap from URLs
   */
  generateSitemap(urls: SitemapUrl[]): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>\n';
    const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">\n';
    const urlsetClose = '</urlset>';

    const urlElements = urls.map(url => this.generateUrlElement(url)).join('\n');

    return xmlHeader + urlsetOpen + urlElements + '\n' + urlsetClose;
  }

  /**
   * Generate sitemap index
   */
  generateSitemapIndex(sitemaps: Array<{loc: string, lastmod?: string}>): string {
    const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>\n';
    const indexOpen = '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
    const indexClose = '</sitemapindex>';

    const sitemapElements = sitemaps.map(sitemap => {
      let element = `  <sitemap>\n    <loc>${sitemap.loc}</loc>\n`;
      if (sitemap.lastmod) {
        element += `    <lastmod>${sitemap.lastmod}</lastmod>\n`;
      }
      element += '  </sitemap>';
      return element;
    }).join('\n');

    return xmlHeader + indexOpen + sitemapElements + '\n' + indexClose;
  }

  /**
   * Generate URL element for sitemap
   */
  private generateUrlElement(url: SitemapUrl): string {
    let element = '  <url>\n';
    element += `    <loc>${this.escapeXml(url.loc)}</loc>\n`;
    
    if (url.lastmod) {
      element += `    <lastmod>${url.lastmod}</lastmod>\n`;
    }
    
    if (url.changefreq) {
      element += `    <changefreq>${url.changefreq}</changefreq>\n`;
    }
    
    if (url.priority !== undefined) {
      element += `    <priority>${url.priority.toFixed(1)}</priority>\n`;
    }

    // Add image elements
    if (url.images && url.images.length > 0) {
      url.images.forEach(image => {
        element += '    <image:image>\n';
        element += `      <image:loc>${this.escapeXml(image.loc)}</image:loc>\n`;
        if (image.title) {
          element += `      <image:title>${this.escapeXml(image.title)}</image:title>\n`;
        }
        if (image.caption) {
          element += `      <image:caption>${this.escapeXml(image.caption)}</image:caption>\n`;
        }
        element += '    </image:image>\n';
      });
    }
    
    element += '  </url>';
    return element;
  }

  /**
   * Escape XML special characters
   */
  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&apos;');
  }

  /**
   * Get static pages for sitemap
   */
  getStaticPages(): SitemapUrl[] {
    const now = new Date().toISOString();
    
    return [
      {
        loc: `${this.baseUrl}/`,
        lastmod: now,
        changefreq: 'daily',
        priority: 1.0
      },
      {
        loc: `${this.baseUrl}/truyen-tranh`,
        lastmod: now,
        changefreq: 'daily',
        priority: 0.9
      },
      {
        loc: `${this.baseUrl}/the-loai`,
        lastmod: now,
        changefreq: 'weekly',
        priority: 0.8
      },
      {
        loc: `${this.baseUrl}/tim-kiem`,
        lastmod: now,
        changefreq: 'monthly',
        priority: 0.7
      },
      {
        loc: `${this.baseUrl}/xep-hang`,
        lastmod: now,
        changefreq: 'daily',
        priority: 0.8
      },
      {
        loc: `${this.baseUrl}/truyen-hot`,
        lastmod: now,
        changefreq: 'daily',
        priority: 0.8
      },
      {
        loc: `${this.baseUrl}/truyen-moi`,
        lastmod: now,
        changefreq: 'hourly',
        priority: 0.9
      },
      {
        loc: `${this.baseUrl}/truyen-full`,
        lastmod: now,
        changefreq: 'daily',
        priority: 0.7
      }
    ];
  }

  /**
   * Generate comic URLs for sitemap
   */
  generateComicUrls(comics: any[]): SitemapUrl[] {
    return comics.map(comic => ({
      loc: `${this.baseUrl}/truyen-tranh/${comic.slug || comic.url}-${comic.id}`,
      lastmod: comic.updatedAt || comic.updateAt,
      changefreq: 'weekly' as const,
      priority: 0.8,
      images: comic.thumbnail ? [{
        loc: comic.thumbnail,
        title: comic.title,
        caption: `Ảnh bìa truyện ${comic.title}`
      }] : undefined
    }));
  }

  /**
   * Generate chapter URLs for sitemap
   */
  generateChapterUrls(comic: any, chapters: any[]): SitemapUrl[] {
    return chapters.map(chapter => ({
      loc: `${this.baseUrl}/truyen-tranh/${comic.slug || comic.url}/${chapter.slug || chapter.id}`,
      lastmod: chapter.updateAt,
      changefreq: 'monthly' as const,
      priority: 0.6
    }));
  }

  /**
   * Generate genre URLs for sitemap
   */
  generateGenreUrls(genres: any[]): SitemapUrl[] {
    return genres.map(genre => ({
      loc: `${this.baseUrl}/the-loai/${genre.slug}`,
      lastmod: new Date().toISOString(),
      changefreq: 'weekly' as const,
      priority: 0.7
    }));
  }

  /**
   * Generate author URLs for sitemap
   */
  generateAuthorUrls(authors: any[]): SitemapUrl[] {
    return authors.map(author => ({
      loc: `${this.baseUrl}/tac-gia/${author.slug}`,
      lastmod: new Date().toISOString(),
      changefreq: 'monthly' as const,
      priority: 0.6
    }));
  }

  /**
   * Format date for sitemap
   */
  formatDate(date: string | Date): string {
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }

  /**
   * Get main sitemap index
   */
  getMainSitemapIndex(): Array<{loc: string, lastmod?: string}> {
    const now = new Date().toISOString();
    
    return [
      {
        loc: `${this.baseUrl}/sitemap-static.xml`,
        lastmod: now
      },
      {
        loc: `${this.baseUrl}/sitemap-comics.xml`,
        lastmod: now
      },
      {
        loc: `${this.baseUrl}/sitemap-chapters.xml`,
        lastmod: now
      },
      {
        loc: `${this.baseUrl}/sitemap-genres.xml`,
        lastmod: now
      },
      {
        loc: `${this.baseUrl}/sitemap-authors.xml`,
        lastmod: now
      },
      {
        loc: `${this.baseUrl}/sitemap-news.xml`,
        lastmod: now
      },
      {
        loc: `${this.baseUrl}/sitemap-images.xml`,
        lastmod: now
      }
    ];
  }

  /**
   * Split large sitemaps into chunks
   */
  chunkSitemap(urls: SitemapUrl[], chunkSize: number = 50000): SitemapUrl[][] {
    const chunks: SitemapUrl[][] = [];
    for (let i = 0; i < urls.length; i += chunkSize) {
      chunks.push(urls.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * Validate sitemap URL count
   */
  validateSitemap(urls: SitemapUrl[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (urls.length > 50000) {
      errors.push(`Sitemap contains ${urls.length} URLs, maximum is 50,000`);
    }
    
    // Check for duplicate URLs
    const urlSet = new Set();
    const duplicates: string[] = [];
    
    urls.forEach(url => {
      if (urlSet.has(url.loc)) {
        duplicates.push(url.loc);
      } else {
        urlSet.add(url.loc);
      }
    });
    
    if (duplicates.length > 0) {
      errors.push(`Found ${duplicates.length} duplicate URLs`);
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}
