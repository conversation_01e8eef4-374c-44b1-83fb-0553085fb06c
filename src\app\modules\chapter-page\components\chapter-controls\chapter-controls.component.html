<!-- Control Bar -->
<nav class="control-bar" appFadeIn [duration]="1000">
  <div *ngIf="isBrowser" class="control-bar-content">
    <!-- Home Button -->
    <div class="control-group">
      <a href="" title="Trang chủ" class="control-button control-button-home">
        <svg class="control-icon" viewBox="0 0 24 24">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
          <polyline points="9,22 9,12 15,12 15,22" />
        </svg>
      </a>
    </div>

    <!-- Fullscreen Button -->
    <div class="control-group">
      <button title="Toàn màn hình" class="control-button" (click)="onToggleFullscreen()">
        <svg class="control-icon" viewBox="0 0 24 24">
          <path
            d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2 2v-3M3 16v3a2 2 0 0 0 2 2h3"
          />
        </svg>
      </button>
    </div>

    <!-- Chapter Navigation -->
    <div class="chapter-navigation-group">
      <button
        class="nav-button nav-button-prev"
        (click)="onNavigate('next')"
        aria-label="Chương trước"
        [disabled]="isLoading"
        [class.nav-button-active]="canNavigatePrev()"
      >
        <svg class="nav-icon" viewBox="0 0 24 24">
          <polyline points="15 18 9 12 15 6" />
        </svg>
      </button>

      <div class="chapter-selector-wrapper">
        <app-chapter-selector
          [chapters]="listChapters"
          [mainChapter]="mainChapter"
          [topToBottom]="topToBottom"
          (chapterChange)="onChapterChange($event)"
        >
        </app-chapter-selector>
      </div>

      <button
        class="nav-button nav-button-next"
        (click)="onNavigate('prev')"
        aria-label="Chương tiếp"
        [disabled]="isLoading"
        [class.nav-button-active]="canNavigateNext()"
      >
        <svg class="nav-icon" viewBox="0 0 24 24">
          <polyline points="9 18 15 12 9 6" />
        </svg>
      </button>
    </div>

    <!-- Zoom Controls -->
    <div (appClickOutside)="onZoomPanelClose()" class="control-group zoom-group">
      <button title="Thu phóng" class="control-button zoom-button" (click)="onZoomPanelToggle($event)">
        <svg class="control-icon" viewBox="0 0 24 24">
          <ng-container *ngIf="!isLimitZoom; else zoomOutIcon">
            <circle cx="11" cy="11" r="8" />
            <line x1="21" y1="21" x2="16.65" y2="16.65" />
            <line x1="11" y1="8" x2="11" y2="14" />
            <line x1="8" y1="11" x2="14" y2="11" />
          </ng-container>
          <ng-template #zoomOutIcon>
            <circle cx="11" cy="11" r="8" />
            <line x1="21" y1="21" x2="16.65" y2="16.65" />
            <line x1="8" y1="11" x2="14" y2="11" />
          </ng-template>
        </svg>
        
        <!-- Zoom Panel -->
        <div class="zoom-panel">
          <div class="zoom-info">
            <span class="zoom-percentage">{{ getZoomPercentage() }}%</span>
            <div class="zoom-controls">
              <button class="zoom-control-btn" (click)="onZoom('out')" title="Thu nhỏ">
                <svg class="zoom-control-icon" viewBox="0 0 24 24">
                  <line x1="5" y1="12" x2="19" y2="12" />
                </svg>
              </button>
              <button class="zoom-control-btn" (click)="onZoom('in')" title="Phóng to">
                <svg class="zoom-control-icon" viewBox="0 0 24 24">
                  <line x1="12" y1="5" x2="12" y2="19" />
                  <line x1="5" y1="12" x2="19" y2="12" />
                </svg>
              </button>
            </div>
          </div>

          <button (click)="onZoom('reset')" title="Đặt lại" class="zoom-reset-btn">
            <svg class="zoom-reset-icon" viewBox="0 0 24 24">
              <path
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>
        </div>
      </button>
    </div>

    <!-- Settings Button -->
    <div class="control-group">
      <button
        (click)="onToggleMenu()"
        title="Cài đặt"
        class="control-button settings-button"
      >
        <svg class="control-icon" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="3" />
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1" />
        </svg>
      </button>
    </div>
  </div>

  <!-- Settings Menu -->
  <div *ngIf="isBrowser && isToggle" class="settings-menu">
    <div class="settings-menu-content">
      <div class="settings-option">
        <h3 class="settings-option-title">Hướng đọc</h3>
        <div class="settings-option-buttons">
          <button
            class="settings-btn"
            [class.settings-btn-active]="isVertical"
            (click)="onDirectionChange(true)"
          >
            <svg class="settings-btn-icon" viewBox="0 0 24 24">
              <rect x="6" y="2" width="12" height="20" rx="2" />
              <line x1="12" y1="6" x2="12" y2="10" />
              <line x1="12" y1="14" x2="12" y2="18" />
            </svg>
            <span class="settings-btn-text">Đọc dọc</span>
          </button>

          <button
            class="settings-btn"
            [class.settings-btn-active]="!isVertical"
            (click)="onDirectionChange(false)"
          >
            <svg class="settings-btn-icon" viewBox="0 0 24 24">
              <rect x="2" y="6" width="20" height="12" rx="2" />
              <line x1="6" y1="12" x2="10" y2="12" />
              <line x1="14" y1="12" x2="18" y2="12" />
            </svg>
            <span class="settings-btn-text">Đọc ngang</span>
          </button>
        </div>
      </div>

      <div class="settings-option">
        <h3 class="settings-option-title">Tự động chuyển chương</h3>
        <label class="settings-toggle">
          <input
            type="checkbox"
            class="settings-toggle-input"
            (change)="onAutoNextChange($event)"
            [checked]="isAutoNextChapter"
          />
          <div class="settings-toggle-slider">
            <div class="settings-toggle-thumb"></div>
          </div>
          <span class="settings-toggle-label">
            {{ isAutoNextChapter ? 'Bật' : 'Tắt' }}
          </span>
        </label>
      </div>

      <div class="settings-option">
        <h3 class="settings-option-title">Chế độ ban đêm</h3>
        <button
          class="settings-btn settings-btn-full"
          [class.settings-btn-active]="isNightMode"
          (click)="onNightModeChange(!isNightMode)"
        >
          <svg class="settings-btn-icon" viewBox="0 0 24 24">
            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
          </svg>
          <span class="settings-btn-text">
            {{ isNightMode ? 'Tắt chế độ ban đêm' : 'Bật chế độ ban đêm' }}
          </span>
        </button>
      </div>
    </div>
  </div>
</nav>
