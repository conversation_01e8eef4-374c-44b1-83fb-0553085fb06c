using ComicAPI.Models;
namespace ComicAPI.DTOs;

public class ChapterServerDTO
{
    public int ID { get; set; }
    public string ServerName { get; set; } = null!;
    public string? Host { get; set; }
    public string? Referer { get; set; }
    public string[]? Images { get; set; }
    public string? Code { get; set; }
    public int Status { get; set; }
    public int IsDefault { get; set; }

    public ChapterServerDTO(ChapterServer entity)
    {
        ID = entity.ID;
        ServerName = entity.ServerName ?? string.Empty;
        Host = entity.Host;
        Referer = entity.Referer;
        Images = entity.Images;
        Code = entity.Code;
        Status = entity.Status;
        IsDefault = entity.IsDefault;
    }
}
