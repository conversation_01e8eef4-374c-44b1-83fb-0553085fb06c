import { ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Comic } from '@schema';
import { AccountService } from '@services/account.service';
import { ComicService } from '@services/comic.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
  selector: 'app-followed',
  templateUrl: './followed-page.component.html',
  styleUrl: './followed-page.component.scss',
  standalone: false
})
export class FollowedPageComponent extends OptimizedBaseComponent implements OnInit {
  comics: Comic[] = [];
  totalpage = 1;
  isLogin = false;
  isFirstLoading = false;
  isComicLoading = false;
  selectedComic: Comic | undefined = {} as Comic;
  currentPage = 1;
  constructor(
    private comicService: ComicService,
    private accountService: AccountService,
    private route: ActivatedRoute,
    private toastService: ToastService,
    private popupService: PopupService,
    private router: Router,
    private seoService: SeoService,
    override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) override platformId: object,
  ) {
    super(cd, platformId);
    this.seoService.setTitle('Truyện đã theo dõi');
  }
  // @Output() Click: any;
  OnChangePage(page: number) {
    this.router.navigate(['theo-doi'], {
      queryParams: { page: page },
      fragment: 'comics',
    });
  }
  ngOnInit(): void {
    this.isFirstLoading = true;

    this.runInBrowser(() => {
      this.route.queryParams.subscribe((params) => {
        this.isLogin = this.accountService.isAuthenticated();
        const page = Number(params['page']) || 1;
        if (this.isLogin) {

          this.reqFollowComics(page);

        } else {
          this.comics = [];
          this.isFirstLoading = false;
        }
      });
    });


  }


  reqFollowComics(page = 1) {
    this.isComicLoading = true;
    this.accountService.GetFollowedComics(page).subscribe((res: any) => {
      this.comics = res.data.comics;
      this.isFirstLoading = false;
      this.isComicLoading = false;
      this.totalpage = res.data.totalpage;
      this.currentPage = page;
      this.safeMarkForCheck();
    });
  }


  onUnFollowClick(ids: number[]) {
    if (!ids.length) {
      return;
    }
    const [comicId] = ids;
    this.selectedComic = this.comics.find((comic) => comic.id === comicId);
    this.popupService
      .showConfirmPopup({
        title: 'Hủy theo dõi truyện',
        message: this.message,
        confirmButtonText: 'Hủy theo dõi',
        cancelButtonText: 'Hủy',
      })
      .then((result: any) => {
        const { isconfirm, isCancel } = result;
        if (isconfirm) {
          this.onConfirm();
        }
        if (isCancel) {
          // this.onCancel();
        }
      });
  }

  onConfirm() {
    if (!this.selectedComic) {
      return;
    }
    this.accountService
      .Follow(this.selectedComic.id, false)
      .subscribe((res: any) => {
        if (res.status === 1) {
          this.reqFollowComics(this.currentPage);
          this.toastService.show(
            ToastType.Success,
            `Đã bỏ theo dõi ${this.selectedComic?.title}`,
          );
        } else {
          this.toastService.show(ToastType.Error, res.message);
        }
      });
  }

  get message(): string {
    if (!this.selectedComic) return '';
    return `Bạn có chắc chắn muốn hủy theo dõi <b>${this.selectedComic?.title}</b> ?`;
  }
  // onCancel() { }
}
