import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ComponentRef, On<PERSON><PERSON>roy, ViewChild, ViewContainerRef, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ChatBoxComponent } from '../chat-box.component';
import { PopupService } from '@services/popup.service';

@Component({
  selector: 'app-chat-box-bubble',
  templateUrl: './chat-box-bubble.component.html',
  styleUrl: './chat-box-bubble.component.scss',
  standalone: true,
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule],

})
export class ChatBoxBubbleComponent implements OnDestroy {
  IsVisible = true;
  @ViewChild('chatbox', { read: ViewContainerRef }) container!: ViewContainerRef;
  chatBoxComponentRef!: ComponentRef<ChatBoxComponent>;
  async loadHeavyComponent() {
    const { ChatBoxComponent } = await import('../chat-box.component');
    this.container.clear();
    this.chatBoxComponentRef = this.container.createComponent(ChatBoxComponent);
    this.chatBoxComponentRef.instance.hideChatChange.subscribe((value) => this.closeChat());
  }
  public isChapterRoute = false;
  public static Instance: ChatBoxBubbleComponent | null = null;

  constructor(private route: ActivatedRoute, 
    private router: Router, 
    private cd: ChangeDetectorRef,
    private popupService: PopupService,
  ) {
    ChatBoxBubbleComponent.Instance = this;

  }
  ngOnDestroy() {
    ChatBoxBubbleComponent.Instance = null;
  }

  public SetVisible(isShow: boolean) {
    this.IsVisible = isShow
    this.cd.detectChanges();
  }
  closeChat() {
    this.SetVisible(true);
    this.chatBoxComponentRef.instance.setVisible(false)
  }
  showChat() {
    this.SetVisible(false);
    this.loadHeavyComponent().then(() => this.chatBoxComponentRef.instance.setVisible(true));

  }
}
