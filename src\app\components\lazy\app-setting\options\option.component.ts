import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, ContentChild, EventEmitter, Input, Output, TemplateRef } from '@angular/core';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { IOption } from 'src/app/dataSource/schema/IOption';

@Component({
  selector: 'app-option',
  standalone: true,
  templateUrl: './option.component.html',
  imports: [CommonModule],
  // changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OptionComponent {

  @Input()
  selectedValue?: any;  // Lưu trữ giá trị đã chọn
  @Output()
  selectedValueChange = new EventEmitter<any>();
  @Input()
  options: IOption[] = []  // Danh sách tùy chọn
  selectedIdx = 0;
  constructor(private cd: ChangeDetectorRef) { };
  // Hàm để toggle (mở hoặc đóng) dropdown


  ngOnInit() {
    let idx = this.options.findIndex((option) => option.value === this.selectedValue);
    this.selectedIdx = idx
  }

  // Hàm để chọn một option
  selectOption(option: IOption) {
    this.selectedValue = option.value;
    this.selectedIdx = this.options.findIndex((option) => option.value === this.selectedValue);
    this.selectedValueChange.emit(this.selectedValue);
  }

  closeDropdown() {
  }

  ngOnChanges() {
    let idx = this.options.findIndex((option) => option.value === this.selectedValue);
    this.selectedIdx = idx    
    this.cd.detectChanges();

  }
}
