import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Chapter, ChapterPage } from '@schema';

export interface NavigationEvent {
  direction: 'next' | 'prev';
}

export interface ZoomEvent {
  action: 'in' | 'out' | 'reset';
}

export interface SettingsEvent {
  type: 'direction' | 'autoNext' | 'nightMode';
  value: boolean;
}

@Component({
  selector: 'app-chapter-controls',
  templateUrl: './chapter-controls.component.html',
  styleUrl: './chapter-controls.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class ChapterControlsComponent extends OptimizedBaseComponent implements OnInit {
  // Input properties
  @Input() listChapters: Chapter[] = [];
  @Input() mainChapter!: ChapterPage;
  @Input() isLoading = false;
  @Input() zoomLevel = 0;
  @Input() isLimitZoom = false;
  @Input() isToggle = false;
  @Input() isVertical = true;
  @Input() isAutoNextChapter = false;
  @Input() isNightMode = false;
  @Input() topToBottom = true;

  // Output events
  @Output() navigate = new EventEmitter<NavigationEvent>();
  @Output() chapterChange = new EventEmitter<number>();
  @Output() zoom = new EventEmitter<ZoomEvent>();
  @Output() toggleFullscreen = new EventEmitter<void>();
  @Output() toggleMenu = new EventEmitter<boolean>();
  @Output() settingsChange = new EventEmitter<SettingsEvent>();

  // Component state
  private readonly _zoomPercentage = signal(100);
  private readonly _canNavigatePrev = signal(false);
  private readonly _canNavigateNext = signal(false);

  // Computed properties
  readonly zoomPercentage = computed(() => {
    return Math.round((this.zoomLevel) * 100) + 100;
  });

  readonly canNavigatePrev = computed(() => {
    if (!this.listChapters.length || !this.mainChapter) return false;
    const lastChapter = this.listChapters[this.listChapters.length - 1];
    return this.mainChapter.slug !== lastChapter.slug;
  });

  readonly canNavigateNext = computed(() => {
    if (!this.listChapters.length || !this.mainChapter) return false;
    const firstChapter = this.listChapters[0];
    return this.mainChapter.slug !== firstChapter.slug;
  });

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.updateComputedValues();
  }

  // Navigation methods
  onNavigate(direction: 'next' | 'prev'): void {
    if (this.isLoading) return;
    this.navigate.emit({ direction });
  }

  onChapterChange(chapterId: number): void {
    this.chapterChange.emit(chapterId);
  }

  // Zoom methods
  onZoom(action: 'in' | 'out' | 'reset'): void {
    this.zoom.emit({ action });
  }

  getZoomPercentage(): number {
    return this.zoomPercentage();
  }

  // Settings methods
  onToggleFullscreen(): void {
    this.toggleFullscreen.emit();
  }

  onToggleMenu(state?: boolean): void {
    const newState = state !== undefined ? state : !this.isToggle;
    this.toggleMenu.emit(newState);
  }

  onDirectionChange(isVertical: boolean): void {
    this.settingsChange.emit({ type: 'direction', value: isVertical });
  }

  onAutoNextChange(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.settingsChange.emit({ type: 'autoNext', value: checkbox.checked });
  }

  onNightModeChange(isNightMode: boolean): void {
    this.settingsChange.emit({ type: 'nightMode', value: isNightMode });
  }

  // TrackBy functions
  trackByChapterId = (index: number, chapter: Chapter): number => chapter.id;

  // Event handlers for zoom panel
  onZoomPanelToggle(event: Event): void {
    event.stopPropagation();
    const panel = (event.currentTarget as HTMLElement).querySelector('.zoom-panel');
    panel?.classList.toggle('zoom-panel-active');
  }

  onZoomPanelClose(): void {
    // This will be called by click outside directive
  }

  private updateComputedValues(): void {
    this._zoomPercentage.set(this.zoomPercentage());
    this._canNavigatePrev.set(this.canNavigatePrev());
    this._canNavigateNext.set(this.canNavigateNext());
  }
}
