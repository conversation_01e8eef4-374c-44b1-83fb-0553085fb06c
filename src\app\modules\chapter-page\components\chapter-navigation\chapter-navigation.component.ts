import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Chapter, ChapterPage } from '@schema';

export interface NavigationEvent {
  direction: 'next' | 'prev';
}

@Component({
  selector: 'app-chapter-navigation',
  templateUrl: './chapter-navigation.component.html',
  styleUrl: './chapter-navigation.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class ChapterNavigationComponent extends OptimizedBaseComponent implements OnInit {
  // Input properties
  @Input() listChapters: Chapter[] = [];
  @Input() mainChapter!: ChapterPage;
  @Input() isLoading = false;

  // Output events
  @Output() navigate = new EventEmitter<NavigationEvent>();

  // Component state
  private readonly _canNavigatePrev = signal(false);
  private readonly _canNavigateNext = signal(false);

  // Computed properties
  readonly canNavigatePrev = computed(() => {
    if (!this.listChapters.length || !this.mainChapter) return false;
    const lastChapter = this.listChapters[this.listChapters.length - 1];
    return this.mainChapter.slug !== lastChapter.slug;
  });

  readonly canNavigateNext = computed(() => {
    if (!this.listChapters.length || !this.mainChapter) return false;
    const firstChapter = this.listChapters[0];
    return this.mainChapter.slug !== firstChapter.slug;
  });

  readonly navigationTitle = computed(() => {
    return this.mainChapter?.title || 'Chương hiện tại';
  });

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.updateComputedValues();
  }

  // Navigation methods
  onNavigate(direction: 'next' | 'prev'): void {
    if (this.isLoading) return;
    this.navigate.emit({ direction });
  }

  private updateComputedValues(): void {
    this._canNavigatePrev.set(this.canNavigatePrev());
    this._canNavigateNext.set(this.canNavigateNext());
  }
}
