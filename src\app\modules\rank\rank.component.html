<app-breadcrumb
  class="z-10 mt-3 mb-5 md:container mx-auto flex"
  [Links]="[
    { label: 'Trang chủ', url: '/' },
    { label: 'Xếp hạng', url: '' }
  ]"
>
</app-breadcrumb>

<div id="content" class="md:container mx-auto mt-5 dark:text-white">
  <!-- Enhanced Ranking Container -->
  <!-- Subtle Decorative Elements -->
  <div class="ranking-decoration star opacity-5">⭐</div>

  <!-- Enhanced Header Section -->
  <div class="ranking-header">
    <div>
      <h1 class="ranking-title">Bảng Xếp Hạng Truyện Tranh</h1>
      <p class="text-neutral-600 dark:text-neutral-400 mt-2">
        Khám phá những bộ truyện tranh được yêu thích nhất
      </p>
    </div>

    <!-- Enhanced Filter Controls -->
    <div class="ranking-filters">
      <div class="filter-group">
        <label class="filter-label">Sắp xếp theo</label>
        <app-selection-2
          class="filter-select"
          [options]="dataView.sorts"
          (selectedValueChange)="onSortOptionChange($event)"
          [selectedValue]="selectOptions.sorts.value"
        >
        </app-selection-2>
      </div>

      <div class="filter-group">
        <label class="filter-label">Trạng thái</label>
        <app-selection-2
          class="filter-select"
          [options]="dataView.status"
          (selectedValueChange)="onStatusOptionChange($event)"
          [selectedValue]="selectOptions.status.value"
        >
        </app-selection-2>
      </div>
    </div>
  </div>

  <!-- Enhanced Top 3 Podium Section -->
  <div class="podium-container" *ngIf="listTopComics && listTopComics.length >= 3 && currentPage ===0">
    <div class="text-center mb-5">
      <h2 class="text-xl font-semibold text-neutral-800 dark:text-white mb-2">
        Top 3 Truyện Tranh Hàng Đầu
      </h2>
      <p class="text-sm text-neutral-600 dark:text-neutral-400">Những bộ truyện được yêu thích nhất</p>
    </div>

    <div class="podium-grid">
      <!-- Second Place -->
      <div class="podium-item second-place">
        <div class="podium-card" (click)="showDetails(listTopComics[1])">
          <div class="rank-badge">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
              />
            </svg>
          </div>
          <a [routerLink]="['/truyen-tranh', listTopComics[1].url + '-' + listTopComics[1].id]">
            <img
              loading="lazy"
              class="comic-cover"
              [src]="listTopComics[1].coverImage"
              [alt]="listTopComics[1].title"
              onerror="this.src='/option2.png'"
            />
          </a>
          <h3 class="comic-title">{{ listTopComics[1].title }}</h3>
          <p class="comic-author">{{ listTopComics[1].author || 'Đang cập nhật' }}</p>
          <div class="comic-stats">
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>
            {{ listTopComics[1].viewCount | numeral }}
          </div>
        </div>
      </div>

      <!-- First Place -->
      <div class="podium-item first-place">
        <div class="podium-card" (click)="showDetails(listTopComics[0])">
          <div class="rank-badge">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
              />
            </svg>
          </div>
          <a [routerLink]="['/truyen-tranh', listTopComics[0].url + '-' + listTopComics[0].id]">
            <img
              loading="lazy"
              class="comic-cover"
              [src]="listTopComics[0].coverImage"
              [alt]="listTopComics[0].title"
              onerror="this.src='/option2.png'"
            />
          </a>
          <h3 class="comic-title">{{ listTopComics[0].title }}</h3>
          <p class="comic-author">{{ listTopComics[0].author || 'Đang cập nhật' }}</p>
          <div class="comic-stats">
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>
            {{ listTopComics[0].viewCount | numeral }}
          </div>
        </div>
      </div>

      <!-- Third Place -->
      <div class="podium-item third-place">
        <div class="podium-card" (click)="showDetails(listTopComics[2])">
          <div class="rank-badge">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path
                d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
              />
            </svg>
          </div>
          <a [routerLink]="['/truyen-tranh', listTopComics[2].url + '-' + listTopComics[2].id]">
            <img
              loading="lazy"
              class="comic-cover"
              [src]="listTopComics[2].coverImage"
              [alt]="listTopComics[2].title"
              onerror="this.src='/option2.png'"
            />
          </a>
          <h3 class="comic-title">{{ listTopComics[2].title }}</h3>
          <p class="comic-author">{{ listTopComics[2].author || 'Đang cập nhật' }}</p>
          <div class="comic-stats">
            <svg
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
              <circle cx="12" cy="12" r="3"></circle>
            </svg>
            {{ listTopComics[2].viewCount | numeral }}
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Selected Comic Details -->
    <div class="selected-comic-details" *ngIf="selectedComic">
      <div class="text-center mb-6">
        <h3 class="text-2xl font-bold text-neutral-800 dark:text-white mb-2">
          {{ selectedComic.title }}
        </h3>
        <p class="text-neutral-600 dark:text-neutral-400">
          Tác giả: {{ selectedComic.author || 'Đang cập nhật' }}
        </p>
      </div>

      <div class="comic-stats-row">
        <div class="stat-item">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          <span>{{ selectedComic.viewCount | numeral }} lượt xem</span>
        </div>

        <div class="stat-item">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.29 1.51 4.04 3 5.5l7 7z"
            ></path>
          </svg>
          <span>{{ selectedComic.rating || 0 }} điểm</span>
        </div>

        <div class="stat-item">
          <svg
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
            <polyline points="14,2 14,8 20,8"></polyline>
          </svg>
          <span>{{ selectedComic.chapters?.length || 0 }} chapter</span>
        </div>
      </div>

      <div class="genre-tags" *ngIf="selectedComic.genres && selectedComic.genres.length > 0">
        <span
          *ngFor="let genre of selectedComic.genres; let i = index"
          class="genre-tag"
          [class.primary]="i < 2"
          [class.secondary]="i >= 2"
        >
          {{ genre.title }}
        </span>
      </div>

      <p class="comic-description" *ngIf="selectedComic.description">
        {{ selectedComic.description }}
      </p>
    </div>
  </div>

  <!-- Enhanced Ranking List Section -->
  <app-grid-comic
    id="listComic"
    [listComics]="listComics | slice : (0) :listComics.length"
   
  ></app-grid-comic>
  <app-pagination *ngIf="totalpage > 1 && !ssr()" [totalpage]="totalpage" [currentPage]="currentPage" (OnChange)="onChangePage($event)"> </app-pagination>
</div>
