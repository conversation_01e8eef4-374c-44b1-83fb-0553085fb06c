// Privacy Policy Container
.privacy-policy-container {
  @apply max-w-6xl mx-auto px-4 py-8 bg-white dark:bg-dark-bg text-neutral-900 dark:text-white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.7;
}

// Header Section
.privacy-header {
  @apply text-center mb-12 py-16 bg-gradient-to-br from-sky-50 to-indigo-100 dark:from-neutral-800 dark:to-neutral-900 rounded-2xl;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
    pointer-events: none;
  }
}

.privacy-header-content {
  @apply relative z-10;
}

.privacy-title {
  @apply text-4xl md:text-5xl font-bold mb-4 text-neutral-900 dark:text-white flex items-center justify-center gap-4;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.privacy-icon {
  @apply w-12 h-12 text-sky-600 dark:text-sky-400;
}

.privacy-subtitle {
  @apply text-xl text-neutral-600 dark:text-neutral-300 mb-6 max-w-2xl mx-auto;
}

.privacy-meta {
  @apply flex flex-wrap justify-center gap-6 text-sm text-neutral-500 dark:text-neutral-400;
}

.privacy-date,
.privacy-version {
  @apply px-4 py-2 bg-white dark:bg-neutral-800 rounded-full border border-neutral-200 dark:border-neutral-700;
}

// Table of Contents
.privacy-toc {
  @apply mb-12 p-6 bg-neutral-50 dark:bg-neutral-800 rounded-xl border border-neutral-200 dark:border-neutral-700;
}

.toc-title {
  @apply text-2xl font-bold mb-6 text-neutral-900 dark:text-white;
}

.toc-list {
  @apply grid grid-cols-1 md:grid-cols-2 gap-3;
  counter-reset: toc-counter;
}

.toc-link {
  @apply block p-3 text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300 hover:bg-sky-50 dark:hover:bg-sky-900/20 rounded-lg transition-all duration-200 font-medium;

  &:hover {
    transform: translateX(4px);
  }
}

// Main Content
.privacy-content {
  @apply space-y-16;
}

// Section Styling
.privacy-section {
  @apply scroll-mt-24;

  &:not(:last-child) {
    @apply border-b border-neutral-200 dark:border-neutral-700 pb-16;
  }
}

.section-title {
  @apply text-3xl font-bold mb-8 text-neutral-900 dark:text-white flex items-center gap-4;
}

.section-number {
  @apply inline-flex items-center justify-center w-12 h-12 bg-sky-600 text-white rounded-full text-lg font-bold;
}

.section-content {
  @apply space-y-6;
}

.section-intro {
  @apply text-lg text-neutral-700 dark:text-neutral-300 font-medium;
}

.subsection-title {
  @apply text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200;
}

// Highlight Box
.highlight-box {
  @apply p-6 bg-sky-50 dark:bg-sky-900/20 border-l-4 border-sky-500 rounded-r-lg;

  h3 {
    @apply text-lg font-semibold mb-3 text-sky-800 dark:text-sky-300;
  }

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2;

      &::before {
        content: '✓';
        @apply text-sky-600 dark:text-sky-400 font-bold mt-1 flex-shrink-0;
      }
    }
  }
}

// Info Grid
.info-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.info-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200;

  h4 {
    @apply text-lg font-semibold mb-3 text-neutral-900 dark:text-white;
  }

  ul {
    @apply space-y-2;

    li {
      @apply text-neutral-600 dark:text-neutral-300 flex items-start gap-2;

      &::before {
        content: '•';
        @apply text-sky-500 font-bold mt-1 flex-shrink-0;
      }
    }
  }
}

// Purpose Grid
.purpose-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.purpose-card {
  @apply p-6 bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800 dark:to-neutral-900 border border-neutral-200 dark:border-neutral-700 rounded-xl text-center hover:shadow-lg transition-all duration-300;

  &:hover {
    transform: translateY(-4px);
  }
}

.purpose-icon {
  @apply text-4xl mb-4 block;
}

// Cookies Grid
.cookies-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.cookie-card {
  @apply p-6 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  &.essential {
    @apply bg-lime-50 dark:bg-lime-900/20 border-lime-200 dark:border-lime-800;
  }

  &.functional {
    @apply bg-sky-50 dark:bg-sky-900/20 border-sky-200 dark:border-sky-800;
  }

  &.analytics {
    @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800;
  }

  &.advertising {
    @apply bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800;
  }

  h4 {
    @apply text-lg font-semibold mb-3;
  }

  p {
    @apply text-sm text-neutral-600 dark:text-neutral-300 mb-3;
  }

  ul {
    @apply space-y-1 text-sm;

    li {
      @apply flex items-start gap-2;

      &::before {
        content: '→';
        @apply text-neutral-400 mt-1 flex-shrink-0;
      }
    }
  }
}

.cookie-management {
  @apply p-6 bg-neutral-50 dark:bg-neutral-800 rounded-xl;

  ul {
    @apply space-y-3 mb-4;

    li {
      @apply flex items-start gap-3;

      &::before {
        content: '⚙️';
        @apply mt-1 flex-shrink-0;
      }
    }
  }
}

.warning-box {
  @apply p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg text-yellow-800 dark:text-yellow-300;
}

// Sharing Section
.sharing-principle {
  @apply p-6 bg-red-50 dark:bg-red-900/20 border-l-4 border-red-500 rounded-r-lg mb-8;

  h3 {
    @apply text-lg font-semibold mb-3 text-red-800 dark:text-red-300;
  }
}

.principle-text {
  @apply text-red-700 dark:text-red-300 font-medium;
}

.sharing-cases {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.sharing-case {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-3 text-neutral-900 dark:text-white;
  }

  p {
    @apply text-neutral-600 dark:text-neutral-300 mb-3;
  }

  ul {
    @apply space-y-2;

    li {
      @apply text-sm text-neutral-600 dark:text-neutral-300 flex items-start gap-2;

      &::before {
        content: '▸';
        @apply text-sky-500 mt-1 flex-shrink-0;
      }
    }
  }
}

// Security Section
.security-measures {
  @apply grid grid-cols-1 md:grid-cols-2 gap-8;
}

.security-category {
  @apply p-6 bg-gradient-to-br from-lime-50 to-emerald-100 dark:from-lime-900/20 dark:to-emerald-900/20 border border-lime-200 dark:border-lime-800 rounded-xl;

  h3 {
    @apply text-lg font-semibold mb-4 text-lime-800 dark:text-lime-300;
  }

  ul {
    @apply space-y-3;

    li {
      @apply flex items-start gap-3;

      &::before {
        content: '🔒';
        @apply mt-1 flex-shrink-0;
      }

      strong {
        @apply text-lime-700 dark:text-lime-400;
      }
    }
  }
}

.security-notice {
  @apply p-6 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-xl;

  h3 {
    @apply text-lg font-semibold mb-3 text-orange-800 dark:text-orange-300;
  }

  p {
    @apply text-orange-700 dark:text-orange-300 mb-4;
  }

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2;

      &::before {
        content: '⚡';
        @apply text-orange-600 dark:text-orange-400 mt-1 flex-shrink-0;
      }
    }
  }
}

// Retention Table
.retention-table {
  @apply space-y-4;
}

.retention-row {
  @apply grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg;
}

.data-type {
  @apply font-semibold text-neutral-900 dark:text-white;
}

.retention-period {
  @apply text-sky-600 dark:text-sky-400 font-medium;
}

.retention-reason {
  @apply text-neutral-600 dark:text-neutral-300 text-sm;
}

// Rights Section
.rights-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.right-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl text-center hover:shadow-lg transition-all duration-300;

  &:hover {
    transform: translateY(-2px);
  }
}

.right-icon {
  @apply text-3xl mb-3 block;
}

.right-action {
  @apply mt-4 px-4 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-colors duration-200 text-sm font-medium;
}

.rights-exercise {
  @apply mt-8 p-6 bg-neutral-50 dark:bg-neutral-800 rounded-xl;

  h3 {
    @apply text-lg font-semibold mb-6 text-neutral-900 dark:text-white;
  }
}

.exercise-steps {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.step {
  @apply flex flex-col items-center text-center;
}

.step-number {
  @apply w-12 h-12 bg-sky-600 text-white rounded-full flex items-center justify-center font-bold text-lg mb-4;
}

.step-content {
  h4 {
    @apply font-semibold mb-2 text-neutral-900 dark:text-white;
  }

  p {
    @apply text-sm text-neutral-600 dark:text-neutral-300;
  }
}

// Third Party Services
.third-party-services {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.service-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-3 text-neutral-900 dark:text-white;
  }

  p {
    @apply text-sm text-neutral-600 dark:text-neutral-300 mb-2;

    strong {
      @apply text-neutral-900 dark:text-white;
    }

    a {
      @apply text-sky-600 dark:text-sky-400 hover:underline;
    }
  }
}

.embedded-content {
  @apply mt-8 p-6 bg-sky-50 dark:bg-sky-900/20 border border-sky-200 dark:border-sky-800 rounded-xl;

  h3 {
    @apply text-lg font-semibold mb-3 text-sky-800 dark:text-sky-300;
  }

  p {
    @apply text-sky-700 dark:text-sky-300;
  }
}

// Children Protection
.children-protection {
  @apply space-y-8;

  h3 {
    @apply text-xl font-semibold mb-4 text-neutral-900 dark:text-white;
  }
}

.children-policies {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6;
}

.policy-item {
  @apply p-6 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-3 text-purple-800 dark:text-purple-300;
  }

  p {
    @apply text-purple-700 dark:text-purple-300;
  }
}

.parental-notice {
  @apply p-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl;

  h3 {
    @apply text-lg font-semibold mb-3 text-yellow-800 dark:text-yellow-300;
  }

  p {
    @apply text-yellow-700 dark:text-yellow-300 mb-4;
  }
}

.contact-info {
  @apply p-4 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg;

  p {
    @apply text-sm text-yellow-800 dark:text-yellow-300 mb-1;

    strong {
      @apply font-semibold;
    }
  }
}

// Update Process
.update-process {
  @apply mb-8;

  h4 {
    @apply text-lg font-semibold mb-6 text-neutral-900 dark:text-white;
  }
}

.process-steps {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
}

.process-step {
  @apply flex flex-col items-center text-center;
}

.step-info {
  h5 {
    @apply font-semibold mb-2 text-neutral-900 dark:text-white;
  }

  p {
    @apply text-sm text-neutral-600 dark:text-neutral-300;
  }
}

// Version History
.version-history {
  @apply space-y-4;

  h4 {
    @apply text-lg font-semibold mb-4 text-neutral-900 dark:text-white;
  }
}

.version-item {
  @apply flex flex-wrap items-center gap-4 p-4 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg;
}

.version-date {
  @apply text-sm text-neutral-500 dark:text-neutral-400 font-mono;
}

.version-number {
  @apply px-3 py-1 bg-sky-100 dark:bg-sky-900/30 text-sky-800 dark:text-sky-300 rounded-full text-sm font-semibold;
}

.version-changes {
  @apply text-sm text-neutral-600 dark:text-neutral-300 flex-1;
}

// Contact Section
.contact-intro {
  @apply mb-8;

  p {
    @apply text-lg text-neutral-700 dark:text-neutral-300;
  }
}

.contact-methods {
  @apply grid grid-cols-1 md:grid-cols-3 gap-6 mb-8;
}

.contact-card {
  @apply p-6 bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-xl;

  h4 {
    @apply text-lg font-semibold mb-4 text-neutral-900 dark:text-white;
  }

  p {
    @apply text-sm text-neutral-600 dark:text-neutral-300 mb-2;

    strong {
      @apply text-neutral-900 dark:text-white;
    }
  }
}

.contact-form-notice {
  @apply p-6 bg-sky-50 dark:bg-sky-900/20 border border-sky-200 dark:border-sky-800 rounded-xl;

  h3 {
    @apply text-lg font-semibold mb-3 text-sky-800 dark:text-sky-300;
  }

  p {
    @apply text-sky-700 dark:text-sky-300 mb-4;
  }

  ul {
    @apply space-y-2;

    li {
      @apply flex items-start gap-2;

      &::before {
        content: '📝';
        @apply mt-1 flex-shrink-0;
      }
    }
  }
}

// Footer
.privacy-footer {
  @apply mt-16 pt-12 border-t border-neutral-200 dark:border-neutral-700;
}

.footer-content {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-8;
}

.footer-summary {
  h3 {
    @apply text-lg font-semibold mb-4 text-neutral-900 dark:text-white;
  }
}

.commitment-grid {
  @apply grid grid-cols-2 gap-4;
}

.commitment-item {
  @apply flex items-center gap-3 p-3 bg-neutral-50 dark:bg-neutral-800 rounded-lg;
}

.commitment-icon {
  @apply text-xl;
}

.footer-meta {
  @apply text-center lg:text-right;

  p {
    @apply text-sm text-neutral-500 dark:text-neutral-400 mb-2;
  }
}

.footer-links {
  @apply flex flex-wrap justify-center lg:justify-end gap-4 mt-4;
}

.footer-link {
  @apply text-sky-600 dark:text-sky-400 hover:text-sky-800 dark:hover:text-sky-300 text-sm font-medium;
}

// Responsive Design
@media (max-width: 768px) {
  .privacy-title {
    @apply text-3xl flex-col gap-2;
  }

  .privacy-meta {
    @apply flex-col gap-3;
  }

  .toc-list {
    @apply grid-cols-1;
  }

  .section-title {
    @apply text-2xl flex-col items-start gap-2;
  }

  .purpose-grid {
    @apply grid-cols-1;
  }

  .rights-grid {
    @apply grid-cols-1;
  }

  .contact-methods {
    @apply grid-cols-1;
  }

  .commitment-grid {
    @apply grid-cols-1;
  }
}

// Print Styles
@media print {
  .privacy-policy-container {
    @apply text-black bg-white;
  }

  .privacy-header {
    @apply bg-white border border-neutral-300;
  }

  .toc-link {
    @apply text-black;
  }

  .section-number {
    @apply bg-neutral-800;
  }
}