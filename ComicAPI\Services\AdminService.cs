
using ComicAPI.Data;
using ComicAPI.Models;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.EntityFrameworkCore;
using ComicAPI.Services;
using ComicAPI.DTOs;
namespace ComicAPI.Services;

public class AdminService
{
    private readonly ComicDbContext _dbContext;
    private readonly UrlService _urlService;
    private readonly ITokenMgr _tokenMgr;
    private readonly EmailSender _emailSender;

    //Contructor
    public AdminService(ComicDbContext db, ITokenMgr tokenMgr, UrlService urlService, EmailSender emailSender)
    {
        _urlService = urlService;
        _dbContext = db;
        _tokenMgr = tokenMgr;
        _emailSender = emailSender;

    }

    public async Task<bool> NotifyNewChapterToFollowers(int comicID, string comicTitle, string comicUrl, string image)
    {


        var comic_id = new Npgsql.NpgsqlParameter("comic_id", comicID);
        var comic_title = new Npgsql.NpgsqlParameter("comic_title", comicTitle);
        var comic_url = new Npgsql.NpgsqlParameter("comic_url", comicUrl);
        var image_ = new Npgsql.NpgsqlParameter("image", image);
        await this._dbContext.Database.ExecuteSqlRawAsync("SELECT app_notify_new_chapter_to_followers(@comic_id, @comic_title, @comic_url, @image)", comic_id, comic_title, comic_url, image_);
        return true;
    }


}

