
using ComicAPI.Services;

namespace ComicAPI.BackroundTask;

// File: IIntervalTask.cs
using System;
using System.Threading;
using System.Threading.Tasks;

public interface IIntervalTask
{
    string Name { get; } // Tên của tác vụ để dễ dàng ghi log/theo dõi
    TimeSpan Interval { get; } // Khoảng thời gian gi<PERSON>a các lần chạy
    Task ExecuteAsync(CancellationToken stoppingToken); // Phương thức thực hiện công việc
}

public class ComicUpdateTask : IIntervalTask
{
    private readonly IComicService _comicService;
    private readonly ILogger<ComicUpdateTask> _logger;

    public ComicUpdateTask(IComicService comicService, ILogger<ComicUpdateTask> logger)
    {
        _comicService = comicService;
        _logger = logger;
    }

    public string Name => "ComicUpdateTask";

    public TimeSpan Interval => TimeSpan.FromMinutes(1);
    public async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(Interval, stoppingToken);
                await _comicService.SyncViewComic();
                await _comicService.SyncViewChapter();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing ComicUpdateTask");
        }
    }
}

public class UserUpdateTask : IIntervalTask
{
    private readonly IUserService _userService;
    private readonly ILogger<UserUpdateTask> _logger;

    public UserUpdateTask(IUserService userService, ILogger<UserUpdateTask> logger)
    {
        _userService = userService;
        _logger = logger;
    }

    public string Name => "UserUpdateTask";

    public TimeSpan Interval => TimeSpan.FromMinutes(1);

    public async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(Interval, stoppingToken);
                await _userService.SyncUserExp();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing UserUpdateTask");
        }
    }
}


public class QuestUpdateTask : IIntervalTask
{
    // private readonly IQuestRepository _questRepository;
    // private readonly ILogger<QuestUpdateTask> _logger;

    // public QuestUpdateTask(IQuestRepository questRepository, ILogger<QuestUpdateTask> logger)
    // {
    //     _questRepository = questRepository;
    //     _logger = logger;
    // }

    public string Name => "QuestUpdateTask";

    public TimeSpan Interval => TimeSpan.FromMinutes(1);

    public async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        try
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(Interval, stoppingToken);
                // await _questRepository.ExpireOldQuestsAsync();
            }
        }
        catch (Exception ex)
        {
            // _logger.LogError(ex, "Error executing QuestUpdateTask");
        }
    }
}


public class TimeBackgroundService : BackgroundService
{
    private readonly IHostApplicationLifetime _appLifetime;
    private ulong tick = 0;
    private readonly IServiceProvider _serviceProvider;
    private List<XTask> _updaters = new List<XTask>();
    private readonly ILogger<TimeBackgroundService> _logger;

    private readonly TimeSpan _interval;
    public TimeBackgroundService(IServiceProvider services, IBackgroundTaskQueue taskQueue, ILogger<TimeBackgroundService> logger)
    {
        _serviceProvider = services;
        _interval = TimeSpan.FromSeconds(1); // thời gian chạy task định kỳ
        _logger = logger;
        _appLifetime = services.GetRequiredService<IHostApplicationLifetime>();

    }
    private ulong GetTick()
    {
        return tick + 1;
    }
    public override Task StartAsync(CancellationToken cancellationToken)
    {
        Init();
        _appLifetime.ApplicationStopping.Register(OnStopping);
        base.StartAsync(cancellationToken);
        return Task.CompletedTask;
    }

    private void OnStopping()
    {
        // Thực hiện cleanup: đóng DB, flush log, v.v.
        _logger.LogInformation("Unified Background Service is stopping.");


    }
    private void Update(object? state) // Call every 1 second
    {
        tick = GetTick();
        // Console.WriteLine(tick);
        //Implement Task Update Here
        for (int i = 0; i < _updaters.Count; i++)
        {
            _updaters[i].Update(tick);
        }
    }

    void Init()
    {

        var tasks = new XTask(second: 60);
        tasks.Register(UpdateViewChapter);
        tasks.Register(UpdateExp);

        var tasks2 = new XTask(second: 60);
        tasks2.Register(UpdateViewComic);
        AddUpdater(tasks);
        AddUpdater(tasks2);



    }

    async void UpdateViewComic()
    {

        using (var scope = _serviceProvider.CreateScope())
        {
            var comicService = scope.ServiceProvider.GetRequiredService<IComicService>();
            await comicService.SyncViewComic();

        }

    }
    async void UpdateViewChapter()
    {

        using (var scope = _serviceProvider.CreateScope())
        {
            var comicService = scope.ServiceProvider.GetRequiredService<IComicService>();
            await comicService.SyncViewChapter();

        }

    }
    async void UpdateExp()
    {
        using (var scope = _serviceProvider.CreateScope())
        {
            var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
            await userService.SyncUserExp();
        }

    }
    public void AddUpdater(XTask updater)
    {
        _updaters.Add(updater);
    }

    public void RemoveUpdater(XTask updater)
    {
        _updaters.Remove(updater);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Unified Background Service is starting.");
        await RunIntervalTaskAsync(stoppingToken);
        _logger.LogInformation("Unified Background Service is stopping.");
    }

    private async Task RunIntervalTaskAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                this.Update(tick);
                await Task.Delay(_interval, stoppingToken);

            }
            catch (TaskCanceledException ex)
            {
                _logger.LogInformation("Task canceled");

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "");
            }

        }


    }
}