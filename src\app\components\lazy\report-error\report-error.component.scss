

// Overlay
.report-overlay {
  @apply fixed inset-0 bg-black/60 backdrop-blur-sm z-50;
  @apply flex items-center justify-center p-4;
}

// Modal
.report-modal {
  @apply bg-white dark:bg-neutral-800 rounded-2xl shadow-2xl;
  @apply max-w-lg w-full max-h-[90vh] overflow-hidden;
  @apply border border-neutral-200 dark:border-neutral-700;
}

// Header
.report-header {
  @apply relative p-6 border-b border-neutral-200 dark:border-neutral-700;
  @apply bg-primary-100 text-white;
}


.header-content {
  @apply text-center;
}

.header-title {
  @apply text-xl font-bold mb-2;
}

.header-subtitle {
  @apply text-sm opacity-90;
}

.report-close {
  @apply absolute top-4 right-4 w-8 h-8 rounded-full;
  @apply bg-white/20 hover:bg-white/30 transition-colors;
  @apply flex items-center justify-center border-none cursor-pointer;

  svg {
    @apply w-5 h-5;
  }

  &:focus {
    @apply outline-none ring-2 ring-white/50;
  }
}

// Content
.report-content {
  @apply p-6;
}

.report-form {
  @apply space-y-6;
}

// Form Groups
.form-group {
  @apply space-y-3;
}

.form-label {
  @apply flex items-center gap-2 text-sm font-medium;
  @apply text-neutral-700 dark:text-neutral-300;
}

.label-icon {
  @apply w-4 h-4 text-neutral-500 dark:text-neutral-400;
}

.form-input,
.form-textarea {
  @apply w-full px-4 py-3 rounded-xl border border-neutral-300 dark:border-neutral-600;
  @apply bg-neutral-50 dark:bg-neutral-700 text-neutral-900 dark:text-white;
  @apply focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500;
  @apply transition-all duration-200 placeholder-neutral-500 dark:placeholder-neutral-400;

  &:hover {
    @apply border-neutral-400 dark:border-neutral-500;
  }
}

.form-textarea {
  @apply resize-none;
}

// Error Messages
.error-message {
  @apply flex items-center gap-2 text-sm text-red-600 dark:text-red-400;
  @apply bg-red-50 dark:bg-red-900/20 px-3 py-2 rounded-lg;

  svg {
    @apply w-4 h-4 flex-shrink-0;
  }
}

// Quick Tags
.quick-tags {
  @apply space-y-3;
}

.tags-label {
  @apply text-sm font-medium text-neutral-700 dark:text-neutral-300;
}

.tags-container {
  @apply flex flex-wrap gap-2;
}

.tag-button {
  @apply px-3 py-2 rounded-lg border border-neutral-300 dark:border-neutral-600;
  @apply bg-neutral-50 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
  @apply hover:bg-orange-50 dark:hover:bg-orange-900/20;
  @apply hover:border-orange-300 dark:hover:border-orange-600;
  @apply hover:text-orange-700 dark:hover:text-orange-300;
  @apply transition-all duration-200 text-sm font-medium cursor-pointer;

  &:focus {
    @apply outline-none ring-2 ring-orange-500/50;
  }
}

// Actions
.form-actions {
  @apply flex gap-3 pt-4 border-t border-neutral-200 dark:border-neutral-700;
}

.action-button {
  @apply flex-1 flex items-center justify-center gap-2 px-4 py-3;
  @apply font-medium rounded-xl transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;

  svg {
    @apply w-5 h-5;
  }

  &--primary {
    @apply bg-primary-100 text-white;
    @apply hover:bg-primary-200;

    &:disabled {
      @apply transform-none hover:shadow-lg;
    }
  }

  &--secondary {
    @apply bg-neutral-200 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
    @apply hover:bg-neutral-300 dark:hover:bg-neutral-600;
  }
}
