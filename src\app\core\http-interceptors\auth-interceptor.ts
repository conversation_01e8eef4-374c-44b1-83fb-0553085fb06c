import { isPlatformBrowser } from "@angular/common";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from "@angular/common/http";
import { Inject, Injectable, PLATFORM_ID, REQUEST } from "@angular/core";
import { AccountService } from "@services/account.service";
import { LoadingService } from "@services/loading.service";
import { Request } from "express";
import { catchError, map, throwError } from "rxjs";

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

    constructor(private auth: AccountService, private httpService: LoadingService,
        @Inject(PLATFORM_ID) private platformId: object,
        @Inject(REQUEST) private request: Request,
    ) { }

    intercept(req: HttpRequest<any>, next: HttpHandler) {
        // Get the auth token from the service.
        const authToken = this.auth.getAuthorizationToken();
        if (authToken) {
            req = req.clone({
                headers: req.headers.set('Authorization', `Bearer ${authToken}`),
            });
        }

        if (isPlatformBrowser(this.platformId)) {
            if (req.method === 'GET') {
                this.httpService.addTask(req.url);
            }

            return next
                .handle(req)
                .pipe(
                    catchError(err => {
                        if (req.method === 'GET') {
                            this.httpService.removeTask(req.url);
                        }
                        return throwError(() => err);
                    }),

                    map(event => {
                        if (event instanceof HttpResponse) {
                            if (req.method === 'GET') {
                                this.httpService.removeTask(req.url);
                            }
                        }
                        return event;
                    })
                )
        }

        return next.handle(req);

    }
}