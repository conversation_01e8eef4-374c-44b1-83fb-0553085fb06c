// ===== SYNC FORM COMPONENT =====
// Beautiful step-by-step form with modern design

// ===== CONTAINER =====
.sync-form-container {
  @apply max-w-4xl mx-auto p-4;
}

// ===== FORM STEPS =====
.form-step {
  @apply bg-white dark:bg-neutral-800 rounded-2xl p-8 
         border border-gray-200 dark:border-neutral-700 
         shadow-lg space-y-8 transition-all duration-500;
}

// ===== STEP HEADER =====
.step-header {
  @apply flex items-center gap-6 pb-6 border-b border-gray-200 dark:border-neutral-700;
}

.step-icon {
  @apply w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 
         rounded-2xl flex items-center justify-center shadow-lg flex-shrink-0;
}

.step-icon .icon {
  @apply w-8 h-8 text-white;
  fill: currentColor;
}

.step-content {
  @apply flex-1 space-y-2;
}

.step-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.step-subtitle {
  @apply text-gray-600 dark:text-gray-400;
}

// ===== SOURCE SELECTION =====
.source-selection {
  @apply space-y-8;
}

.source-group {
  @apply space-y-4;
}

.source-label {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.source-options {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.source-option {
  @apply p-6 border-2 border-gray-200 dark:border-neutral-700 
         rounded-xl cursor-pointer transition-all duration-200 
         hover:border-blue-300 dark:hover:border-blue-600 
         hover:shadow-md;

  &.selected {
    @apply border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md;
  }

  &.disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

.option-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-3 text-center;

  &[data-color="yellow"] {
    @apply bg-yellow-100 dark:bg-yellow-900/30;
    
    .icon {
      @apply text-yellow-600 dark:text-yellow-400;
    }
  }

  &[data-color="cyan"] {
    @apply bg-cyan-100 dark:bg-cyan-900/30;
    
    .icon {
      @apply text-cyan-600 dark:text-cyan-400;
    }
  }
}

.option-icon .icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.option-content {
  @apply space-y-1;
}

.option-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.option-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// ===== SYNC ARROW =====
.sync-arrow {
  @apply flex justify-center py-4;
}

.arrow-icon {
  @apply w-8 h-8 text-blue-500 dark:text-blue-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== METHOD SELECTION =====
.method-selection {
  @apply space-y-6;
}

.method-options {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.method-option {
  @apply p-6 border-2 border-gray-200 dark:border-neutral-700
         rounded-xl cursor-pointer transition-all duration-200
         hover:border-blue-300 dark:hover:border-blue-600
         hover:shadow-md;

  &.selected {
    @apply border-blue-500 bg-blue-50 dark:bg-blue-900/20 shadow-md;
  }
}

.method-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center mb-3;

  &[data-icon="user"] {
    @apply bg-green-100 dark:bg-green-900/30;

    .icon {
      @apply text-green-600 dark:text-green-400;
    }
  }

  &[data-icon="code"] {
    @apply bg-purple-100 dark:bg-purple-900/30;

    .icon {
      @apply text-purple-600 dark:text-purple-400;
    }
  }
}

.method-icon .icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.method-content {
  @apply space-y-1;
}

.method-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.method-description {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// ===== METHOD INFO =====
.method-info {
  @apply mt-6;
}

.info-card {
  @apply flex items-start gap-4 p-4
         bg-blue-50 dark:bg-blue-900/20
         border border-blue-200 dark:border-blue-800
         rounded-lg;
}

.info-icon {
  @apply w-10 h-10 bg-blue-100 dark:bg-blue-900/30
         rounded-lg flex items-center justify-center flex-shrink-0;
}

.info-icon .icon {
  @apply w-5 h-5 text-blue-600 dark:text-blue-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.info-content {
  @apply space-y-1;
}

.info-title {
  @apply font-semibold text-gray-900 dark:text-white;
}

.info-text {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// ===== SYNC SUMMARY =====
.sync-summary {
  @apply mt-6;
}

.summary-card {
  @apply flex items-center gap-4 p-4 
         bg-green-50 dark:bg-green-900/20 
         border border-green-200 dark:border-green-800 
         rounded-lg;
}

.summary-icon {
  @apply w-10 h-10 bg-green-100 dark:bg-green-900/30 
         rounded-lg flex items-center justify-center;
}

.summary-icon .icon {
  @apply w-5 h-5 text-green-600 dark:text-green-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.summary-content {
  @apply space-y-1;
}

.summary-title {
  @apply font-semibold text-gray-900 dark:text-white;
}

.summary-text {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

// ===== CREDENTIALS FORM =====
.credentials-form {
  @apply space-y-6;
}

.account-form {
  @apply space-y-6;
}

// ===== SCRIPT FORM =====
.script-form {
  @apply space-y-6;
}

.script-instructions {
  @apply mb-6;
}

.instruction-card {
  @apply p-6 bg-gray-50 dark:bg-neutral-700
         border border-gray-200 dark:border-neutral-600
         rounded-lg space-y-4;
}

.instruction-header {
  @apply flex items-center gap-3;
}

.instruction-icon {
  @apply w-6 h-6 text-blue-600 dark:text-blue-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.instruction-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.instruction-steps {
  @apply space-y-2 pl-4;
}

.instruction-steps li {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.script-code {
  @apply border border-gray-300 dark:border-neutral-600
         rounded-lg overflow-hidden;
}

.script-header {
  @apply flex items-center justify-between p-3
         bg-gray-100 dark:bg-neutral-800
         border-b border-gray-300 dark:border-neutral-600;
}

.script-title {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}

.copy-script-button {
  @apply flex items-center gap-2 px-3 py-1
         text-xs bg-blue-500 hover:bg-blue-600
         text-white rounded transition-colors duration-200;
}

.copy-icon {
  @apply w-3 h-3;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.script-code pre {
  @apply p-4 bg-gray-900 dark:bg-black
         overflow-x-auto m-0;
}

.script-code code {
  @apply text-sm text-green-400 font-mono;
}

.instruction-note {
  @apply text-sm text-gray-600 dark:text-gray-400
         italic border-l-4 border-blue-500 pl-4;
}

.textarea-wrapper {
  @apply relative;
}

.form-textarea {
  @apply w-full px-4 py-3
         border border-gray-300 dark:border-neutral-600
         rounded-lg bg-white dark:bg-neutral-700
         text-gray-900 dark:text-white
         placeholder-gray-500 dark:placeholder-gray-400
         focus:ring-2 focus:ring-blue-500 focus:border-blue-500
         transition-all duration-200;
  resize: vertical;

  &.error {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
  }
}

.form-group {
  @apply space-y-2;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.input-wrapper {
  @apply relative;
}

.form-input {
  @apply w-full px-4 py-3 pl-12 pr-12 
         border border-gray-300 dark:border-neutral-600 
         rounded-lg bg-white dark:bg-neutral-700 
         text-gray-900 dark:text-white 
         placeholder-gray-500 dark:placeholder-gray-400 
         focus:ring-2 focus:ring-blue-500 focus:border-blue-500 
         transition-all duration-200;

  &.error {
    @apply border-red-500 focus:ring-red-500 focus:border-red-500;
  }
}

.input-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 
         w-5 h-5 text-gray-400 dark:text-gray-500;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.input-action {
  @apply absolute right-3 top-1/2 transform -translate-y-1/2 
         p-1 text-gray-400 dark:text-gray-500 
         hover:text-gray-600 dark:hover:text-gray-300 
         transition-colors duration-200;
}

.action-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.form-error {
  @apply text-sm text-red-600 dark:text-red-400;
}

// ===== TEST BUTTON =====
.test-button {
  @apply flex items-center gap-2 px-4 py-2 
         bg-blue-100 dark:bg-blue-900/30 
         text-blue-700 dark:text-blue-300 
         border border-blue-200 dark:border-blue-800 
         rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 
         transition-all duration-200 disabled:opacity-50;
}

.test-button .button-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.test-button .button-text {
  @apply text-sm font-medium;
}

// ===== VALIDATION SUCCESS =====
.validation-success {
  @apply flex items-center gap-2 p-3 
         bg-green-50 dark:bg-green-900/20 
         border border-green-200 dark:border-green-800 
         rounded-lg;
}

.success-icon {
  @apply w-5 h-5 text-green-600 dark:text-green-400;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.success-text {
  @apply text-sm font-medium text-green-700 dark:text-green-300;
}


// ===== STEP ACTIONS =====
.step-actions {
  @apply flex flex-col sm:flex-row gap-3 justify-end pt-6 
         border-t border-gray-200 dark:border-neutral-700;
}

.action-button {
  @apply flex items-center justify-center gap-2 px-6 py-3 
         font-semibold rounded-lg transition-all duration-200 
         disabled:opacity-50 disabled:cursor-not-allowed;

  &.primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 
           hover:from-blue-600 hover:to-purple-700 
           text-white shadow-lg hover:shadow-xl;
  }

  &.secondary {
    @apply bg-gray-100 hover:bg-gray-200 
           dark:bg-neutral-700 dark:hover:bg-neutral-600 
           text-gray-700 dark:text-gray-300 
           border border-gray-300 dark:border-neutral-600;
  }

  &.tertiary {
    @apply bg-transparent hover:bg-gray-50 dark:hover:bg-neutral-800 
           text-gray-600 dark:text-gray-400 
           border border-gray-300 dark:border-neutral-600;
  }
}

.button-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.button-text {
  @apply font-medium;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .sync-form-container {
    @apply p-4;
  }

  .form-step {
    @apply p-6 space-y-6;
  }

  .step-header {
    @apply flex-col items-start gap-4;
  }

  .source-options {
    @apply grid-cols-1;
  }

  .settings-grid {
    @apply grid-cols-1 gap-4;
  }

  .step-actions {
    @apply flex-col;
  }

  .action-button {
    @apply w-full;
  }
}
