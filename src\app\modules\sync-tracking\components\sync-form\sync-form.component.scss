// ===== SYNC FORM COMPONENT =====
// Modern, beautiful UI with glassmorphism effects

// ===== CONTAINER =====
.sync-form-container {
  @apply max-w-6xl mx-auto p-6 space-y-8;
}

// ===== HEADER =====
.sync-form-header {
  @apply text-center mb-8;
}

.header-content {
  @apply flex flex-col items-center space-y-4;
}

.header-icon {
  @apply w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 
         rounded-2xl flex items-center justify-center shadow-lg;
}

.icon-sync {
  @apply w-8 h-8 text-white;
  fill: currentColor;
}

.header-text {
  @apply space-y-2;
}

.header-title {
  @apply text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 
         bg-clip-text text-transparent;
}

.header-subtitle {
  @apply text-lg text-gray-600 dark:text-gray-400 max-w-2xl;
}

// ===== FORM SECTIONS =====
.form-section {
  @apply bg-white dark:bg-neutral-800 rounded-2xl p-6 lg:p-8 
         border border-gray-200 dark:border-neutral-700 
         shadow-lg backdrop-blur-sm;
}

.section-header {
  @apply mb-6 text-center lg:text-left;
}

.section-title {
  @apply text-2xl font-bold text-gray-900 dark:text-white mb-2;
}

.section-subtitle {
  @apply text-gray-600 dark:text-gray-400;
}

// ===== CREDENTIALS GRID =====
.credentials-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-6;
}

.credential-card {
  @apply bg-gradient-to-br from-gray-50 to-gray-100 
         dark:from-neutral-700 dark:to-neutral-800 
         rounded-xl p-6 border border-gray-200 dark:border-neutral-600
         transition-all duration-300 hover:shadow-md;
}

.card-header {
  @apply flex items-center justify-between mb-6;
}

.site-logo {
  @apply w-12 h-12 rounded-lg flex items-center justify-center;
  
  &.nettruyen-logo {
    @apply bg-gradient-to-br from-green-500 to-emerald-600;
  }
  
  &.truyenqq-logo {
    @apply bg-gradient-to-br from-orange-500 to-red-600;
  }
}

.logo-icon {
  @apply w-6 h-6 text-white;
  fill: currentColor;
}

.site-info {
  @apply flex-1 ml-4;
}

.site-name {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.site-url {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.validation-status {
  @apply w-8 h-8 rounded-full bg-gray-200 dark:bg-neutral-600 
         flex items-center justify-center transition-all duration-300;
  
  &.valid {
    @apply bg-green-100 dark:bg-green-900;
  }
}

.status-icon {
  @apply w-5 h-5;
  
  &.valid {
    @apply text-green-600 dark:text-green-400;
  }
  
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== FORM FIELDS =====
.form-fields {
  @apply space-y-4;
}

.field-group {
  @apply space-y-2;
}

.field-label {
  @apply block text-sm font-semibold text-gray-700 dark:text-gray-300;
}

.input-wrapper {
  @apply relative;
}

.form-input {
  @apply w-full px-4 py-3 pl-12 pr-12 
         bg-white dark:bg-neutral-700 
         border border-gray-300 dark:border-neutral-600 
         rounded-lg text-gray-900 dark:text-white 
         placeholder-gray-500 dark:placeholder-gray-400
         focus:ring-2 focus:ring-blue-500 focus:border-transparent
         transition-all duration-200;
  
  &.error {
    @apply border-red-500 focus:ring-red-500;
  }
}

.input-icon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 
         w-5 h-5 text-gray-400 dark:text-gray-500;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.input-action {
  @apply absolute right-4 top-1/2 transform -translate-y-1/2 
         w-5 h-5 text-gray-400 hover:text-gray-600 
         dark:text-gray-500 dark:hover:text-gray-300
         transition-colors duration-200;
}

.action-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.field-error {
  @apply text-sm text-red-600 dark:text-red-400 mt-1;
}

// ===== TEST BUTTON =====
.test-button {
  @apply w-full flex items-center justify-center gap-2 px-4 py-3 
         bg-blue-50 hover:bg-blue-100 dark:bg-blue-900/20 dark:hover:bg-blue-900/30 
         text-blue-600 dark:text-blue-400 
         border border-blue-200 dark:border-blue-800 
         rounded-lg font-medium
         disabled:opacity-50 disabled:cursor-not-allowed
         transition-all duration-200;
}

.button-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.button-text {
  @apply font-medium;
}

// ===== SETTINGS GRID =====
.settings-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.setting-item {
  @apply space-y-3;
}

.setting-label {
  @apply flex items-start gap-3 cursor-pointer;
}

.setting-checkbox {
  @apply sr-only;
}

.checkbox-custom {
  @apply w-5 h-5 mt-0.5 bg-white dark:bg-neutral-700 
         border-2 border-gray-300 dark:border-neutral-600 
         rounded flex items-center justify-center
         transition-all duration-200;
  
  .setting-checkbox:checked + & {
    @apply bg-blue-500 border-blue-500;
  }
}

.checkbox-icon {
  @apply w-3 h-3 text-white opacity-0 transition-opacity duration-200;
  
  .setting-checkbox:checked + .checkbox-custom & {
    @apply opacity-100;
  }
  
  fill: none;
  stroke: currentColor;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.setting-content {
  @apply flex-1 space-y-1;
}

.setting-title {
  @apply block font-semibold text-gray-900 dark:text-white;
}

.setting-description {
  @apply block text-sm text-gray-600 dark:text-gray-400;
}

.setting-select {
  @apply w-full px-4 py-3 
         bg-white dark:bg-neutral-700 
         border border-gray-300 dark:border-neutral-600 
         rounded-lg text-gray-900 dark:text-white
         focus:ring-2 focus:ring-blue-500 focus:border-transparent
         transition-all duration-200;
}

// ===== ACTION BUTTONS =====
.form-actions {
  @apply flex flex-col sm:flex-row gap-4 justify-end mt-8;
}

.action-button {
  @apply flex items-center justify-center gap-2 px-6 py-3 
         font-semibold rounded-lg 
         disabled:opacity-50 disabled:cursor-not-allowed
         transition-all duration-200;
  
  &.primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 
           hover:from-blue-600 hover:to-purple-700 
           text-white shadow-lg hover:shadow-xl;
  }
  
  &.secondary {
    @apply bg-gray-100 hover:bg-gray-200 
           dark:bg-neutral-700 dark:hover:bg-neutral-600 
           text-gray-700 dark:text-gray-300 
           border border-gray-300 dark:border-neutral-600;
  }
  
  &.tertiary {
    @apply bg-transparent hover:bg-gray-50 dark:hover:bg-neutral-800 
           text-gray-600 dark:text-gray-400 
           border border-gray-300 dark:border-neutral-600;
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .sync-form-container {
    @apply p-4 space-y-6;
  }
  
  .form-section {
    @apply p-4;
  }
  
  .credentials-grid {
    @apply grid-cols-1 gap-4;
  }
  
  .settings-grid {
    @apply grid-cols-1 gap-4;
  }
  
  .form-actions {
    @apply flex-col;
  }
  
  .action-button {
    @apply w-full;
  }
}
