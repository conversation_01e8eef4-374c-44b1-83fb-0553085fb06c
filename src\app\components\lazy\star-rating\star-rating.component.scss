
.rating-overlay {
  @apply fixed inset-0 bg-black/60 backdrop-blur-sm z-50;
  @apply flex items-center justify-center p-4;
}

.rating-modal {
  @apply bg-white dark:bg-neutral-800 rounded-2xl shadow-2xl;
  @apply max-w-md w-full max-h-[90vh] overflow-hidden;
  @apply border border-neutral-200 dark:border-neutral-700;
  animation: slideUp 0.3s ease-out;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);

  .dark & {
    background: rgba(31, 41, 55, 0.95);
  }
}

// Header
.rating-header {
  @apply relative p-6 text-center border-b border-neutral-100 dark:border-neutral-700;
  @apply bg-primary-100 text-white;
}

.rating-title {
  @apply text-xl font-bold mb-2;
}

.rating-subtitle {
  @apply text-sm opacity-90;
}

.rating-close {
  @apply absolute top-4 right-4 w-8 h-8 rounded-full;
  @apply bg-white/20 hover:bg-white/30 transition-colors;
  @apply flex items-center justify-center;

  svg {
    @apply w-5 h-5;
  }
}

// Content
.rating-content {
  @apply p-6 space-y-6;
}

.star-rating-container {
  @apply text-center space-y-4;
}

.star-rating-grid {
  @apply flex justify-center gap-2;
}

.star-button {
  @apply relative w-12 h-12 rounded-full transition-all duration-300;
  @apply hover:scale-110 focus:outline-none focus:ring-2 focus:ring-yellow-400;
  @apply bg-neutral-100 dark:bg-neutral-700 hover:bg-yellow-50 dark:hover:bg-yellow-900/20;

  &.star-active {
    @apply bg-yellow-100 dark:bg-yellow-900/30;

    .star-icon {
      @apply text-yellow-500 scale-110;
    }

    .star-glow {
      @apply opacity-100;
    }
  }

  &:hover {
    @apply shadow-lg;

    .star-icon {
      @apply scale-105;
    }
  }
}

.star-icon {
  @apply w-8 h-8 text-neutral-300 dark:text-neutral-600 transition-all duration-300;
  @apply absolute inset-0 m-auto;
}

.star-glow {
  @apply absolute inset-0 rounded-full opacity-0 transition-opacity duration-300;
  @apply bg-gradient-to-r from-yellow-400 to-orange-400;
  filter: blur(8px);
  z-index: -1;
}

.rating-text {
  @apply space-y-1;
}

.rating-score {
  @apply block text-3xl font-bold text-neutral-900 dark:text-white;
}

.rating-label {
  @apply block text-lg font-medium text-yellow-600 dark:text-yellow-400;
}

.rating-description {
  @apply text-center;

  p {
    @apply text-neutral-600 dark:text-neutral-400 leading-relaxed;
  }
}

// Actions
.rating-actions {
  @apply flex gap-3 p-6 bg-neutral-50 dark:bg-neutral-900/50;
}

.action-button {
  @apply flex-1 flex items-center justify-center gap-2 px-4 py-3;
  @apply font-medium rounded-xl transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;

  svg {
    @apply w-5 h-5;
  }

  &--primary {
    @apply bg-primary-100 text-white;
    @apply hover:bg-primary-200;
    @apply transform hover:scale-105;
  }

  &--secondary {
    @apply bg-neutral-200 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
    @apply hover:bg-neutral-300 dark:hover:bg-neutral-600;
  }
}
