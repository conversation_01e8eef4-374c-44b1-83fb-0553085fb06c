<div class="equipped-items-container">
  <!-- Header -->
  <div class="equipped-header">
    <h3 class="equipped-title">Trang bị hiện tại</h3>
    <p class="equipped-subtitle"><PERSON><PERSON><PERSON> vật phẩm đang được trang bị</p>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading()">
    <div class="loading-spinner"></div>
    <p>Đang tải trang bị...</p>
  </div>

  <!-- Equipment Slots -->
  <div class="equipment-slots" *ngIf="!isLoading()">
    <div 
      *ngFor="let slot of equipmentSlots; trackBy: trackBySlotType"
      class="equipment-slot"
      [class.equipment-slot--empty]="!getEquippedItemForSlot(slot.slotType)"
      (click)="onSlotClick(slot.slotType)"
    >
      <!-- Slot Header -->
      <div class="slot-header">
        <div class="slot-icon">
          <i [class]="'icon-' + slot.icon"></i>
        </div>
        <div class="slot-info">
          <h4 class="slot-label">{{ slot.label }}</h4>
          <p class="slot-description">{{ slot.description }}</p>
        </div>
      </div>

      <!-- Equipped Item -->
      <div class="slot-content">
        <div 
          class="equipped-item" 
          *ngIf="getEquippedItemForSlot(slot.slotType) as equippedItem; else emptySlot"
        >
          <app-item-card
            [item]="getDisplayInfoForEquippedItem(equippedItem)"
            [size]="'medium'"
            [showActions]="true"
            [showQuantity]="false"
            (actionClicked)="onItemAction($event)"
          ></app-item-card>
        </div>

        <ng-template #emptySlot>
          <div class="empty-slot">
            <div class="empty-slot-icon">
              <i [class]="'icon-' + slot.icon"></i>
            </div>
            <p class="empty-slot-text">Chưa trang bị</p>
            <button class="empty-slot-btn" (click)="onSlotClick(slot.slotType)">
              Chọn vật phẩm
            </button>
          </div>
        </ng-template>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!isLoading() && equippedItems().length === 0">
    <div class="empty-state__icon">
      <i class="icon-empty-equipment"></i>
    </div>
    <h3 class="empty-state__title">Chưa có trang bị</h3>
    <p class="empty-state__description">
      Bạn chưa trang bị vật phẩm nào. Hãy vào kho đồ để trang bị!
    </p>
    <button class="empty-state__btn" routerLink="/tai-khoan/inventory">
      Mở kho đồ
    </button>
  </div>
</div>
