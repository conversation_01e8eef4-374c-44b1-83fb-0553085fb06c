import { DOCUMENT } from '@angular/common';
import { Inject, Injectable } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';


@Injectable({
  providedIn: 'root',
})
export class SeoService {
  constructor(
    private title: Title,
    private meta: Meta,
    @Inject(DOCUMENT) private document: Document
  ) { }
  // addSchema(schema: any) {
  //   if (isPlatformServer(this.platformId)) {
  //     let script = this.document.createElement('script');
  //     script.type = 'application/ld+json';
  //     script.innerHTML = JSON.stringify(schema);
  //     this.document.head.appendChild(script);
  //   }
  // }

  setTitle(title: string) {
    this.title.setTitle(title);
  }

  addTags(tags: MetaDefinition[]) {
    this.meta.addTags(tags);
  }

  updateTag(tag: MetaDefinition) {
    this.meta.updateTag(tag);
  }

  updateLink(ref: string, href: string) {
    const head = this.document.head;
    let element: HTMLLinkElement | null = this.document.querySelector(
      `link[rel='${ref}']`
    );
    if (element == null) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }
    element.setAttribute('rel', ref);
    element.setAttribute('href', href);
  }
}
