import { DOCUMENT, isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { Meta, MetaDefinition, Title } from '@angular/platform-browser';
import { Router } from '@angular/router';

export interface SEOData {
  title: string;
  description: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'book' | 'video';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  section?: string;
  tags?: string[];
  locale?: string;
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  canonical?: string;
  noindex?: boolean;
  nofollow?: boolean;
}

@Injectable({
  providedIn: 'root',
})
export class SeoService {
  private readonly baseUrl = 'https://metruyenmoi.com';
  private readonly siteName = 'Mê Truyện Mới';
  private readonly defaultImage = '/assets/images/og-default.jpg';

  constructor(
    private title: Title,
    private meta: Meta,
    private router: Router,
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: object
  ) { }

  /**
   * Set comprehensive SEO data for a page
   */
  setSEOData(data: SEOData): void {
    // Set title
    const fullTitle = data.title ? `${data.title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);

    // Get current URL
    const currentUrl = data.url || `${this.baseUrl}${this.router.url}`;

    // Basic meta tags
    this.updateTag({ name: 'description', content: data.description });
    this.updateTag({ name: 'keywords', content: data.keywords || this.getDefaultKeywords() });
    this.updateTag({ name: 'author', content: data.author || this.siteName });

    // Robots meta
    const robotsContent = this.getRobotsContent(data.noindex, data.nofollow);
    this.updateTag({ name: 'robots', content: robotsContent });

    // Open Graph tags
    this.updateTag({ property: 'og:title', content: data.title || this.siteName });
    this.updateTag({ property: 'og:description', content: data.description });
    this.updateTag({ property: 'og:type', content: data.type || 'website' });
    this.updateTag({ property: 'og:url', content: currentUrl });
    this.updateTag({ property: 'og:image', content: data.image || `${this.baseUrl}${this.defaultImage}` });
    this.updateTag({ property: 'og:site_name', content: data.siteName || this.siteName });
    this.updateTag({ property: 'og:locale', content: data.locale || 'vi_VN' });

    // Article specific tags
    if (data.type === 'article' || data.type === 'book') {
      if (data.publishedTime) {
        this.updateTag({ property: 'article:published_time', content: data.publishedTime });
      }
      if (data.modifiedTime) {
        this.updateTag({ property: 'article:modified_time', content: data.modifiedTime });
      }
      if (data.section) {
        this.updateTag({ property: 'article:section', content: data.section });
      }
      if (data.tags) {
        data.tags.forEach(tag => {
          this.addTag({ property: 'article:tag', content: tag });
        });
      }
    }

    // Twitter Card tags
    this.updateTag({ name: 'twitter:card', content: data.twitterCard || 'summary_large_image' });
    this.updateTag({ name: 'twitter:title', content: data.title || this.siteName });
    this.updateTag({ name: 'twitter:description', content: data.description });
    this.updateTag({ name: 'twitter:image', content: data.image || `${this.baseUrl}${this.defaultImage}` });

    // Canonical URL
    this.updateCanonical(data.canonical || currentUrl);

    // Additional meta tags for better SEO
    this.updateTag({ name: 'theme-color', content: '#3B82F6' });
    this.updateTag({ name: 'msapplication-TileColor', content: '#3B82F6' });
    this.updateTag({ name: 'apple-mobile-web-app-capable', content: 'yes' });
    this.updateTag({ name: 'apple-mobile-web-app-status-bar-style', content: 'default' });
  }

  /**
   * Add structured data (JSON-LD)
   */
  addStructuredData(schema: any): void {
    if (isPlatformBrowser(this.platformId)) {
      // Remove existing structured data
      this.removeStructuredData();

      const script = this.document.createElement('script');
      script.type = 'application/ld+json';
      script.text = JSON.stringify(schema);
      script.id = 'structured-data';
      this.document.head.appendChild(script);
    }
  }

  /**
   * Remove existing structured data
   */
  private removeStructuredData(): void {
    const existingScript = this.document.getElementById('structured-data');
    if (existingScript) {
      existingScript.remove();
    }
  }

  /**
   * Set page title
   */
  setTitle(title: string): void {
    const fullTitle = title ? `${title} | ${this.siteName}` : this.siteName;
    this.title.setTitle(fullTitle);
  }

  /**
   * Add meta tags
   */
  addTags(tags: MetaDefinition[]): void {
    this.meta.addTags(tags);
  }

  /**
   * Add single meta tag
   */
  addTag(tag: MetaDefinition): void {
    this.meta.addTag(tag);
  }

  /**
   * Update meta tag
   */
  updateTag(tag: MetaDefinition): void {
    this.meta.updateTag(tag);
  }

  /**
   * Update canonical link
   */
  updateCanonical(url: string): void {
    this.updateLink('canonical', url);
  }

  /**
   * Update link element
   */
  updateLink(rel: string, href: string): void {
    const head = this.document.head;
    let element: HTMLLinkElement | null = this.document.querySelector(`link[rel='${rel}']`);

    if (!element) {
      element = this.document.createElement('link') as HTMLLinkElement;
      head.appendChild(element);
    }

    element.setAttribute('rel', rel);
    element.setAttribute('href', href);
  }

  /**
   * Get default keywords
   */
  private getDefaultKeywords(): string {
    return 'truyện tranh, manga, comic, đọc truyện online, truyện tranh hay, manga việt nam, comic việt nam, truyện tranh miễn phí';
  }

  /**
   * Get robots content
   */
  private getRobotsContent(noindex?: boolean, nofollow?: boolean): string {
    const index = noindex ? 'noindex' : 'index';
    const follow = nofollow ? 'nofollow' : 'follow';
    return `${index}, ${follow}`;
  }

  /**
   * Generate breadcrumb structured data
   */
  generateBreadcrumbSchema(breadcrumbs: Array<{name: string, url: string}>): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      'itemListElement': breadcrumbs.map((item, index) => ({
        '@type': 'ListItem',
        'position': index + 1,
        'name': item.name,
        'item': `${this.baseUrl}${item.url}`
      }))
    };
  }

  /**
   * Generate website structured data
   */
  generateWebsiteSchema(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      'name': this.siteName,
      'url': this.baseUrl,
      'description': 'Website đọc truyện tranh online miễn phí với kho tàng truyện tranh phong phú, cập nhật liên tục',
      'potentialAction': {
        '@type': 'SearchAction',
        'target': `${this.baseUrl}/tim-kiem?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      },
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'url': this.baseUrl,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.baseUrl}/assets/images/logo.png`
        }
      }
    };
  }

  /**
   * Generate comic book structured data
   */
  generateComicSchema(comic: any): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Book',
      'name': comic.title,
      'description': comic.description,
      'image': comic.thumbnail,
      'url': `${this.baseUrl}/truyen-tranh/${comic.slug}`,
      'author': {
        '@type': 'Person',
        'name': comic.author || 'Đang cập nhật'
      },
      'genre': comic.genres?.map((g: any) => g.name) || [],
      'bookFormat': 'GraphicNovel',
      'inLanguage': 'vi',
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName
      },
      'datePublished': comic.createdAt,
      'dateModified': comic.updatedAt,
      'aggregateRating': comic.rating ? {
        '@type': 'AggregateRating',
        'ratingValue': comic.rating,
        'ratingCount': comic.ratingCount || 1,
        'bestRating': 5,
        'worstRating': 1
      } : undefined
    };
  }

  /**
   * Generate article structured data for chapters
   */
  generateChapterSchema(comic: any, chapter: any): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      'headline': `${comic.title} - ${chapter.name}`,
      'description': `Đọc ${chapter.name} của truyện ${comic.title} tại ${this.siteName}`,
      'image': comic.thumbnail,
      'url': `${this.baseUrl}/truyen-tranh/${comic.slug}/${chapter.slug}`,
      'datePublished': chapter.createdAt,
      'dateModified': chapter.updatedAt,
      'author': {
        '@type': 'Person',
        'name': comic.author || 'Đang cập nhật'
      },
      'publisher': {
        '@type': 'Organization',
        'name': this.siteName,
        'logo': {
          '@type': 'ImageObject',
          'url': `${this.baseUrl}/assets/images/logo.png`
        }
      },
      'mainEntityOfPage': {
        '@type': 'WebPage',
        '@id': `${this.baseUrl}/truyen-tranh/${comic.slug}/${chapter.slug}`
      },
      'isPartOf': {
        '@type': 'Book',
        'name': comic.title,
        'url': `${this.baseUrl}/truyen-tranh/${comic.slug}`
      }
    };
  }
}
