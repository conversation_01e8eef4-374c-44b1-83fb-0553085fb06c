using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicAPI.Models
{
    public class UserInventory
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, Column("userid")]
        public int UserID { get; set; }

        [Required, Column("itemtemplateid")]
        public int ItemTemplateID { get; set; }

        [Required, Column("quantity")]
        public int Quantity { get; set; } = 1;

        [Column("obtainedat")]
        public DateTime ObtainedAt { get; set; } = DateTime.UtcNow;

        [Column("expiresat")]
        public DateTime? ExpiresAt { get; set; }


        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual ItemTemplate ItemTemplate { get; set; } = null!;
    }
}
