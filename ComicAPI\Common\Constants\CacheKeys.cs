namespace ComicAPI.Common.Constants
{
    /// <summary>
    /// Centralized cache key definitions with consistent naming patterns
    /// </summary>
    public static class CacheKeys
    {
        // Comic related caches
        public const string Comics = "comics:{0}:{1}:{2}:{3}:{4}"; // page:step:genre:status:sort
        public const string Comic = "comic:{0}"; // comicId or key
        public const string ComicsByIds = "comics:ids:{0}"; // comma-separated ids
        public const string Genres = "genres";
        public const string TopViewComics = "top_view_comics:{0}:{1}"; // type:step
        public const string HotComics = "hot_comics:{0}:{1}"; // page:step
        public const string ComicChapters = "comic:chapters:{0}"; // comicId
        public const string ChapterPages = "chapter:pages:{0}"; // chapterId
        public const string ComicSearch = "comic:search:{0}"; // keyword
        public const string SimilarComics = "comic:similar:{0}"; // comicId
        
        // User related caches
        public const string UserProfile = "user:profile:{0}"; // userId
        public const string UserQuests = "user:quests:{0}"; // userId
        public const string UserInventory = "user:inventory:{0}"; // userId
        public const string UserFollowedComics = "user:followed:{0}"; // userId
        
        // System caches
        public const string Announcements = "announcements";
        public const string ComicRecommendations = "comic:recommendations";
        
        /// <summary>
        /// Cache expiration times in minutes
        /// </summary>
        public static class ExpirationTimes
        {
            public const int Short = 5;      // 5 minutes - for frequently changing data
            public const int Medium = 30;    // 30 minutes - for moderately stable data
            public const int Long = 120;     // 2 hours - for stable data
            public const int VeryLong = 720; // 12 hours - for very stable data
        }
        
        /// <summary>
        /// Helper methods for generating cache keys
        /// </summary>
        public static class Helpers
        {
            public static string GetComicsKey(int page, int step, int genre, int status, int sort)
                => string.Format(Comics, page, step, genre, status, sort);
                
            public static string GetComicKey(string comicKey)
                => string.Format(Comic, comicKey);
                
            public static string GetComicsByIdsKey(string ids)
                => string.Format(ComicsByIds, ids);
                
            public static string GetHotComicsKey(int page, int step)
                => string.Format(HotComics, page, step);
                
            public static string GetTopViewComicsKey(string type, int step)
                => string.Format(TopViewComics, type, step);
                
            public static string GetUserProfileKey(int userId)
                => string.Format(UserProfile, userId);
                
            public static string GetUserQuestsKey(int userId)
                => string.Format(UserQuests, userId);
                
            public static string GetComicChaptersKey(int comicId)
                => string.Format(ComicChapters, comicId);
                
            public static string GetChapterPagesKey(int chapterId)
                => string.Format(ChapterPages, chapterId);
                
            public static string GetComicSearchKey(string keyword)
                => string.Format(ComicSearch, keyword.ToLowerInvariant());
                
            public static string GetSimilarComicsKey(int comicId)
                => string.Format(SimilarComics, comicId);
        }
    }
}
