// ===== CHAPTER CONTROLS COMPONENT =====
// Optimized styles using Tailwind @apply

// ===== CONTROL BAR =====
.control-bar {
  @apply relative flex flex-row justify-center w-full items-center z-10 
         bg-white dark:bg-neutral-800 dark:border-neutral-700;
}

.control-bar-content {
  @apply flex justify-center w-full items-center h-full py-2 gap-2;
}

.control-group {
  @apply z-10 flex items-center gap-2;
}

.control-button {
  @apply flex items-center gap-2 px-3 py-2 text-sm font-medium 
         text-gray-600 dark:text-gray-300 
         hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 
         rounded-lg 
         border border-gray-200 dark:border-neutral-600 
         hover:border-primary-100/50
         transition-all duration-200;

  &.control-button-home {
    @apply text-primary-100 border-primary-100/30 bg-primary-100/5;
  }

  &.settings-button {
    @apply bg-gray-50 dark:bg-neutral-700;
  }
}

.control-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== CHAPTER NAVIGATION =====
.chapter-navigation-group {
  @apply flex items-center gap-1 px-4;
}

.nav-button {
  @apply flex items-center gap-2 px-2 py-2 text-sm font-medium 
         text-gray-600 dark:text-gray-300 hover:text-white 
         bg-gray-200 dark:bg-neutral-600 hover:bg-primary-100 
         rounded-lg border-none cursor-pointer 
         disabled:opacity-50 disabled:cursor-not-allowed
         transition-all duration-200;

  &.nav-button-active {
    @apply text-white bg-primary-100;
  }
}

.nav-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chapter-selector-wrapper {
  @apply mx-1;
}

// ===== ZOOM CONTROLS =====
.zoom-group {
  @apply relative;
}

.zoom-button {
  @apply relative;
}

.zoom-panel {
  @apply absolute hidden
         bg-white dark:bg-neutral-800 rounded-lg shadow-lg 
         border border-gray-200 dark:border-neutral-700 top-12
         p-4 space-y-3 items-center
         -translate-x-full min-w-40 z-50;

  &.zoom-panel-active {
    @apply block;
  }
}

.zoom-info {
  @apply flex items-center gap-4 w-full;
}

.zoom-percentage {
  @apply text-primary-100 text-sm font-semibold min-w-12;
}

.zoom-controls {
  @apply flex gap-2;
}

.zoom-control-btn {
  @apply flex items-center justify-center w-8 h-8 
         text-gray-600 dark:text-gray-300 
         hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 
         rounded-md transition-all duration-200;
}

.zoom-control-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.zoom-reset-btn {
  @apply flex items-center gap-2 px-3 py-1.5 
         bg-primary-100 hover:bg-primary-200 text-white 
         text-sm font-medium rounded-lg 
         border-none cursor-pointer
         transition-all duration-200;
}

.zoom-reset-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// ===== SETTINGS MENU =====
.settings-menu {
  @apply absolute -z-50 bottom-0 transition-transform px-4 py-4 w-full 
         bg-white dark:bg-neutral-800 shadow-lg 
         border-t border-gray-200 dark:border-neutral-700;
}

.settings-menu-content {
  @apply -z-50 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 
         items-start justify-center lg:w-4/5 xl:w-3/4 mx-auto;
}

.settings-option {
  @apply space-y-3;
}

.settings-option-title {
  @apply text-sm font-semibold text-gray-900 dark:text-white mb-2;
}

.settings-option-buttons {
  @apply flex flex-col gap-2;
}

.settings-btn {
  @apply flex items-center gap-3 px-4 py-3 text-sm font-medium 
         text-gray-600 dark:text-gray-300 
         hover:text-gray-900 dark:hover:text-white 
         bg-gray-50 dark:bg-neutral-700 
         hover:bg-gray-100 dark:hover:bg-neutral-600 
         rounded-lg 
         border border-gray-200 dark:border-neutral-600 cursor-pointer
         transition-all duration-200;

  &.settings-btn-active {
    @apply text-primary-100 bg-primary-100/10 border-primary-100/30;
  }

  &.settings-btn-full {
    @apply w-full justify-center;
  }
}

.settings-btn-icon {
  @apply w-5 h-5 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.settings-btn-text {
  @apply text-left;
}

// ===== TOGGLE SWITCH =====
.settings-toggle {
  @apply flex items-center gap-3 cursor-pointer;
}

.settings-toggle-input {
  @apply sr-only;
}

.settings-toggle-slider {
  @apply relative w-12 h-6 bg-gray-200 dark:bg-neutral-600 
         rounded-full transition-colors duration-200;

  .settings-toggle-input:checked + & {
    @apply bg-primary-100;
  }
}

.settings-toggle-thumb {
  @apply absolute top-0.5 left-0.5 w-5 h-5 bg-white 
         rounded-full shadow-sm transition-transform duration-200;

  .settings-toggle-input:checked + .settings-toggle-slider & {
    @apply translate-x-6;
  }
}

.settings-toggle-label {
  @apply text-sm font-medium text-gray-700 dark:text-gray-300;
}
