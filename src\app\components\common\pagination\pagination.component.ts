import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule],
})
export class PaginationComponent implements OnChanges {
  pages!: string[];
  @Input() currentPage = 1;
  @Input() totalpage!: number;
  @Output() OnChange = new EventEmitter<number>();
  showSearch = false;
  @ViewChild('SearchInput')
  SearchInputElement!: ElementRef;
  constructor(private route: ActivatedRoute) { }
  ngOnChanges() {
    if (this.totalpage) {
      this.PageSetup(Number(this.currentPage));
    }
  }
  PageSetup(page: number) {
    this.pages = [];
    
    // If total pages is 5 or less, show all pages
    if (this.totalpage <= 5) {
      for (let i = 1; i <= this.totalpage; i++) {
        this.pages.push(i.toString());
      }
      return;
    }

    // For more than 5 pages, use ellipsis logic
    if (page <= 3) {
      // Show: 1, 2, 3, 4, 5, ..., last
      for (let i = 1; i <= 5; i++) {
        this.pages.push(i.toString());
      }
      this.pages.push('...', this.totalpage.toString());
    } else if (page >= this.totalpage - 2) {
      // Show: 1, ..., last-4, last-3, last-2, last-1, last
      this.pages.push('1', '...');
      for (let i = this.totalpage - 4; i <= this.totalpage; i++) {
        this.pages.push(i.toString());
      }
    } else {
      // Show: 1, ..., current-1, current, current+1, ..., last
      this.pages.push('1', '...');
      for (let i = page - 1; i <= page + 1; i++) {
        this.pages.push(i.toString());
      }
      this.pages.push('...', this.totalpage.toString());
    }
  }

  OnChangePage(page: string) {
    if (page === '...' || !page) {
      this.showSearch = !this.showSearch;
      return;
    }

    this.showSearch = false;
    let _pageint = Number(page);
    _pageint = Math.min(this.totalpage, _pageint);
    _pageint = Math.max(1, _pageint);

    if (this.totalpage > 0 && _pageint != this.currentPage) {
      this.PageSetup(_pageint);
      this.OnChange.emit(_pageint);
      this.currentPage = _pageint;
      this.showSearch = false;
    }
  }
  onFocus(value: boolean) {
    if (!value) {
      this.showSearch = false;
    }
  }

  // TrackBy function for performance optimization
  trackByPage = (_index: number, page: string): string => {
    return page;
  };
}
