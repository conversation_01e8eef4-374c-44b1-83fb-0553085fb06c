import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnInit, PLATFORM_ID, ViewChild, ViewContainerRef } from '@angular/core';
import { RouterModule } from '@angular/router';
import { FooterComponent } from '@components/footer/footer.component';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';
import { NavComponent } from '@components/nav/nav.component';
import { PopupService } from '@services/popup.service';
// import { OverlayContainer } from '@angular/cdk/overlay';

import { ThemeService } from '@services/theme.service';
import { BehaviorSubject, Observable } from 'rxjs';


@Component({
    selector: 'app-content-layout',
    templateUrl: './layout.component.html',
    styleUrl: './layout.component.scss',
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule,FooterComponent,NavComponent,RouterModule, ChatBoxBubbleComponent],
})
export class LayoutComponent implements OnInit {
  currentTheme: Observable<boolean> = new BehaviorSubject<boolean>(false);
  @ViewChild('popupContainer', { read: ViewContainerRef }) vcRef!: ViewContainerRef;
  constructor(private themeService: ThemeService,
    @Inject(PLATFORM_ID) private platformId: object,
    private popupService: PopupService,
    
  ) {
  }
  ngOnInit() {    
    this.currentTheme = this.themeService.getDarkTheme();
  }
  ngAfterViewInit() {
    this.popupService.viewContainerRef = this.vcRef;
  }

  get isBrowser() {
    return isPlatformBrowser(this.platformId);
  }

}
