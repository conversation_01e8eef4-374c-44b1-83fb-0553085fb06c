// @import '@ctrl/ngx-emoji-mart/picker';
@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&family=Roboto+Flex:opsz,wght@8..144,400;500;600;700&family=Roboto+Mono:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;600;700&display=swap');
:root,
html,
body {
  font-family: 'Roboto', sans-serif;
  scroll-behavior: smooth !important;
  overflow-x: hidden;
  letter-spacing: 0.15px;
}

// Define custom scrollbar styles
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* Track */
::-webkit-scrollbar-track {
  // background: #f1f1f1;
  background-color: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  @apply bg-neutral-200 dark:bg-neutral-600 rounded-lg cursor-pointer;
  // border-radius: 6px;
  // background-color: rgba(0, 0, 0, 0.2);
  // cursor: pointer;
}


.scrollbar-style-1::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}



/* add the code bellow */
@layer utilities {

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }
}


.block-title {
  @apply  text-lg uppercase font-bold flex min-h-full px-2 text-center items-center justify-center;
}