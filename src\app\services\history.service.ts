import { Injectable } from '@angular/core';
import { Chapter, Comic } from '@schema';
import { StorageService } from './storage.service';
@Injectable({
  providedIn: 'root',
})
export class HistoryService {
  listHistory: Comic[] = [];
  maxHistory = 48;
  constructor(private storageService: StorageService) {
    this.listHistory = this.storageService.GetHistory();
  }

  SaveHistory(comic: Comic, chapter: Chapter) {
    const _comic: any = {
      id: comic.id,
      title: comic.title,
      url: comic.url,
      coverImage: comic.coverImage,
      viewCount: comic.viewCount,
      rating: comic.rating,
      updateAt: comic.updateAt,
      chapters: [],
    };
    const oldComic = this.listHistory.find((c) => c.id == comic.id);
    if (oldComic) {
      oldComic.coverImage = comic.coverImage;
      if (!oldComic.chapters) oldComic.chapters = [];
      if (oldComic.chapters.some((c) => c.id == chapter.id)) return;
      oldComic.chapters.push(chapter);
    } else {
      this.listHistory.unshift(_comic);
      this.listHistory = this.listHistory.slice(0, this.maxHistory);
      _comic.chapters = [chapter];
    }
    this.storageService.SetHistory(this.listHistory);
  }

  GetHistorys(): Comic[] {
    return this.listHistory;
  }
  GetHistory(id: number): Comic | undefined {
    return this.listHistory.find((c) => c.id === id);
  }
  GetHistorySize(): number {
    return this.listHistory.length;
  }

  RemoveHistory(id: number) {
    this.listHistory = this.listHistory.filter((c) => c.id != id);
    this.storageService.SetHistory(this.listHistory);
  }
}
