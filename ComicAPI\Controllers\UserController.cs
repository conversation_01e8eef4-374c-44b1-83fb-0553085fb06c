
using ComicAPI.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ComicAPI.DTOs;
using ComicAPI.Services;

namespace ComicAPI.Controllers
{
    [ApiController]
    [Route("api")]
    [Authorize]
    public class UserController : ControllerBase
{
    private readonly IUserService _userService;
    private readonly HttpClient _httpClient;
    private readonly UrlService _urlService;

    public UserController(IUserService userService, HttpClient httpClient, UrlService urlService)
    {
        _userService = userService;
        _httpClient = httpClient;
        _urlService = urlService;
    }
    [HttpGet("user/me")]
    public async Task<ActionResult<ServiceResponse<UserDTO>>> GetMyUserInfo()
    {
        return Ok(await _userService.GetMyUserInfo());
    }
    //no authorize

    [HttpGet("user/{id}")]
    [AllowAnonymous]
    public async Task<ActionResult<ServiceResponse<UserDTO>>> GetUserInfo(int id)
    {
        return Ok(await _userService.GetUserInfo(id));
    }
    [HttpPost("user/follow")]
    public async Task<ActionResult<ServiceResponse<int>>> FollowComic(int comicid, bool follow = true)
    {
        ServiceResponse<int> result;
        if (follow)
        {
            result = await _userService.FollowComic(comicid);
        }
        else
        {
            result = await _userService.UnFollowComic(comicid);
        }
        return Ok(result);
    }

    [HttpPost("user/comment")]
    public async Task<ActionResult<ServiceResponse<CommentDTO>>> CommentComic(AddCommentDTO addCommentDTO)
    {
        var data = await _userService.AddComment(addCommentDTO.Content!, addCommentDTO.ChapterId, addCommentDTO.replyFromCmt);
        return Ok(data);
    }

    [HttpGet("user/followed-comics")]
    public async Task<ActionResult<ServiceResponse<List<ListComicDTO>>>> GetFollowedComics(int page = 1, int size = 40)
    {
        size = Math.Max(size, 28);
        return Ok(await _userService.GetFollowComics(page, size));
    }

    [HttpGet("comments/comic/{comicId}")]
    [AllowAnonymous]
    public async Task<ActionResult<ServiceResponse<CommentPageDTO>>> GetCommentsOfComic(int comicId, int page = 1, int size = 10)
    {
        return Ok(await _userService.GetCommentsOfComic(comicId, page, size));
    }

    [HttpGet("comments/chapter/{chapterId}")]
    [AllowAnonymous]
    public async Task<ActionResult<ServiceResponse<CommentPageDTO>>> GetCommentsOfChapter(int chapterId, int page = 1, int size = 10)
    {
        return Ok(await _userService.GetCommentsOfChapter(chapterId, page, size));
    }
    [HttpPost("user/update")]
    public async Task<ActionResult<ServiceResponse<UserDTO>>> UpdateInfo(UpdateUserInfo request)
    {

        return Ok(await _userService.UpdateInfo(request));
    }
    [HttpPost("user/update/password")]
    public async Task<ActionResult<ServiceResponse<string>>> UpdatePassword(UpdateUserPassword request)
    {

        return Ok(await _userService.UpdatePassword(request));
    }
    [HttpPost("user/update/avatar")]
    public async Task<ActionResult<ServiceResponse<string>>> UpdateAvatar(IFormFile image)
    {

        return Ok(await _userService.UpdateAvatar(image));
    }
    [HttpPost("user/update/typelevel/{typelevel}")]
    public async Task<ActionResult<ServiceResponse<string>>> UpdateTypeLevel(int typelevel)
    {

        return Ok(await _userService.UpdateTypelevel(typelevel));
    }
    [HttpPost("user/update/maxim")]
    public async Task<ActionResult<ServiceResponse<string>>> UpdateMaxim(string? maxim)
    {

        return Ok(await _userService.UpdateMaxim(maxim));
    }


    [HttpGet("user/notify")]
    public async Task<ActionResult<ServiceResponse<List<UserNotificationDTO>>>> GetUserNotify()
    {

        return Ok(await _userService.GetUserNotify());
    }
    [HttpPost("user/notify/update")]
    public async Task<ActionResult<ServiceResponse<string>>> UpdateUserNotify(
       UpdateUserNotifyDTO notify)
    {

        var result = await _userService.UpdateUserNotify(notify.ID, notify.IsRead);
        return Ok(result);
    }
    [HttpDelete("user/notify/delete/{notifyID}")]
    public async Task<ActionResult<ServiceResponse<string>>> DeleteUserNotify(int notifyID = -1)
    {

        var result = await _userService.DeleteUserNotify(notifyID);
        return Ok(result);
    }
    [HttpGet("user/vote")]
    public async Task<ActionResult<ServiceResponse<int>>> GetUserVote(int comicid)
    {
        return Ok(await _userService.GetUserVote(comicid));
    }
    [HttpPost("user/vote/update")]
    public async Task<ActionResult<ServiceResponse<int>>> VoteComic([FromBody] VoteDataDTO data)
    {
        var result = await _userService.VoteComic(data.comicid, data.votePoint);

        return Ok(result);
    }
    [HttpDelete("user/vote/delete")]
    public async Task<ActionResult<ServiceResponse<int>>> UnVoteComic(int comicid)
    {
        return Ok(await _userService.UnVoteComic(comicid));
    }

    [AllowAnonymous]
    [HttpGet("chat/conversations")]
    public async Task<ActionResult<ServiceResponse<List<ConversationDTO>>>> GetMyConversations()
    {
        return Ok(await _userService.GetMyConversations());
    }

    [HttpGet("chat/conversation/{conversationId}")]
    public async Task<ActionResult<ServiceResponse<ConversationDTO>>> GetConversation(Guid conversationId)
    {
        return Ok(await _userService.GetConversation(conversationId));
    }

    [HttpPost("chat/message")]
    public async Task<ActionResult<ServiceResponse<MessageDTO>>> SendMessage([FromBody] SendMessageDTO request)
    {
        return Ok(await _userService.SendMessage(request));
    }

    [HttpGet("chat/messages/{conversationId}")]
    public async Task<ActionResult<ServiceResponse<MessagePageDTO>>> GetMessages(Guid conversationId, int page = 1, int pageSize = 50)
    {
        return Ok(await _userService.GetMessages(conversationId, page, pageSize));
    }


    [HttpGet("chat/chatbot/{messageid}")]
    public async Task GetChatbotMessageResponse(Guid messageid)
    {
        ServiceResponse<Message>? msg = await _userService.GetMessage(messageid);
        if (msg == null || msg.Data == null) return;
        string user_question = msg.Data.Content;


        string url = $"{_urlService.ChatbotUrl}?query={Uri.EscapeDataString(user_question)}&user_id={_userService.CurrentUser?.ID}&use_context=true&k=5";

        using var request = new HttpRequestMessage(HttpMethod.Get, url);
        request.Headers.Accept.Add(new System.Net.Http.Headers.MediaTypeWithQualityHeaderValue("text/event-stream"));
        Response.ContentType = "text/event-stream";

        using var response = await _httpClient.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, HttpContext.RequestAborted);

        await using var responseStream = await response.Content.ReadAsStreamAsync(HttpContext.RequestAborted);
        using var reader = new StreamReader(responseStream);
        string msg_content = "";
        // Đọc từng dòng từ stream và ghi ra response
        while (!reader.EndOfStream)
        {
            var line = await reader.ReadLineAsync();
            msg_content += line;
            await Response.WriteAsync(line + "\n\n", HttpContext.RequestAborted);
            await Response.Body.FlushAsync(HttpContext.RequestAborted); // async flush

        }
        // await _userService.SendMessage(new SendMessageDTO { Content = msg_content, ConversationId = msg.Data.ConversationId });
    }
    }
}