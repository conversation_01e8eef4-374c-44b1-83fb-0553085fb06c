import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root',
})
export class ThemeService {
  private isDarkTheme: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(
    false
  );

  public IsDark = () => this.isDarkTheme.value;

  constructor(private storageService: StorageService) {
    const _isDarkTheme = storageService.IsDarkTheme();
    this.isDarkTheme.next(_isDarkTheme);
  }

  setDarkTheme(isDarkTheme: boolean) {
    this.isDarkTheme.next(isDarkTheme);
    this.storageService.SetDarkTheme(isDarkTheme);
  }

  ToggleTheme() {
    this.isDarkTheme.next(!this.isDarkTheme.value);
    this.storageService.SetDarkTheme(this.isDarkTheme.value);
  }

  getDarkTheme(): Observable<boolean> {
    return this.isDarkTheme;
  }
}


