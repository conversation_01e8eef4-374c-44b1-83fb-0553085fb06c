import {
  isPlatformServer
} from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Comic, ComicList, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { UrlService } from '@services/url.service';
@Component({
  selector: 'app-home',
  templateUrl: './home-page.component.html',
  styleUrl: './home-page.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class HomePageComponent implements OnInit {
  listComics: Comic[] = [];
  totalpage!: number;
  currentPage = 1;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private comicService: ComicService,
    private seoService: SeoService,
    private urlService: UrlService,
    private cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Document,
  ) {
    this.initializeSEO();
  }

  private initializeSEO(): void {
    const seoData = {
      title: 'Đọc Truyện Tranh Online Miễn Phí - Manga, Manhwa, Manhua Hay Nhất',
      description: 'Website đọc truyện tranh online hàng đầu Việt Nam với hơn 50,000+ bộ manga, manhwa, manhua. Cập nhật nhanh nhất, chất lượng HD, hoàn toàn miễn phí. Đọc ngay!',
      keywords: 'truyện tranh online, manga việt nam, manhwa hay, manhua mới nhất, đọc truyện miễn phí, comic online, truyện tranh hot, manga full, manhwa romance, action manga, truyện ngôn tình, truyện hành động, one piece, naruto, dragon ball',
      type: 'website' as const,
      url: this.urlService.BASE_URL,
      image: `${this.urlService.BASE_URL}/assets/images/og-home.jpg`,
      siteName: 'Mê Truyện Mới',
      locale: 'vi_VN',
      twitterCard: 'summary_large_image' as const
    };

    this.seoService.setSEOData(seoData);

    // Add website structured data
    const websiteSchema = this.seoService.generateWebsiteSchema();
    this.seoService.addStructuredData(websiteSchema);

    // Add breadcrumb for homepage
    const breadcrumbSchema = this.seoService.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: '/' }
    ]);

    // Combine schemas
    const combinedSchema = [websiteSchema, breadcrumbSchema];
    this.seoService.addStructuredData(combinedSchema);
  }

  ngOnInit(): void {
    
    this.route.queryParams.subscribe((params) => {
      const page = Number(params['page']) || 1;
      this.currentPage = page;
      this.refreshPage(page);

    });
  }


  refreshPage(page: number, size = 30): void {
    this.listComics = [];
    this.comicService.getComics({
      step: size.toString(),
      genre: '-1',
      page: page.toString(),
      sort: '1',
      status: '-1',
    }).subscribe((res: IServiceResponse<ComicList>) => {
      this.totalpage = res.data!.totalpage;
      this.listComics = res.data!.comics;

      if (this.ssr()) {
        this.listComics = this.listComics.slice(0, 10);
      }

      // Update SEO for pagination
      if (page > 1) {
        this.updatePaginationSEO(page);
      }

      // Add ItemList structured data for comics
      this.addComicsStructuredData();

      this.cd.detectChanges();
    });
  }

  private updatePaginationSEO(page: number): void {
    const title = `Trang ${page} - Truyện Tranh Mới Nhất | Mê Truyện Mới`;
    const description = `Xem trang ${page} của danh sách truyện tranh mới nhất được cập nhật liên tục. Hơn 50,000+ bộ manga, manhwa, manhua chất lượng cao.`;

    this.seoService.setSEOData({
      title,
      description,
      url: `${this.urlService.BASE_URL}?page=${page}`,
      type: 'website',
      canonical: `${this.urlService.BASE_URL}?page=${page}`
    });
  }

  private addComicsStructuredData(): void {
    if (this.listComics.length > 0) {
      const itemListSchema = {
        '@context': 'https://schema.org',
        '@type': 'ItemList',
        'name': 'Truyện Tranh Mới Nhất',
        'description': 'Danh sách truyện tranh mới cập nhật',
        'numberOfItems': this.listComics.length,
        'itemListElement': this.listComics.slice(0, 10).map((comic, index) => ({
          '@type': 'ListItem',
          'position': index + 1,
          'item': {
            '@type': 'Book',
            'name': comic.title,
            'description': comic.description || `Đọc truyện ${comic.title} online miễn phí`,
            'image': comic.coverImage,
            'url': `${this.urlService.BASE_URL}/truyen-tranh/${comic.url}-${comic.id}`,
            'author': {
              '@type': 'Person',
              'name': comic.author || 'Đang cập nhật'
            },
            'genre': comic.genres?.map(g => g.title) || [],
            'bookFormat': 'GraphicNovel',
            'inLanguage': 'vi'
          }
        }))
      };

      // Update existing structured data
      const existingSchemas = [
        this.seoService.generateWebsiteSchema(),
        itemListSchema
      ];

      this.seoService.addStructuredData(existingSchemas);
    }
  }
  onChangePage(page: number) {
    this.router.navigate([''], {
      queryParams: { page: page },
      fragment: 'comics',
    });
  }
  ssr() {
    return isPlatformServer(this.platformId);
  }
}
