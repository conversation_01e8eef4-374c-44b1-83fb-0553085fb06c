// User Profile Container Styles - Matching Current Website Design
.user-profile-container {
  @apply min-h-screen bg-white dark:bg-dark-bg md:container mx-auto;
}

// Loading State
.loading-container {
  @apply flex items-center justify-center min-h-screen;
}

.loading-content {
  @apply flex flex-col items-center gap-4;
}

.loading-spinner {
  @apply w-12 h-12 border-4 border-neutral-200 border-t-primary-100 rounded-full animate-spin;
}

.loading-text {
  @apply text-neutral-600 dark:text-neutral-400 font-medium;
}

// Main Layout
.profile-layout {
  @apply flex min-h-screen;
}

// Sidebar - Matching Current Website Design
.profile-sidebar {
  @apply w-80 bg-neutral-100 dark:bg-neutral-800 border-r border-neutral-200 dark:border-neutral-900 flex-shrink-0;
}

.sidebar-content {
  @apply flex flex-col h-full p-4;
}

// User Quick Info - Simplified Design
.user-quick-info {
  @apply flex items-center gap-3 p-3 bg-white dark:bg-neutral-700 rounded-lg mb-4 border border-neutral-200 dark:border-neutral-900;
}

.user-avatar-container {
  @apply relative;
}

.user-avatar {
  @apply w-14 h-14 rounded-full object-cover border-2 border-neutral-200 dark:border-neutral-600;
}

.user-status-indicator {
  @apply absolute -bottom-0.5 -right-0.5 w-4 h-4 bg-lime-500 border-2 border-white dark:border-neutral-700 rounded-full;
}

.user-basic-info {
  @apply flex-1 min-w-0;
}

.user-name {
  @apply text-base font-bold text-neutral-900 dark:text-white truncate;
}

.user-join-date {
  @apply text-xs text-neutral-500 dark:text-neutral-400;
}

// Navigation - Simplified Design
.profile-navigation {
  @apply flex-1;
}

.nav-section-title {
  @apply text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3;
}

.nav-list {
  @apply space-y-1;
}

.nav-item {
  @apply relative;
}

.nav-link {
  @apply block w-full p-3 rounded-lg transition-all duration-200 hover:bg-white dark:hover:bg-neutral-700;

  &.nav-link-active {
    @apply bg-primary-100 text-white;

    .nav-icon-wrapper {
      @apply bg-white/20;
    }

    .nav-icon {
      @apply text-white;
    }

    .nav-label {
      @apply text-white;
    }

    .nav-description {
      @apply text-white/80;
    }
  }

  &:hover:not(.nav-link-active) {
    @apply shadow-sm;
  }
}

.nav-link-content {
  @apply flex items-center gap-3;
}

.nav-icon-wrapper {
  @apply flex items-center justify-center w-9 h-9 bg-neutral-200 dark:bg-neutral-600 rounded-lg transition-colors duration-200;
}

.nav-icon {
  @apply w-4 h-4 text-neutral-600 dark:text-neutral-300 transition-colors duration-200;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.nav-text-content {
  @apply flex-1 min-w-0;
}

.nav-label {
  @apply block text-sm font-medium text-neutral-900 dark:text-white;
}

.nav-description {
  @apply block text-xs text-neutral-500 dark:text-neutral-400 truncate;
}

.nav-badge {
  @apply px-2 py-1 bg-primary-100 text-white text-xs font-bold rounded-full min-w-[18px] text-center;
}

// Main Content
.profile-main-content {
  @apply flex-1 p-4 overflow-auto bg-white dark:bg-dark-bg;
}

// Responsive Design
@media (max-width: 1024px) {
  .profile-sidebar {
    @apply w-64;
  }

  .sidebar-content {
    @apply p-3;
  }

  .profile-main-content {
    @apply p-3;
  }
}

@media (max-width: 768px) {
  .profile-layout {
    @apply flex-col;
  }

  .profile-sidebar {
    @apply w-full h-auto border-r-0 border-b border-neutral-200 dark:border-neutral-900;
    @apply bg-white dark:bg-neutral-900;
  }

  .sidebar-content {
    @apply p-4;
  }

  // Mobile: Compact user info
  .user-quick-info {
    @apply flex-row justify-center gap-3 mb-6;
    @apply bg-neutral-50 dark:bg-neutral-800 rounded-xl p-4;
  }

  .user-avatar {
    @apply w-12 h-12;
  }

  .user-name {
    @apply text-sm;
  }

  .user-join-date {
    @apply text-xs;
  }

  // Mobile: Grid layout for navigation
  .profile-navigation {
    @apply flex-none;
  }

  .nav-section-title {
    @apply hidden; // Hide section title on mobile
  }

  .nav-list {
    @apply grid grid-cols-4 gap-2; // 4 columns grid with smaller gap
    @apply space-y-0; // Remove vertical spacing
  }

  .nav-item {
    @apply relative;
  }

  .nav-link {
    @apply flex flex-col items-center justify-center;
    @apply p-2 rounded-lg;
    @apply bg-neutral-50 dark:bg-neutral-800;
    @apply hover:bg-neutral-100 dark:hover:bg-neutral-700;
    @apply transition-all duration-200;
    @apply min-h-[75px]; // Smaller height to accommodate text

    &.nav-link-active {
      @apply bg-primary-100 text-white;
      @apply shadow-lg shadow-primary-100/25;

      .nav-icon-wrapper {
        @apply bg-white/20;
      }

      .nav-icon {
        @apply text-white;
      }

      .nav-mobile-label {
        @apply text-white;
      }
    }

    &:hover:not(.nav-link-active) {
      @apply shadow-md;
      transform: translateY(-1px);
    }
  }

  .nav-link-content {
    @apply flex flex-col items-center gap-1; // Smaller gap
  }

  .nav-icon-wrapper {
    @apply w-8 h-8 bg-transparent; // Smaller icon wrapper
    @apply flex items-center justify-center;
  }

  .nav-icon {
    @apply w-5 h-5 text-neutral-600 dark:text-neutral-300; // Smaller icon
  }

  // Hide desktop text content on mobile
  .nav-text-content {
    @apply hidden;
  }

  // Show mobile label (small text under icon)
  .nav-mobile-label {
    @apply block text-xs text-neutral-600 dark:text-neutral-400 text-center;
    @apply font-medium leading-tight;
    @apply max-w-full truncate;
  }

  // Mobile: Badge positioning for grid layout
  .nav-badge {
    @apply absolute -top-1 -right-1;
    @apply px-1.5 py-0.5 text-xs;
    @apply min-w-[16px] h-4;
    @apply flex items-center justify-center;
    @apply bg-red-500 text-white;
    @apply rounded-full;
    @apply shadow-sm;
  }
}

// Extra small screens (mobile phones)
@media (max-width: 480px) {
  .sidebar-content {
    @apply p-3;
  }

  .user-quick-info {
    @apply gap-2 p-3 mb-4;
  }

  .user-avatar {
    @apply w-10 h-10;
  }

  .user-name {
    @apply text-xs;
  }

  .user-join-date {
    @apply text-xs;
  }

  // Smaller grid on very small screens
  .nav-list {
    @apply grid-cols-3 gap-1; // 3 columns for very small screens with smaller gap
  }

  .nav-link {
    @apply p-1.5 min-h-[65px]; // Smaller padding and height
  }

  .nav-icon-wrapper {
    @apply w-7 h-7; // Smaller icon wrapper
  }

  .nav-icon {
    @apply w-4 h-4; // Smaller icon
  }

  .nav-mobile-label {
    @apply text-xs; // Keep text size but ensure it fits
  }

  .nav-badge {
    @apply text-xs min-w-[12px] h-3;
    @apply px-1 py-0;
  }
}

// Accessibility
.nav-link:focus {
  @apply outline-none ring-2 ring-primary-100 ring-opacity-50;
}

// High Contrast Mode
@media (prefers-contrast: high) {
  .profile-sidebar {
    @apply border-2 border-black dark:border-white;
  }

  .nav-link.nav-link-active {
    @apply bg-black text-white dark:bg-white dark:text-black;
  }

  .user-quick-info {
    @apply border-2 border-black dark:border-white;
  }
}

// Reduced Motion
@media (prefers-reduced-motion: reduce) {
  .nav-link,
  .nav-icon-wrapper,
  .nav-icon {
    @apply transition-none;
  }

  .loading-spinner {
    animation: none;
  }
}

// Performance Optimizations
.profile-sidebar,
.nav-link {
  transform: translateZ(0);
  backface-visibility: hidden;
}
