<!-- Modern Comic-themed Register Container -->
<div class="auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="comic-bubbles">
      <div class="bubble bubble-1">🎨</div>
      <div class="bubble bubble-2">📚</div>
      <div class="bubble bubble-3">🌟</div>
      <div class="bubble bubble-4">🚀</div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="auth-content">
    <!-- Left Side - Branding -->
    <div class="auth-branding">
      <div class="brand-content">
        <div class="brand-logo">
          <div class="logo-icon">🎭</div>
          <h1 class="brand-title">Tham gia <span class="brand-accent">MeTruyen</span>Moi</h1>
        </div>
        <p class="brand-subtitle">Bắt đầu hành trình khám phá truyện tranh tuyệt vời</p>
        <div class="brand-features">
          <div class="feature-item">
            <span class="feature-icon">🎁</span>
            <span class="feature-text">Miễn phí hoàn toàn</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📖</span>
            <span class="feature-text">Đọc không giới hạn</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">💫</span>
            <span class="feature-text">Cộng đồng sôi động</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side - Register Form -->
    <div class="auth-form-container">
      <div class="auth-form-card">
        <!-- Header -->
        <div class="form-header">
          <h2 class="form-title">Tạo tài khoản mới</h2>
          <p class="form-subtitle">
            Chỉ cần vài bước đơn giản để bắt đầu đọc truyện
          </p>
        </div>

        <!-- Register Form -->
        <form class="auth-form" [formGroup]="form" (submit)="onSubmit()">
          <!-- Name Field -->
          <div class="form-group">
            <label for="name" class="form-label">
              <span class="label-icon">👤</span>
              Họ và tên
            </label>
            <div class="input-wrapper">
              <input
                id="name"
                name="name"
                type="text"
                formControlName="name"
                placeholder="Nhập họ và tên của bạn"
                class="form-input"
                autocomplete="name"
                required
              />
              <div class="input-icon">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('name')">
              <span *ngIf="form.get('name')?.hasError('required')">
                Vui lòng nhập họ và tên
              </span>
            </div>
          </div>

          <!-- Email Field -->
          <div class="form-group">
            <label for="email" class="form-label">
              <span class="label-icon">📧</span>
              Email
            </label>
            <div class="input-wrapper">
              <input
                id="email"
                name="email"
                type="email"
                formControlName="email"
                placeholder="Nhập địa chỉ email của bạn"
                class="form-input"
                autocomplete="email"
                required
              />
              <div class="input-icon">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('email')">
              <span *ngIf="form.get('email')?.hasError('required')">
                Vui lòng nhập địa chỉ email
              </span>
              <span *ngIf="form.get('email')?.hasError('email') && submitted">
                Định dạng email không hợp lệ
              </span>
            </div>
          </div>

          <!-- Password Field -->
          <div class="form-group">
            <label for="password" class="form-label">
              <span class="label-icon">🔒</span>
              Mật khẩu
            </label>
            <div class="input-wrapper">
              <input
                id="password"
                name="password"
                [type]="showPassword ? 'text' : 'password'"
                formControlName="password"
                placeholder="Tạo mật khẩu mạnh"
                class="form-input"
                autocomplete="new-password"
                required
              />
              <button
                type="button"
                (click)="showPassword = !showPassword"
                class="password-toggle"
              >
                <app-eye-icon [show]="showPassword"></app-eye-icon>
              </button>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('password')">
              <span *ngIf="form.get('password')?.hasError('required')">
                Vui lòng nhập mật khẩu
              </span>
            </div>
          </div>

          <!-- Confirm Password Field -->
          <div class="form-group">
            <label for="confirm-password" class="form-label">
              <span class="label-icon">🔐</span>
              Xác nhận mật khẩu
            </label>
            <div class="input-wrapper">
              <input
                id="confirm-password"
                name="confirm-password"
                [type]="showConfirmPassword ? 'text' : 'password'"
                formControlName="confirm-password"
                placeholder="Nhập lại mật khẩu"
                class="form-input"
                autocomplete="new-password"
                required
              />
              <button
                type="button"
                (click)="showConfirmPassword = !showConfirmPassword"
                class="password-toggle"
              >
                <app-eye-icon [show]="showConfirmPassword"></app-eye-icon>
              </button>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('confirm-password')">
              <span *ngIf="form.get('confirm-password')?.hasError('required')">
                Vui lòng xác nhận mật khẩu
              </span>
            </div>
          </div>

          <!-- Terms & Conditions -->
          <div class="form-group">
            <div class="terms-checkbox">
              <input
                id="accept"
                name="accept"
                type="checkbox"
                formControlName="accept"
                class="form-checkbox"
                required
              />
              <label for="accept" class="checkbox-label">
                Tôi đồng ý với
                <a href="#" class="terms-link">Điều khoản sử dụng</a>
                và
                <a href="#" class="terms-link">Chính sách bảo mật</a>
              </label>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('accept')">
              <span *ngIf="form.get('accept')?.hasError('required')">
                Vui lòng đồng ý với điều khoản sử dụng
              </span>
            </div>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            class="submit-button"
            [disabled]="form.invalid"
          >
            <span class="button-content">
              <span class="button-icon">🎉</span>
              <span class="button-text">Tạo tài khoản</span>
            </span>
          </button>
        </form>

        <!-- Divider -->
        <div class="form-divider">
          <div class="divider-line"></div>
          <span class="divider-text">Hoặc đăng ký với</span>
          <div class="divider-line"></div>
        </div>

        <!-- Social Login -->
        <div class="social-login">
          <asl-google-signin-button
            type="standard"
            size="large"
            shape="rectangular"
            theme="filled_black"
            [width]="280"
            class="google-button"
          >
          </asl-google-signin-button>
        </div>

        <!-- Login Link -->
        <div class="auth-switch">
          <p class="switch-text">
            Đã có tài khoản?
            <a [routerLink]="['/auth/login']" class="switch-link">
              Đăng nhập ngay
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
