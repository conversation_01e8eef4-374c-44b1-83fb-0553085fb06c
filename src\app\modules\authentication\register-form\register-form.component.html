<!-- Modern Comic-themed Register Container -->
<div class="auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="comic-bubbles">
      <div class="bubble bubble-1">🎨</div>
      <div class="bubble bubble-2">📚</div>
      <div class="bubble bubble-3">🌟</div>
      <div class="bubble bubble-4">🚀</div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="auth-content">
    <!-- Left Side - Branding -->
    <div class="auth-branding">
      <div class="brand-content">
        <div class="brand-logo">
          <div class="logo-icon">
            <img loading="lazy" src="/new-favicon.png" alt="MeTruyenMoi Logo" />
          </div>
          <h1 class="brand-title">Tham gia <span class="brand-accent">MeTruyen</span>Moi</h1>
        </div>
        <p class="brand-subtitle">Luôn cập nhật truyện tranh mới nhất mỗi ngày</p>
        <div class="brand-features">
          <div class="feature-item">
            <span class="feature-icon"><svg class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 11 12 14 22 4" />
                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11" />
              </svg></span>
            <span class="feature-text">Miễn phí hoàn toàn</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon"><svg class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 11 12 14 22 4" />
                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11" />
              </svg></span>
            <span class="feature-text">Đọc không giới hạn</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon"><svg class="size-6" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 11 12 14 22 4" />
                <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11" />
              </svg></span>
            <span class="feature-text">Cộng đồng sôi động</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side - Register Form -->
    <div class="auth-form-container">
      <div class="auth-form-card">
        <!-- Header -->
        <div class="form-header">
          <h2 class="form-title">Tạo tài khoản mới</h2>
          <p class="form-subtitle">Chỉ cần vài bước đơn giản để bắt đầu đọc truyện</p>
        </div>

        <!-- Register Form -->
        <form class="auth-form" [formGroup]="form" (submit)="onSubmit()">
          <!-- Name Field -->
          <div class="form-group">
            <label for="name" class="form-label">
              <span class="label-icon"><svg width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                  stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path stroke="none" d="M0 0h24v24H0z" />
                  <circle cx="12" cy="7" r="4" />
                  <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2" />
                </svg></span>
              Họ và tên
            </label>
            <div class="input-wrapper">
              <input id="name" name="name" type="text" formControlName="name" placeholder="Nhập họ và tên của bạn"
                class="form-input" autocomplete="name" required />
              <div class="input-icon">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('name')">
              <span *ngIf="form.get('name')?.hasError('required')"> Vui lòng nhập họ và tên </span>
            </div>
          </div>

          <!-- Email Field -->
          <div class="form-group">
            <label for="email" class="form-label">
              <span class="label-icon"><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </span>
              Email
            </label>
            <div class="input-wrapper">
              <input id="email" name="email" type="email" formControlName="email"
                placeholder="Nhập địa chỉ email của bạn" class="form-input" autocomplete="email" required />
              <div class="input-icon">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('email')">
              <span *ngIf="form.get('email')?.hasError('required')">
                Vui lòng nhập địa chỉ email
              </span>
              <span *ngIf="form.get('email')?.hasError('email') && submitted">
                Định dạng email không hợp lệ
              </span>
            </div>
          </div>

          <!-- Password Field -->
          <div class="form-group">
            <label for="password" class="form-label">
              <span class="label-icon"><svg class="h-6 w-6" width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                  stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                  <path stroke="none" d="M0 0h24v24H0z" />
                  <rect x="5" y="11" width="14" height="10" rx="2" />
                  <circle cx="12" cy="16" r="1" />
                  <path d="M8 11v-4a4 4 0 0 1 8 0v4" />
                </svg></span>
              Mật khẩu
            </label>
            <div class="input-wrapper">
              <input id="password" name="password" [type]="showPassword ? 'text' : 'password'"
                formControlName="password" placeholder="Tạo mật khẩu mạnh" class="form-input"
                autocomplete="new-password" required />
              <button type="button" (click)="showPassword = !showPassword" class="password-toggle">
                <app-eye-icon [show]="showPassword"></app-eye-icon>
              </button>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('password')">
              <span *ngIf="form.get('password')?.hasError('required')">
                Vui lòng nhập mật khẩu
              </span>
            </div>
          </div>

          <!-- Confirm Password Field -->
          <div class="form-group">
            <label for="confirm-password" class="form-label">
              <span class="label-icon"><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 11V7a4 4 0 118 0m-4 8v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2z" />
                </svg>
              </span>
              Xác nhận mật khẩu
            </label>
            <div class="input-wrapper">
              <input id="confirm-password" name="confirm-password" [type]="showConfirmPassword ? 'text' : 'password'"
                formControlName="confirm-password" placeholder="Nhập lại mật khẩu" class="form-input"
                autocomplete="new-password" required />
              <button type="button" (click)="showConfirmPassword = !showConfirmPassword" class="password-toggle">
                <app-eye-icon [show]="showConfirmPassword"></app-eye-icon>
              </button>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('confirm-password')">
              <span *ngIf="form.get('confirm-password')?.hasError('required')">
                Vui lòng xác nhận mật khẩu
              </span>
            </div>
          </div>

          <!-- Terms & Conditions -->
          <div class="form-group">
            <div class="terms-checkbox">
              <input id="accept" name="accept" type="checkbox" formControlName="accept" class="form-checkbox"
                required />
              <label for="accept" class="checkbox-label">
                Tôi đồng ý với
                <a href="#" class="terms-link">Điều khoản sử dụng</a>
                và
                <a href="#" class="terms-link">Chính sách bảo mật</a>
              </label>
            </div>
            <div class="error-message" *ngIf="isControlInvalid('accept')">
              <span *ngIf="form.get('accept')?.hasError('required')">
                Vui lòng đồng ý với điều khoản sử dụng
              </span>
            </div>
          </div>

          <!-- Submit Button -->
          <button type="submit" class="submit-button" [disabled]="form.invalid">
            <span class="button-content">
              <span class="button-icon"><svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </span>
              <span class="button-text">Tạo tài khoản</span>
            </span>
          </button>
        </form>

        <!-- Divider -->
        <div class="form-divider">
          <div class="divider-line"></div>
          <span class="divider-text">Hoặc đăng ký với</span>
          <div class="divider-line"></div>
        </div>

        <!-- Social Login -->
        <div class="social-login">
          <asl-google-signin-button type="standard" size="large" shape="rectangular" theme="filled_black" [width]="280"
            class="google-button">
          </asl-google-signin-button>
        </div>

        <!-- Login Link -->
        <div class="auth-switch">
          <p class="switch-text">
            Đã có tài khoản?
            <a [routerLink]="['/auth/login']" class="switch-link"> Đăng nhập ngay </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>