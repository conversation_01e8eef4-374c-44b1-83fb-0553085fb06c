import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { RouterLink } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ComicCardComponent } from '@components/common/comic-card/Ver1/comic-card.component';
import { EmptyComponent } from '@components/common/empty/empty.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { Comic } from '@schema';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { ToastService, ToastType } from '@services/toast.service';

@Component({
  selector: 'app-reading-history',
  standalone: true,
  imports: [CommonModule, RouterLink, ComicCardComponent, PaginationComponent, EmptyComponent, SpinnerComponent],
  templateUrl: './reading-history.component.html',
  styleUrl: './reading-history.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ReadingHistoryComponent extends OptimizedBaseComponent implements OnInit {

  constructor( private historyService: HistoryService,
    private toastService: ToastService,
    private popupService: PopupService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object,
  ) {
    super(cd, platformId);
  }

  comics: Comic[] = [];
  isLoading = true;
  currentPage = 1;
  itemsPerPage = 14;
  totalPages = 1;
  searchQuery = '';
  sortBy: 'recent' | 'title' | 'progress' = 'recent';

  ngOnInit() {
    this.runInBrowser(() => {
    this.loadHistory();
    });
  }

  private loadHistory() {
    this.isLoading = true;

    const historyData = this.historyService.GetHistorys();

    if (historyData.length === 0) {
      this.comics = [];
      this.isLoading = false;
      this.cd.detectChanges();
      return;
    }

    // Calculate total pages
    this.totalPages = Math.floor((historyData.length - 1) / this.itemsPerPage) + 1;

    // Get paginated data
    const paginatedHistory = historyData.slice(
      (this.currentPage - 1) * this.itemsPerPage,
      this.currentPage * this.itemsPerPage
    );

    // Set comics for display
    this.comics = paginatedHistory;
    this.isLoading = false;
    this.cd.detectChanges();
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.loadHistory();
  }

  onRemoveSelectedComics(ids: number[]) {
    if (!ids.length) return;

    this.popupService.showConfirmPopup({
      title: 'Xóa lịch sử đọc',
      message: this.getRemoveMessage(ids),
      confirmButtonText: 'Xóa',
      cancelButtonText: 'Hủy'
    }).then((result: any) => {
      const { isconfirm } = result;
      if (isconfirm) {
        ids.forEach(id => {
          this.historyService.RemoveHistory(id);
        });

        this.loadHistory();
        this.toastService.show(ToastType.Success, 'Đã xóa lịch sử đọc thành công');
      }
    });
  }

  private getRemoveMessage(ids: number[]): string {
    if (ids.length > 1) {
      return `Bạn có chắc chắn muốn xóa <b>${ids.length}</b> truyện khỏi lịch sử đọc?`;
    }

    const [comicId] = ids;
    const comic = this.comics.find(comic => comic.id === comicId);
    return comic
      ? `Bạn có chắc chắn muốn xóa <b>${comic.title}</b> khỏi lịch sử đọc?`
      : 'Bạn có chắc chắn muốn xóa truyện này khỏi lịch sử đọc?';
  }

  get hasHistory(): boolean {
    return this.historyService.GetHistorySize() > 0;
  }


}
