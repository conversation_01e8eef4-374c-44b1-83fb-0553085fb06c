using ComicApp.Models.SyncTracking;
using ComicAPI.DTOs.SyncTracking;

namespace ComicAPI.Services.SyncTracking
{
    public interface ICrawlerService
    {
        /// <summary>
        /// Test login credentials for a specific site
        /// </summary>
        Task<bool> TestLoginAsync(ComicSource site, SyncCredentialsDTO credentials);

        /// <summary>
        /// Fetch tracked comics from NetTruyen
        /// </summary>
        Task<List<TrackedComic>> FetchComicsAsync(ComicSource site, SyncCredentialsDTO credentials);

    }
}
