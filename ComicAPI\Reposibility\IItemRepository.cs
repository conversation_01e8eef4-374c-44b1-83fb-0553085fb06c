
using ComicAPI.DTOs;

namespace ComicAPI.Reposibility
{
    public interface IItemRepository
    {
        // Item Templates
        Task<List<ItemTemplateDTO>> GetItemTemplatesAsync(string? category = null);
        Task<ItemTemplateDTO?> GetItemTemplateAsync(int templateId);
        Task<List<ItemTemplateDTO>> GetItemTemplatesByRarityAsync(string rarity);

        // User Inventory
        Task<InventoryPageDTO> GetUserInventoryAsync(int userId, int page = 1, int pageSize = 20, string? category = null);
        Task<UserInventoryDTO?> GetUserInventoryItemAsync(int userId, int itemTemplateId);
        Task<List<UserInventoryDTO>> GetUserInventoryByCategoryAsync(int userId, string category);

        // Item Operations
        Task<ItemOperationResponse> GiveItemToUserAsync(int userId, int itemTemplateId, int quantity = 1, string? source = null, int? sourceId = null);
        Task<ItemOperationResponse> UseItemAsync(int userId, int itemTemplateId, int quantity = 1);
        Task<ItemOperationResponse> EquipItemAsync(int userId, int itemTemplateId, string slotType);
        Task<ItemOperationResponse> UnequipItemAsync(int userId, string slotType);

        // Equipped Items
        Task<List<UserEquippedItemDTO>> GetUserEquippedItemsAsync(int userId);
        Task<UserEquippedItemDTO?> GetUserEquippedItemBySlotAsync(int userId, string slotType);

        // Utility
        Task<bool> HasItemAsync(int userId, int itemTemplateId, int quantity = 1);
        Task<int> GetItemQuantityAsync(int userId, int itemTemplateId);
        Task<bool> CleanupExpiredItemsAsync();
        Task<Dictionary<string, int>> GetUserCurrencyAsync(int userId); // Get coins, diamonds, etc.
    }
}
