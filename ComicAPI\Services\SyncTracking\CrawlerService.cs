using System.Text;
using System.Text.RegularExpressions;
using ComicApp.Models.SyncTracking;
using ComicAPI.DTOs.SyncTracking;
using HtmlAgilityPack;

namespace ComicAPI.Services.SyncTracking
{
    public class CrawlerService : ICrawlerService
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<CrawlerService> _logger;

        public CrawlerService(IHttpClientFactory httpClientFactory, ILogger<CrawlerService> logger)
        {
            _httpClient = httpClientFactory.CreateClient("NoRedirectClient");
            _logger = logger;
            // Configure HttpClient
            _httpClient.DefaultRequestHeaders.Add("User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        public async Task<bool> TestLoginAsync(ComicSource site, SyncCredentialsDTO credentials)
        {
            _logger.LogInformation($"Testing login for {site} with username: {credentials.username}");

            try
            {
                return site switch
                {
                    ComicSource.NetTruyen => await TestNettruyenLoginAsync(credentials),
                    ComicSource.TruyenQQ => await TestTruyenqqLoginAsync(credentials),
                    _ => false
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Login test failed for {site}");
                return false;
            }
        }

        private async Task<bool> TestNettruyenLoginAsync(SyncCredentialsDTO credentials)
        {
            try
            {
                // First, get the login page to extract CSRF token
                var loginPageResponse = await _httpClient.GetAsync("https://nettruyenrr.com/Secure/Login.aspx");
                var loginPageContent = await loginPageResponse.Content.ReadAsStringAsync();

                var doc = new HtmlDocument();
                doc.LoadHtml(loginPageContent);

                var tokenInput = doc.DocumentNode.SelectSingleNode("//input[@name='_token']");
                if (tokenInput == null)
                {
                    _logger.LogError("Could not extract CSRF token from NetTruyen login page");
                    return false;
                }

                var token = tokenInput.GetAttributeValue("value", "");
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogError("CSRF token is empty");
                    return false;
                }

                // Create form data for NetTruyen login
                var formData = new List<KeyValuePair<string, string>>
                {
                    new("_token", token),
                    new("email", credentials.username),
                    new("password", credentials.password),
                    new("ctl00$mainContent$login1$LoginCtrl$RememberMe", "on")
                };

                var formContent = new FormUrlEncodedContent(formData);
                foreach (string cookies in loginPageResponse.Headers.GetValues("Set-Cookie"))
                {
                    if (!string.IsNullOrEmpty(cookies))
                    {
                        _httpClient.DefaultRequestHeaders.Add("Cookie", cookies);
                    }
                }
                // Set cookies from login page

                // Attempt login
                var loginResponse = await _httpClient.PostAsync("https://nettruyenrr.com/Secure/Login.aspx", formContent);
                var loginContent = await loginResponse.Content.ReadAsStringAsync();
                foreach (string cookies in loginResponse.Headers.GetValues("Set-Cookie"))
                {
                    if (!string.IsNullOrEmpty(cookies))
                    {
                        _httpClient.DefaultRequestHeaders.Add("Cookie", cookies);
                    }
                }
                // Check if login was successful
                return loginResponse.Headers.GetValues("Set-Cookie").Count() > 2;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "NetTruyen login test error");
                return false;
            }
        }

        private async Task<bool> TestTruyenqqLoginAsync(SyncCredentialsDTO credentials)
        {
            try
            {
                // TruyenQQ login with form data
                var formData = new List<KeyValuePair<string, string>>
                {
                    new("email", credentials.username),
                    new("password", credentials.password),
                    new("expire", "1")
                };

                var formContent = new FormUrlEncodedContent(formData);
                var loginResponse = await _httpClient.PostAsync("https://truyenqqgo.com/frontend/public/login", formContent);
                var loginContent = await loginResponse.Content.ReadAsStringAsync();
                foreach (string cookies in loginResponse.Headers.GetValues("Set-Cookie"))
                {
                    if (!string.IsNullOrEmpty(cookies))
                    {
                        _httpClient.DefaultRequestHeaders.Add("Cookie", cookies);
                    }
                }
                // Check if login was successful
                return loginResponse.Headers.GetValues("Set-Cookie").Count() > 2;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TruyenQQ login test error");
                return false;
            }
        }

        public async Task<List<TrackedComic>> FetchNettruyenComicsAsync(SyncCredentialsDTO credentials)
        {
            try
            {
                // Đăng nhập trước
                // var loginSuccess = await TestNettruyenLoginAsync(credentials);
                // if (!loginSuccess)
                //     throw new Exception("Failed to login to NetTruyen");

                List<TrackedComic>? comics = new List<TrackedComic>();
                // Gọi trang theo dõi
                for (int i = 0; i < 50; i++)
                {
                    var followedUrl = $"https://nettruyenrr.com/theo-doi?page={i+1}";
                    var followedResponse = await _httpClient.GetAsync(followedUrl);
                    followedResponse.EnsureSuccessStatusCode();
                    var followedContent = await followedResponse.Content.ReadAsStringAsync();

                    var doc = new HtmlDocument();
                    doc.LoadHtml(followedContent);

                    var wrapperNode = doc.DocumentNode.SelectSingleNode("//div[contains(@class, 'comics-followed-page')]");
                    if (wrapperNode == null)
                    {
                        break;
                    }
                    var items = wrapperNode.SelectNodes("//div[contains(@class, 'item') and contains(@class, 'item-follow')]");
                    if(items == null || items.Count == 0)
                    {
                        break;
                    }

                    for (int j = 0; j < items.Count; j++)
                    {
                        var item = items[j];

                        var titleNode = item.SelectSingleNode(".//h3//a");
                        var title = titleNode?.InnerText?.Trim() ?? "";
                        var url = titleNode?.GetAttributeValue("href", "") ?? "";


                        if (!string.IsNullOrWhiteSpace(title) && !string.IsNullOrWhiteSpace(url))
                        {
                            // Chuẩn hóa URL
                            url = Regex.Replace(url, @"^https?:\/\/(www\.)?nettruyen(rr)?\.com", "", RegexOptions.IgnoreCase);

                            comics.Add(new TrackedComic
                            {
                                Id = $"nt-{j}",
                                Title = title,
                                Url = url,
                                Slug = SlugHelper.CreateSlug(title),
                                TotalChapters = 0,
                                Source = ComicSource.NetTruyen
                            });
                        }

                    }


                }

                return comics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching NetTruyen comics");
                throw;
            }
        }

        public async Task<List<TrackedComic>> FetchTruyenqqComicsAsync(SyncCredentialsDTO credentials)
        {
            try
            {
                // Login first
                // var loginSuccess = await TestTruyenqqLoginAsync(credentials);
                // if (!loginSuccess)
                // {
                //     throw new Exception("Failed to login to TruyenQQ");
                // }

                // Fetch user's followed comics
                var followedResponse = await _httpClient.GetAsync("https://truyenqqgo.com/truyen-dang-theo-doi.html");
                var followedContent = await followedResponse.Content.ReadAsStringAsync();

                var doc = new HtmlDocument();
                doc.LoadHtml(followedContent);
                var wrapperNode = doc.DocumentNode.SelectSingleNode("//ul[contains(@class, 'list_grid')]");
                if (wrapperNode == null)
                {
                    return [];
                }
                var comics = new List<TrackedComic>();


                
                var items = wrapperNode.SelectNodes("//li");

                if (items != null)
                {
                    for (int i = 0; i < items.Count; i++)
                    {
                        var titleNode = items[i].SelectSingleNode(".//h3//a");


                        var title = titleNode?.InnerText?.Trim() ?? "";
                        var url = titleNode?.GetAttributeValue("href", "") ?? "";


                        if (!string.IsNullOrEmpty(title) && !string.IsNullOrEmpty(url))
                        {
                            comics.Add(new TrackedComic
                            {
                                Id = $"tqq-{i}",
                                Title = title,
                                Url = url.Replace("https://truyenqqto.com", ""),
                                Slug = SlugHelper.CreateSlug(title),
                                Source = ComicSource.TruyenQQ
                            });
                        }
                        
                    }
                }

                return comics;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching TruyenQQ comics");
                throw;
            }
        }



        public Task<List<TrackedComic>> FetchComicsAsync(ComicSource site, SyncCredentialsDTO credentials)
        {
            return site switch
            {
                ComicSource.NetTruyen => FetchNettruyenComicsAsync(credentials),
                ComicSource.TruyenQQ => FetchTruyenqqComicsAsync(credentials),
                _ => throw new NotImplementedException()
            };
        }
    }
}
