<!-- Header Section -->
<header class="header-section">
  <div class="header-container">
    <app-breadcrumb
      class="breadcrumb-wrapper"
      [Links]="breadcrumbLinks()"
    >
    </app-breadcrumb>
    <app-anouncement></app-anouncement>
  </div>
</header>

<!-- Main Container -->
<div class="main-container" #screenContainer>
  <!-- Chapter Header Component -->
  <div #HeaderContainer>
    <app-chapter-header
      [comic]="comic"
      [mainChapter]="mainChapter"
      [listChapterServerIds]="listChapterServerIds"
      [selectedServerId]="selectedServerId"
      [showAllServers]="showAllServers"
      (reportError)="onHeaderReportError()"
      (serverChange)="onHeaderServerChange($event)"
      (toggleServers)="onHeaderToggleServers()"
    ></app-chapter-header>
  </div>

  <!-- Chapter Controls Component -->
  <div #controlBar>
    <app-chapter-controls
      [listChapters]="listChapters"
      [mainChapter]="mainChapter"
      [isLoading]="isLoading"
      [zoomLevel]="zoomLevel"
      [isLimitZoom]="isLimitZoom"
      [isToggle]="isToggle"
      [isVertical]="isVertical"
      [isAutoNextChapter]="isAutoNextChapter"
      [isNightMode]="isNightMode"
      [topToBottom]="topToBottom()"
      (navigate)="onControlsNavigate($event)"
      (chapterChange)="onControlsChapterChange($event)"
      (zoom)="onControlsZoom($event)"
      (toggleFullscreen)="onControlsToggleFullscreen()"
      (toggleMenu)="onControlsToggleMenu($event)"
      (settingsChange)="onControlsSettingsChange($event)"
    ></app-chapter-controls>
  </div>


  <!-- Banners -->
  @if(isBrowser && !isLoading) {
  <div class="banner-section">
    <app-banner2 [codes]="['2051269', '2051272']"></app-banner2>
    <app-banner3></app-banner3>
  </div>
  }

  <!-- Chapter Reader Component -->
  <div #readingContainer>
    <app-chapter-reader
      #imageContainer
      [listImgs]="listImgs || []"
      [isLoading]="isLoading"
      [isVertical]="isVertical"
      [isNightMode]="isNightMode"
      [isFullScreen]="isFullScreen"
      [zoomLevel]="zoomLevel"
      [defaultWidth]="defaultWidth"
      [comicUrl]="comic.url || ''"
      (scroll)="onReaderScroll($event)"
      (toggleFullscreen)="onReaderToggleFullscreen()"
      (imageEvent)="onReaderImageEvent($event)"
    ></app-chapter-reader>
  </div>

    <!-- Chapter Navigation Component -->
    <div #EndChapter>
      <app-chapter-navigation
        [listChapters]="listChapters"
        [mainChapter]="mainChapter"
        [isLoading]="isLoading"
        (navigate)="onNavigationNavigate($event)"
      ></app-chapter-navigation>
    </div>

    <!-- Bottom Banners -->
    @if(isBrowser && !isLoading) {
    <div class="banner-section">
      <app-banner2 [codes]="['2052076', '2052074']"></app-banner2>
    </div>
    }

    <!-- Comments -->
    @if (isBrowser) {
    <app-comment #commentComponent [comicId]="comic.id" [chapterID]="mainChapter.id"></app-comment>
    }

  <!-- Scroll to Top Button -->
  <button
    (click)="scrollToTop($event)"
    class="scroll-to-top-btn"
    [class.scroll-to-top-visible]="showScrollToTop"
    title="Lên đầu trang"
    type="button"
  >
    <svg class="scroll-to-top-icon" viewBox="0 0 24 24">
      <polyline points="18 15 12 9 6 15" />
    </svg>
    <span class="scroll-to-top-text">Lên đầu</span>
  </button>
</div>
