import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Subscription, Observable, of } from 'rxjs';
import { catchError, map, shareReplay } from 'rxjs/operators';

interface ImageCacheEntry {
  blob: Blob;
  timestamp: number;
  url: string;
}

@Injectable({
  providedIn: 'root',
})
export class ImageService {
  private controllers = new Map<string, Subscription>();
  private imageCache = new Map<string, ImageCacheEntry>();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
  private readonly MAX_CACHE_SIZE = 50; // Maximum number of cached images

  constructor(
    private http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: object
  ) { }

  private imageUrlSubject = new BehaviorSubject<string>('');
  imageUrl$ = this.imageUrlSubject.asObservable();

  updateImageUrl(url: string) {
    this.imageUrlSubject.next(url);
  }

  // Enhanced image loading with caching and optimization
  loadImage(url: string, onload: (res: HttpResponse<Blob>) => void) {
    // Check cache first
    const cachedImage = this.getCachedImage(url);
    if (cachedImage) {
      const response = new HttpResponse({
        body: cachedImage.blob,
        status: 200,
        statusText: 'OK',
        url: url
      });
      onload(response);
      return;
    }

    this.controllers.set(
      url,
      this.http
        .get(url, {
          responseType: 'blob',
          observe: 'response',
        })
        .pipe(
          catchError(error => {
            console.error('Image loading failed:', error);
            return of(null);
          })
        )
        .subscribe((res: HttpResponse<Blob> | null) => {
          if (res && res.body) {
            // Cache the image
            this.cacheImage(url, res.body);
            onload(res);
          }
        })
    );
  }

  // Optimized image loading with lazy loading support
  loadImageOptimized(url: string): Observable<string> {
    const cachedImage = this.getCachedImage(url);
    if (cachedImage) {
      return of(URL.createObjectURL(cachedImage.blob));
    }

    return this.http.get(url, { responseType: 'blob', withCredentials: false, headers: { "access-control-allow-origin": "*" } }).pipe(
      map(blob => {
        this.cacheImage(url, blob);
        return URL.createObjectURL(blob);
      }),
      catchError(error => {
        console.error('Image loading failed:', error);
        return of('/option2.png'); // Fallback image
      }),
      shareReplay(1)
    );
  }

  // Preload images for better performance
  preloadImages(urls: string[]): void {
    if (!isPlatformBrowser(this.platformId)) return;

    urls.forEach(url => {
      if (!this.getCachedImage(url)) {
        const img = new Image();
        img.onload = () => {
          // Convert to blob and cache
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (ctx) {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            canvas.toBlob(blob => {
              if (blob) {
                this.cacheImage(url, blob);
              }
            });
          }
        };
        img.src = url;
      }
    });
  }

  private getCachedImage(url: string): ImageCacheEntry | null {
    const cached = this.imageCache.get(url);
    if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
      return cached;
    }
    if (cached) {
      this.imageCache.delete(url);
    }
    return null;
  }

  private cacheImage(url: string, blob: Blob): void {
    // Clean old cache entries if cache is full
    if (this.imageCache.size >= this.MAX_CACHE_SIZE) {
      this.cleanOldCache();
    }

    this.imageCache.set(url, {
      blob,
      timestamp: Date.now(),
      url
    });
  }

  private cleanOldCache(): void {
    const entries = Array.from(this.imageCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

    // Remove oldest 25% of entries
    const toRemove = Math.floor(entries.length * 0.25);
    for (let i = 0; i < toRemove; i++) {
      this.imageCache.delete(entries[i][0]);
    }
  }

  CancelLoad(url: string) {
    if (this.controllers.has(url)) {
      this.controllers.get(url)!.unsubscribe();
      this.controllers.delete(url);
    }
  }

  CancelAll() {
    this.controllers.forEach((controller) => controller.unsubscribe());
    this.controllers.clear();
  }

  addTimestampToUrl(url: string): string {
    const timestamp = new Date().getTime();
    return `${url}?timestamp=${timestamp}`;
  }

  // Clear cache manually
  clearCache(): void {
    this.imageCache.clear();
  }

  // Get cache statistics
  getCacheStats(): { size: number; maxSize: number; hitRate: number } {
    return {
      size: this.imageCache.size,
      maxSize: this.MAX_CACHE_SIZE,
      hitRate: 0 // Could be implemented with hit/miss tracking
    };
  }
}
