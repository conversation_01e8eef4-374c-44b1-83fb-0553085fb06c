import { DOCUMENT, isPlatformServer } from '@angular/common';
import { Component, HostListener, Inject, OnInit, PLATFORM_ID, Renderer2 } from '@angular/core';
import { ComicService } from '@services/comic.service';
import globalConfig from 'GlobalConfig';

@Component({
    selector: 'app-ad',
    templateUrl: './ad.component.html',
    styleUrl: './ad.component.scss',
    standalone: true
})
export class AdComponent implements OnInit {
  constructor(@Inject(DOCUMENT) private document: Document, @Inject(PLATFORM_ID) private platformId: object, private renderer: Renderer2
    , protected comicService: ComicService) { }

  ads: any[] = [];
  // ngAfterViewInit(): void {
  // }
  ngOnInit(): void {
    // this.comicService.getAds().subscribe((res: any) => {
    //   this.ads = res.data;

    // });
    if (isPlatformServer(this.platformId)) return;
    if (globalConfig.EnableAds) {
      this.loadClickAduScript();
    }
    // this.loadInPageClickaduScript();
    // this.loadVignetteBannerScript();
    // this.loadPopupUnderScript();
  }

  @HostListener('window:load')
  OnLoaded() {

  }
  private loadVignetteBannerScript(): void {//monetag

    const script = this.renderer.createElement('script');
    script.type = 'text/javascript';
    script.innerHTML = `(function(d,z,s){s.src='https://'+d+'/401/'+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})('gizokraijaw.net',8772178,document.createElement('script'))`;

    // Thêm script vào DOM
    this.renderer.appendChild(document.body, script);
  }

  loadPopupUnderScript() {//monetag

    const script = this.renderer.createElement('script');
    script.type = 'text/javascript';
    script.innerHTML = `(function(s,u,z,p){s.src=u,s.setAttribute('data-zone',z),p.appendChild(s);})(document.createElement('script'),'https://shebudriftaiter.net/tag.min.js',8781848,document.body||document.documentElement)`;

    // Append the script element to the DOM
    this.renderer.appendChild(document.body, script);
  }

  private loadClickAduScript(): void {
    const script = this.renderer.createElement('script');
    script.setAttribute('data-cfasync', 'false');
    script.type = 'text/javascript';
    script.src = '//diagramjawlineunhappy.com/t/9/fret/meow4/2051266/b2b26ea2.js';
    this.renderer.appendChild(document.body, script);
  }
  private loadInPageClickaduScript(): void {
    const script = this.renderer.createElement('script');
    script.setAttribute('data-cfasync', 'false');
    script.type = 'text/javascript';
    script.src = '//earringsatisfiedsplice.com/bultykh/ipp24/7/bazinga/2051702';
    script.async = true;


    //<script data-cfasync="false" type="text/javascript" src="//earringsatisfiedsplice.com/bultykh/ipp24/7/bazinga/2051702" async></script>
    this.renderer.appendChild(document.body, script);
  }

}
