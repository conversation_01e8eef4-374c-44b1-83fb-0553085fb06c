// generate data context
using ComicAPI.Enums;
using ComicAPI.Models;
using Microsoft.EntityFrameworkCore;

namespace ComicAPI.Data
{
    public class ComicDbContext : DbContext
    {
        public ComicDbContext(DbContextOptions<ComicDbContext> options)
            : base(options)
        {


        }

        public DbSet<User> Users => Set<User>();
        public DbSet<Comic> Comics => base.Set<Comic>();
        public DbSet<Genre> Genres => Set<Genre>();
        public DbSet<ComicGenre> ComicGenre => Set<ComicGenre>();
        public DbSet<UserFollowComic> UserFollowComics => Set<UserFollowComic>();
        public DbSet<Chapter> Chapters => Set<Chapter>();
        public DbSet<Comment> Comments => Set<Comment>();
        public DbSet<DailyComicView> DailyComicViews => Set<DailyComicView>();
        public DbSet<UserNotification> UserNotifications => Set<UserNotification>();
        public DbSet<Notification> Notifications => Set<Notification>();
        public DbSet<UserVoteComic> UserVoteComics => Set<UserVoteComic>();
        public DbSet<Announcement> Announcements => Set<Announcement>();
        public DbSet<UserActivity> UserActivities => Set<UserActivity>();

        // Quest System DbSets
        public DbSet<QuestTemplate> QuestTemplates => Set<QuestTemplate>();
        public DbSet<UserQuest> UserQuests => Set<UserQuest>();
        public DbSet<UserQuestProgress> UserQuestProgresses => Set<UserQuestProgress>();
        

        // Item System DbSets (Simplified)
        public DbSet<ItemTemplate> ItemTemplates => Set<ItemTemplate>();
        public DbSet<UserInventory> UserInventories => Set<UserInventory>();
        public DbSet<UserEquippedItem> UserEquippedItems => Set<UserEquippedItem>();

        // Chat System DbSets
        public DbSet<Conversation> Conversations => Set<Conversation>();
        public DbSet<Message> Messages => Set<Message>();
        public DbSet<ChapterServer> ChapterServers => Set<ChapterServer>();
        // public DbSet<Page> Pages => Set<Page>();
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {


            base.OnConfiguring(optionsBuilder);
        }
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>().ToTable("_user").HasKey(x => x.ID);

            modelBuilder.Entity<Chapter>(b =>
            {
                b.ToTable("chapter");
                b.HasKey(x => x.ID);
            });
            modelBuilder.Entity<Genre>().ToTable("genre").HasKey(x => x.ID);
            modelBuilder.Entity<Comic>(b =>
            {
                b.ToTable("comic");
                b.HasKey(x => x.ID);
                b.HasMany(e => e.Genres).WithMany().UsingEntity<ComicGenre>();
                b.HasMany(e => e.Chapters).WithOne().HasForeignKey(x => x.ComicID);
                // b.HasQueryFilter(x => !(x.UpdateAt < new DateTime(2018, 1, 1).ToUniversalTime() && x.Chapters.Count < 15));
            });
            modelBuilder.Entity<ComicGenre>(b =>
            {
                b.ToTable("comic_genre");
                b.HasKey(x => new { x.ComicID, x.GenreID });
                b.HasOne(x => x.Comic).WithMany().HasForeignKey(x => x.ComicID);
                b.HasOne(x => x.Genre).WithMany().HasForeignKey(x => x.GenreID);
            }
            );

            modelBuilder.Entity<UserFollowComic>(b =>
            {
                b.ToTable("user_follow_comic");
                b.HasKey(x => new { x.ComicID, x.UserID });
                b.HasOne(x => x.comic).WithMany().HasForeignKey(x => x.ComicID);
            });

            modelBuilder.Entity<UserVoteComic>(b =>
            {
                b.ToTable("user_vote_comic");
                b.HasKey(x => new { x.ComicID, x.UserID });

            });

            modelBuilder.Entity<Comment>(b =>
            {
                b.ToTable("comment");
                b.HasKey(x => new { x.ID });
                b.HasOne(x => x.User).WithMany().HasForeignKey(x => x.UserID);
                b.HasMany(x => x.Replies).WithOne().HasForeignKey(x => x.ParentCommentID);
            });

            modelBuilder.Entity<UserNotification>(b =>
            {
                b.ToTable("user_notification");
                b.HasKey(x => new { x.UserID, x.NtfID });
                b.HasOne(x => x.notification).WithMany().HasForeignKey(x => x.NtfID);
            });
            modelBuilder.Entity<Notification>(b =>
            {
                b.ToTable("notification");
                b.HasKey(x => new { x.ID });
            });
            modelBuilder.Entity<ChapterServer>(b =>
            {
                b.ToTable("chapter_servers");
                b.HasKey(x => new { x.ID });
            });

            modelBuilder.Entity<DailyComicView>(b =>
            {

                b.ToTable("daily_comic_views")
                .HasKey(x => new { x.ComicID, x.ViewDate });
                b.HasOne(x => x.comic).WithMany().HasForeignKey(x => x.ComicID);
            }
            );

            modelBuilder.Entity<Announcement>(b =>
            {
                b.ToTable("announcement");
                b.HasKey(x => new { x.ID });
            });

            // Quest System Entity Configurations
            modelBuilder.Entity<QuestTemplate>(b =>
            {
                b.ToTable("quest_template");
                b.HasKey(x => x.ID);
                b.HasMany(x => x.UserQuests).WithOne(x => x.QuestTemplate).HasForeignKey(x => x.QuestTemplateID);
            });

            modelBuilder.Entity<UserQuest>(b =>
            {
                b.ToTable("user_quest");
                b.HasKey(x => x.ID);
                b.HasOne(x => x.User).WithMany().HasForeignKey(x => x.UserID);
                b.HasOne(x => x.QuestTemplate).WithMany(x => x.UserQuests).HasForeignKey(x => x.QuestTemplateID);
                b.HasMany(x => x.ProgressHistory).WithOne(x => x.UserQuest).HasForeignKey(x => x.UserQuestID);
            });

            modelBuilder.Entity<UserQuestProgress>(b =>
            {
                b.ToTable("user_quest_progress");
                b.HasKey(x => x.ID);
                b.HasOne(x => x.UserQuest).WithMany(x => x.ProgressHistory).HasForeignKey(x => x.UserQuestID);
            });


          

            // Item System Entity Configurations (Simplified)
            modelBuilder.Entity<ItemTemplate>(b =>
            {
                b.ToTable("item_template");
                b.HasKey(x => x.ID);
                b.HasMany(x => x.UserInventories).WithOne(x => x.ItemTemplate).HasForeignKey(x => x.ItemTemplateID);
                b.HasMany(x => x.UserEquippedItems).WithOne(x => x.ItemTemplate).HasForeignKey(x => x.ItemTemplateID);
            });

            modelBuilder.Entity<UserInventory>(b =>
            {
                b.ToTable("user_inventory");
                b.HasKey(x => x.ID);
                b.HasOne(x => x.User).WithMany().HasForeignKey(x => x.UserID);
                b.HasOne(x => x.ItemTemplate).WithMany(x => x.UserInventories).HasForeignKey(x => x.ItemTemplateID);
                b.HasIndex(x => new { x.UserID, x.ItemTemplateID }).IsUnique();
            });

            modelBuilder.Entity<UserEquippedItem>(b =>
            {
                b.ToTable("user_equipped_items");
                b.HasKey(x => new { x.UserID, x.SlotType });
                b.HasOne(x => x.User).WithMany().HasForeignKey(x => x.UserID);
                b.HasOne(x => x.ItemTemplate).WithMany(x => x.UserEquippedItems).HasForeignKey(x => x.ItemTemplateID);
            });

            // Chat System Entity Configurations
            modelBuilder.Entity<Conversation>(b =>
            {
                b.ToTable("conversations");
                b.HasKey(x => x.ID);
                b.HasMany(x => x.Messages).WithOne(x => x.Conversation).HasForeignKey(x => x.ConversationId);
            });

            modelBuilder.Entity<Message>(b =>
            {
                b.ToTable("messages");
                b.HasKey(x => x.ID);
                b.HasOne(x => x.User).WithMany().HasForeignKey(x => x.UserId);
                b.HasOne(x => x.Conversation).WithMany(x => x.Messages).HasForeignKey(x => x.ConversationId);
            });

            // User Activity Entity Configuration
            modelBuilder.Entity<UserActivity>(b =>
            {
                b.ToTable("user_activity");
                b.HasKey(x => x.ID);
                b.HasOne(x => x.User).WithMany().HasForeignKey(x => x.UserId);
                b.HasIndex(x => x.UserId);
                b.HasIndex(x => x.Timestamp);
                b.HasIndex(x => new { x.UserId, x.Timestamp });
                b.Property(x => x.ActivityType).HasMaxLength(50);
                b.Property(x => x.Endpoint).HasMaxLength(255);
                b.Property(x => x.IpAddress).HasMaxLength(50);
                b.Property(x => x.UserAgent).HasMaxLength(500);
                b.Property(x => x.SessionId).HasMaxLength(100);
            });

            modelBuilder.HasDbFunction(() => GetLatestChapter(default))
            .HasName("get_latest_chapter");

            // modelBuilder.HasDbFunction(() => GetLatestChapter(default))
            // .HasName("app_notify_new_chapter_to_followers");

        }
        [DbFunction("public", "get_latest_chapter")]
        public virtual IQueryable<Chapter> GetLatestChapter(int comicId)
        {
            var parameter = new Npgsql.NpgsqlParameter("comic_id", comicId);
            return this.Set<Chapter>().FromSqlRaw("SELECT * FROM get_latest_chapter(@comic_id)", parameter);
        }

        public IQueryable<Comic> GetTopDailyComics(TopViewType topViewType = TopViewType.Day)
        {
            int day = 0;
            switch (topViewType)
            {
                case TopViewType.Day:
                    day = 1;
                    break;
                case TopViewType.Week:
                    day = 7;
                    break;
                case TopViewType.Month:
                    day = 30;
                    break;
            }
            var dateTime = DateTime.UtcNow.Date.AddDays(-day);
            var result = this.Comics
                .GroupJoin(this.DailyComicViews
                .Where(dcv => dcv.ViewDate > dateTime)
                .GroupBy(dcv => dcv.ComicID)
                .Select(g => new
                {
                    ComicID = g.Key,
                    TotalDailyViewCount = g.Sum(dcv => dcv.ViewCount)
                }),
            comic => comic.ID,
            dcvGroup => dcvGroup.ComicID,
            (comic, dcvGroup) => new
            {
                Comic = comic,
                TotalDailyViewCount = dcvGroup.FirstOrDefault() != null ? dcvGroup.FirstOrDefault()!.TotalDailyViewCount : 0
            })
            .OrderByDescending(result => result.TotalDailyViewCount)
            .ThenByDescending(result => result.Comic.ViewCount)
            .Select(result => result.Comic);

            return result;
        }
    }
}