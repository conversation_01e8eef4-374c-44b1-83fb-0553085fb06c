import { isPlatformBrowser } from '@angular/common';
import { Component, ElementRef, Inject, Input, OnD<PERSON>roy, OnInit, PLATFORM_ID, Renderer2, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
    selector: 'app-banner2',
    templateUrl: './banner2.component.html',
    styleUrl: './banner2.component.scss',
    standalone: true
})
export class Banner2Component implements OnInit, OnDestroy {
  @Input()
  codes: string[] = [];
  isLoading = true;
  @ViewChild('banner2', { static: true }) adContainer!: ElementRef;
  constructor(private renderer: Renderer2, private el: ElementRef, @Inject(PLATFORM_ID) private platformId: object
    , private route: ActivatedRoute
  ) { }

  ngOnInit() {
    // Tải script cho quảng cáo
    if (isPlatformBrowser(this.platformId)) {
      for (const code of this.codes) {
        this.loadAdScript('//chaseherbalpasty.com/lv/esnk/' + code + '/code.js', '__clb-' + code);
      }
      // this.loadAdScript('//chaseherbalpasty.com/lv/esnk/2051269/code.js', '__clb-2051269');
      // this.loadAdScript('//chaseherbalpasty.com/lv/esnk/2051272/code.js', '__clb-2051272');
    }
  }

  ngOnDestroy() {
    // Xóa các script tìm được
    const adScripts = Array.from(document.querySelectorAll('script'))
      .filter(script => {
        // script.src.includes('2051269')  || script.src.includes('2051272') // Đường dẫn script quảng cáo
        for (const code of this.codes) {
          if (script.src.includes(code))
            return true;
        }
        return false;

      }
      );
    adScripts.forEach(script => script.remove());
  }

  // Phương thức để tải script
  private loadAdScript(src: string, className: string) {
    // Create the script element
    const script = this.renderer.createElement('script');
    script.type = 'text/javascript';
    script.src = src;
    script.async = true;
    script.dataset.cfasync = 'false';
    script.className = className;
    this.renderer.appendChild(this.adContainer.nativeElement, script);
  }
}
