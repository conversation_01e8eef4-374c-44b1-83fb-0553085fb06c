using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using ComicAPI.Enums;

namespace ComicAPI.Models
{
    public class Conversation
    {
        [Key, Column("id")]
        public Guid ID { get; set; } = Guid.NewGuid();

        [<PERSON><PERSON><PERSON><PERSON>(255), Column("name")]
        public string? Name { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [Column("channel"),]
        public ChatChannel Channel { get; set; } = ChatChannel.Admin;

        [<PERSON><PERSON>ey("userid"), Column("userid")]
        public int? UserId { get; set; }
        [ForeignKey("hostid"), Column("hostid")]
        public int? HostId { get; set; }

        [Column("icon")]
        public string? Icon { get; set; }

        // Navigation properties
        public List<Message> Messages { get; set; } = new List<Message>();
    }
}
