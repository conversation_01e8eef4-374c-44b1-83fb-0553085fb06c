<div class="grid-container">
  <!-- Header -->
  <header class="grid-header">
    <div class="title-section">
      <ng-template [ngTemplateOutlet]="iconTemplate || null"></ng-template>
      <h2 class="block-title">{{ title }}</h2>
    </div>

    <!-- Grid Type Switch -->
    <div class="grid-switch">
      <div class="switch-track">
        <div
          class="switch-thumb"
          [class.grid-mode]="girdType() === 0"
          [class.list-mode]="girdType() === 1"
        ></div>
      </div>

      <button
        class="switch-btn"
        [class.active]="girdType() === 1"
        (click)="changeGridType($event.target, 1)"
        title="Xem dạng danh sách"
        aria-label="Chuyển sang chế độ xem danh sách"
      >
        <svg class="switch-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <rect x="3" y="4" width="18" height="4" rx="1" />
          <rect x="3" y="10" width="18" height="4" rx="1" />
          <rect x="3" y="16" width="18" height="4" rx="1" />
        </svg>
      </button>

      <button
        class="switch-btn"
        [class.active]="girdType() === 0"
        (click)="changeGridType($event.target, 0)"
        title="Xem dạng lưới"
        aria-label="Chuyển sang chế độ xem lưới"
      >
        <svg class="switch-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
          <rect x="3" y="3" width="7" height="7" rx="1" />
          <rect x="14" y="3" width="7" height="7" rx="1" />
          <rect x="3" y="14" width="7" height="7" rx="1" />
          <rect x="14" y="14" width="7" height="7" rx="1" />
        </svg>
      </button>
    </div>
  </header>
</div>
<!-- Grid Content -->
<ng-container *ngIf="girdType() === 0; else listView">
  <div [className]="_class">
    <div
      *ngFor="let comic of _listComics(); let i = index; trackBy: trackByComicId"
      class="grid-item"
    >
      <app-comic-card
        [comic]="comic"
        (comicHover)="onHoverComic($event)"
        [topRightTemplate]="actionTemplate"
      />
    </div>
  </div>
</ng-container>

<ng-template #listView>
  <div class="list-grid">
    <div
      *ngFor="let comic of _listComics(); let i = index; trackBy: trackByComicId"
      class="list-item-v2"
    >
      <app-comic-card-v2 [comic]="comic" />
    </div>
  </div>
</ng-template>


<!-- Comic Detail Popup -->
<app-popup-detail-comic
  *ngIf="hoverComic() && isBrowser"
  class="comic-popup"
  [comic]="hoverComic()!"
/>


<ng-content></ng-content>
