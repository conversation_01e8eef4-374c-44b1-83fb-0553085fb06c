// src/app/toast.service.ts
import { isPlatformBrowser } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import { IUser, SettingOption, SettingType } from '@schema';
import globalConfig from 'GlobalConfig';
import { SsrCookieService } from 'ngx-cookie-service-ssr';

@Injectable({
  providedIn: 'root',
})
export class StorageService {

  constructor(
    @Inject(PLATFORM_ID) private platformId: object,
    private ssrCookieService: SsrCookieService,
  ) {


  }

  SaveSetting(setting: Map<SettingType, SettingOption>) {
    if (isPlatformBrowser(this.platformId)) {
      const jsonObject: Record<string, SettingOption> = {};
      setting.forEach((value, key) => {
        jsonObject[key] = value.value;
      });
      localStorage.setItem('setting', JSON.stringify(jsonObject));
    }
  }

  LoadSetting(setting: Map<SettingType, SettingOption>) {
    if (isPlatformBrowser(this.platformId)) {
      let settingObject = JSON.parse(localStorage.getItem('setting') || '{}');

      setting.forEach((value, key) => {
        value.value = settingObject[key] ?? value.value;
      });
    }
  }

  GetSetting() {
    if (isPlatformBrowser(this.platformId)) {
      let setting = localStorage.getItem('setting');
      if (setting) {
        return JSON.parse(setting);
      }
    }
    return null;
  }

  GetGridType() {
    const type = Number(this.ssrCookieService.get('gridType') || 0);
    return type
  }
  SetGridType(type: number) {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('gridType', type.toString(), 365, '/');
    }
  }

  GetUserData() {
    const user = this.ssrCookieService.get('auth');
    if (user && user != '') {
      return JSON.parse(user);
    }
    return null;
  }

  SetUserData(user: IUser) {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('auth', JSON.stringify(user), 365, '/');
    }
  }

  SetZoomImage(zoom: number) {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('zoom', zoom.toString(), 365, '/');
    }
  }

  GetZoomImage() {
    if (isPlatformBrowser(this.platformId)) {
      const zoom = this.ssrCookieService.get('zoom');
      if (zoom) {
        return Number(zoom);
      }
      else {
        if (window.innerWidth < globalConfig.GetScreenSize('lg')) {
          return 0
        }
      }
    }
    return -0.3;
  }

  Logout() {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('auth', '', 365, '/');
    }
  }

  GetUserDeconfirm() {
    if (isPlatformBrowser(this.platformId)) {
      const user = localStorage.getItem('confrimauth');
      if (user) {
        return JSON.parse(user);
      }
    }
    return null;
  }

  SetUserDeconfirm(user: IUser) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('confrimauth', JSON.stringify(user));
    }
  }
  GetRememberMeData() {
    if (isPlatformBrowser(this.platformId)) {
      const rememberMeData = localStorage.getItem('rememberMeData');
      if (rememberMeData) {
        return JSON.parse(rememberMeData);
      }
    }
    return null;
  }

  SetRememberMeData(rememberMeData: any) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('rememberMeData', JSON.stringify(rememberMeData));
    }
  }
  GetHistory() {
    if (isPlatformBrowser(this.platformId)) {
      const history = localStorage.getItem('history');
      if (history) {
        return JSON.parse(history as string);
      }
    }
    return [];
  }

  SetHistory(history: any) {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem('history', JSON.stringify(history));
    }
  }

  IsDarkTheme() {
    return this.ssrCookieService.get('isDarkTheme') === 'true';
  }

  SetDarkTheme(isDarkTheme: boolean) {
    if (isPlatformBrowser(this.platformId)) {
      this.ssrCookieService.set('isDarkTheme', isDarkTheme.toString(), 365, '/');
    }
  }
}
