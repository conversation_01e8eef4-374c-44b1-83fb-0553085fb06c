// eslint.config.js
const tseslint = require("typescript-eslint");
const parser = require("@typescript-eslint/parser");
const unusedImports = require("eslint-plugin-unused-imports");
const angular = require("angular-eslint");
const eslint = require("@eslint/js");

// const angular = require("angular-eslint");
module.exports = [
  ...tseslint.config(
    {
      files: ["**/*.ts"],
      languageOptions: {
        parser,
        parserOptions: {
          project: "./tsconfig.json",
        },
      },
      plugins: {
        "@typescript-eslint": tseslint.plugin,
        "unused-imports": unusedImports,
        "angular-eslint": angular,
      },
      rules: {
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
          "warn",
          {
            vars: "all",
            varsIgnorePattern: "^_",
            argsIgnorePattern: "^_",
          },
        ],
      },
    },
    {
      files: ["**/*.ts"],
      extends: [
        eslint.configs.recommended,
        ...tseslint.configs.recommended,
        ...tseslint.configs.stylistic,
        ...angular.configs.tsRecommended,
      ],
      processor: angular.processInlineTemplates,
      rules: {
        "@angular-eslint/prefer-standalone": "off",
        "@angular-eslint/directive-selector": [
          "error",
          {
            type: "attribute",
            prefix: "app",
            style: "camelCase",
          },
        ],
        "@angular-eslint/component-selector": [
          "error",
          {
            type: "element",
            prefix: "app",
            style: "kebab-case",
          },
        ],
      },
    },
    {
      files: ["**/*.html"],
      extends: [
        ...angular.configs.templateRecommended,
        ...angular.configs.templateAccessibility,
      ],
      rules: {},
    }
  ),
];
