import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChapterListComponent } from './page/list-chapter/chapter-list.component';
import { ComicDetailComponent } from './comic-detail.component';
// import { TopListComponent } from './page/top-list/top-list.component';
import { RouterModule } from '@angular/router';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { ReactiveFormsModule } from '@angular/forms';
import { ComicResolver } from 'src/app/resolvers/comic.resolver';
import { AdsModule } from 'src/app/shared/ads.module';
import { TopListComponent } from '@components/common/top-list/top-list.component';
import { CommentComponent } from '@components/common/comment/comment.component';
import { ComicDescriptionlPipe } from '@pines/description.pipe';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { NumeralPipe } from '@pines/numeral.pipe';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';

@NgModule({
    declarations: [
        ComicDetailComponent,
    ], imports: [RouterModule.forChild([{
        path: '', component: ComicDetailComponent, resolve: { comicRes: ComicResolver }
    }]),
        CommonModule,
        ReactiveFormsModule,
        AdsModule,
        ChapterListComponent,
        TopListComponent,
        CommentComponent,
        ComicDescriptionlPipe,
        DateAgoPipe,
        NumeralPipe,
        BreadcrumbComponent,
        GridComicComponent,
        ], providers: [provideHttpClient(withInterceptorsFromDi())]
})
export class DetailModule { }
