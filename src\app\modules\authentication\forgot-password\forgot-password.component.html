<!-- Modern Comic-themed Forgot Password Container -->
<div class="auth-container">
  <!-- Background Elements -->
  <div class="auth-background">
    <div class="comic-bubbles">
      <div class="bubble bubble-1">🔑</div>
      <div class="bubble bubble-2">💭</div>
      <div class="bubble bubble-3">📧</div>
      <div class="bubble bubble-4">🔒</div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="auth-content">
    <!-- Left Side - Branding -->
    <div class="auth-branding">
      <div class="brand-content">
        <div class="brand-logo">
          <div class="logo-icon">🔐</div>
          <h1 class="brand-title">Khôi phục <span class="brand-accent">tài khoản</span></h1>
        </div>
        <p class="brand-subtitle">Đừng lo lắng, chúng tôi sẽ giúp bạn lấy lại mật khẩu</p>
        <div class="brand-features">
          <div class="feature-item">
            <span class="feature-icon">⚡</span>
            <span class="feature-text">Khôi phục nhanh chóng</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🔒</span>
            <span class="feature-text">Bảo mật tuyệt đối</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📱</span>
            <span class="feature-text">Hướng dẫn chi tiết</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Side - Forgot Password Form -->
    <div class="auth-form-container">
      <div class="auth-form-card">
        <!-- Success State -->
        <div *ngIf="isSuccess" class="success-state">
          <div class="success-icon">✅</div>
          <h2 class="success-title">Email đã được gửi!</h2>
          <p class="success-message">
            Chúng tôi đã gửi hướng dẫn khôi phục mật khẩu đến email của bạn.
            Vui lòng kiểm tra hộp thư và làm theo hướng dẫn.
          </p>
          <div class="success-actions">
            <a [routerLink]="['/auth/login']" class="back-to-login">
              <span class="button-icon">🔙</span>
              <span class="button-text">Quay lại đăng nhập</span>
            </a>
          </div>
        </div>

        <!-- Form State -->
        <div *ngIf="!isSuccess">
          <!-- Header -->
          <div class="form-header">
            <h2 class="form-title">Quên mật khẩu?</h2>
            <p class="form-subtitle">
              Nhập email của bạn và chúng tôi sẽ gửi hướng dẫn khôi phục mật khẩu
            </p>
          </div>

          <!-- Forgot Password Form -->
          <form class="auth-form" (submit)="onSubmit()" [formGroup]="form">
            <!-- Email Field -->
            <div class="form-group">
              <label for="email" class="form-label">
                <span class="label-icon">📧</span>
                Email đã đăng ký
              </label>
              <div class="input-wrapper">
                <input
                  id="email"
                  name="email"
                  type="email"
                  formControlName="email"
                  placeholder="Nhập địa chỉ email của bạn"
                  class="form-input"
                  autocomplete="email"
                  required
                />
                <div class="input-icon">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
              </div>
              <div class="error-message" *ngIf="isControlInvalid('email')">
                <span *ngIf="form.get('email')?.hasError('required')">
                  Vui lòng nhập địa chỉ email
                </span>
                <span *ngIf="form.get('email')?.hasError('email') && submitted">
                  Định dạng email không hợp lệ
                </span>
              </div>
            </div>

            <!-- Submit Button -->
            <button
              type="submit"
              class="submit-button"
              [disabled]="form.invalid"
            >
              <span class="button-content">
                <span class="button-icon">📤</span>
                <span class="button-text">Gửi hướng dẫn khôi phục</span>
              </span>
            </button>
          </form>

          <!-- Back to Login -->
          <div class="auth-switch">
            <p class="switch-text">
              Nhớ lại mật khẩu?
              <a [routerLink]="['/auth/login']" class="switch-link">
                Đăng nhập ngay
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
