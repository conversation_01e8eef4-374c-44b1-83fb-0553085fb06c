
.inventory-container {
  @apply max-w-7xl mx-auto space-y-6;
}

// Header
.inventory-header {
  @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  @apply bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-sm;
}

.inventory-header__title {
  @apply space-y-1;
}

.inventory-title {
  @apply text-2xl font-bold text-neutral-900 dark:text-neutral-100;
}

.inventory-subtitle {
  @apply text-neutral-600 dark:text-neutral-400;
}



// Tabs
.inventory-tabs {
  @apply bg-white dark:bg-neutral-800 rounded-lg p-2 shadow-sm;
  @apply flex flex-wrap gap-2;
}

.tab-btn {
  @apply flex items-center gap-2 px-4 py-3 rounded-lg transition-all duration-200;
  @apply text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100;
  @apply hover:bg-neutral-50 dark:hover:bg-neutral-700;
  @apply border border-transparent;
  @apply min-w-0 flex-1 sm:flex-none;

  &--active {
    @apply shadow-lg shadow-primary-50;
    @apply border-red-200 dark:border-red-700;
    @apply transform scale-105;

    .tab-count {
      @apply bg-white/20 text-white;
    }

    i {
      @apply text-white;
    }
  }

  i {
    @apply text-lg;
  }

  span:not(.tab-count) {
    @apply font-medium text-sm;
  }
}

.tab-count {
  @apply bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
  @apply px-2 py-1 rounded-full text-xs font-semibold min-w-[1.5rem] text-center;
  @apply transition-colors duration-200;
}

// Responsive tabs
@screen sm {
  .inventory-tabs {
    @apply justify-start;
  }

  .tab-btn {
    @apply flex-none;
  }
}

// Filters
.inventory-filters {
  @apply bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-sm;
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4;
}

.filter-group {
  @apply space-y-2;
}

.filter-label {
  @apply block text-sm font-medium text-neutral-700 dark:text-neutral-300;
}

.filter-input, .filter-select {
  @apply w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600;
  @apply bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100;
  @apply rounded-md shadow-sm focus:ring-2 focus:ring-sky-500 focus:border-sky-500;
  @apply text-sm;
}

// Selected Actions
.selected-actions {
  @apply bg-sky-50 dark:bg-sky-900/20 border border-sky-200 dark:border-sky-700;
  @apply rounded-lg p-4 flex items-center justify-between;
}

.selected-info {
  @apply text-sky-700 dark:text-sky-300 font-medium;
}

.selected-buttons {
  @apply flex gap-2;
}

.action-btn {
  @apply px-4 py-2 rounded-md text-sm font-medium transition-colors;

  &--secondary {
    @apply bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
    @apply hover:bg-neutral-200 dark:hover:bg-neutral-600;
  }
}

// Loading
.loading-container {
  @apply flex flex-col items-center justify-center py-12 space-y-4;
  @apply text-neutral-500 dark:text-neutral-400;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-neutral-300 border-t-sky-500 rounded-full animate-spin;
}

// Empty State
.empty-state {
  @apply text-center py-12 space-y-4;
}

.empty-state__icon {
  @apply text-6xl text-neutral-300 dark:text-neutral-600;
}

.empty-state__title {
  @apply text-xl font-semibold text-neutral-900 dark:text-neutral-100;
}

.empty-state__description {
  @apply text-neutral-600 dark:text-neutral-400 max-w-md mx-auto;
}

// Items Grid
.inventory-content {
  @apply bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-sm;
}

.items-grid {
  @apply grid gap-4;

  &--small {
    @apply grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10;
  }

  &--medium {
    @apply grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5;
  }

  &--large {
    @apply grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
}

// Items List
.items-list {
  @apply space-y-2;
}

.list-header {
  @apply grid grid-cols-5 gap-4 p-3 bg-neutral-50 dark:bg-neutral-700 rounded-lg;
  @apply text-sm font-medium text-neutral-700 dark:text-neutral-300;
}

.list-body {
  @apply space-y-1;
}

.list-row {
  @apply grid grid-cols-5 gap-4 p-3 rounded-lg transition-colors;
  @apply hover:bg-neutral-50 dark:hover:bg-neutral-700;

  &--selected {
    @apply bg-sky-50 dark:bg-sky-900/20;
  }

  &--equipped {
    @apply bg-lime-50 dark:bg-lime-900/20;
  }
}

.list-col {
  @apply flex items-center;

  &--item {
    @apply gap-3;
  }

  &--actions {
    @apply gap-2 justify-end;
  }
}

.list-checkbox {
  @apply w-4 h-4 text-sky-600 bg-neutral-100 border-neutral-300 rounded;
  @apply focus:ring-sky-500 dark:focus:ring-sky-600 dark:ring-offset-neutral-800;
  @apply dark:bg-neutral-700 dark:border-neutral-600;
}

.list-item-icon {
  @apply w-10 h-10 rounded-lg object-cover;
}

.list-item-info {
  @apply flex flex-col min-w-0;
}

.list-item-name {
  @apply font-medium text-neutral-900 dark:text-neutral-100 truncate;
}

.list-item-description {
  @apply text-sm text-neutral-600 dark:text-neutral-400 truncate;
}

.category-badge, .rarity-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.category-badge {
  @apply bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
}

.list-action-btn {
  @apply px-3 py-1 rounded text-xs font-medium transition-colors;

  &--primary {
    @apply bg-sky-500 text-white hover:bg-sky-600;
  }

  &--secondary {
    @apply bg-neutral-100 dark:bg-neutral-700 text-neutral-700 dark:text-neutral-300;
    @apply hover:bg-neutral-200 dark:hover:bg-neutral-600;
  }

  &--ghost {
    @apply text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-neutral-100;
  }
}

// Pagination
.inventory-pagination {
  @apply flex items-center justify-center gap-4 py-4;
}

.pagination-btn {
  @apply px-4 py-2 bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-600;
  @apply text-neutral-700 dark:text-neutral-300 rounded-md hover:bg-neutral-50 dark:hover:bg-neutral-700;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

.pagination-info {
  @apply text-sm text-neutral-600 dark:text-neutral-400;
}

// Responsive adjustments




// Icon styles for tabs
.icon-backpack::before {
  content: "🎒";
}

.icon-equipped::before {
  content: "⚔️";
}

.icon-catalog::before {
  content: "📋";
}

.icon-grid::before {
  content: "⊞";
}

.icon-list::before {
  content: "☰";
}

.icon-empty-box::before {
  content: "📦";
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .inventory-container {
    @apply bg-neutral-900;
  }
}
