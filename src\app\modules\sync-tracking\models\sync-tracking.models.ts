// ===== SYNC TRACKING MODELS =====

export interface SyncCredentials {
  nettruyen: {
    username: string;
    password: string;
  };
  truyenqq: {
    username: string;
    password: string;
  };
}

export interface SyncProgress {
  stage: SyncStage;
  progress: number; // 0-100
  message: string;
  currentSite?: string;
  currentAction?: string;
}

export enum SyncStage {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  FETCHING_NETTRUYEN = 'fetching_nettruyen',
  FETCHING_TRUYENQQ = 'fetching_truyenqq',
  COMPARING = 'comparing',
  SYNCING = 'syncing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

export interface TrackedComic {
  id: string;
  title: string;
  url: string;
  lastReadChapter: string;
  lastReadDate: Date;
  totalChapters: number;
  status: ComicStatus;
  thumbnail?: string;
  source: 'nettruyen' | 'truyenqq';
}

export enum ComicStatus {
  READING = 'reading',
  COMPLETED = 'completed',
  DROPPED = 'dropped',
  PLAN_TO_READ = 'plan_to_read'
}

export interface SyncResult {
  success: boolean;
  totalComics: number;
  syncedComics: number;
  errors: SyncError[];
  summary: SyncSummary;
  duration: number; // in seconds
}

export interface SyncError {
  comicId: string;
  comicTitle: string;
  error: string;
  source: 'nettruyen' | 'truyenqq';
}

export interface SyncSummary {
  nettruyenComics: number;
  truyenqqComics: number;
  newComics: number;
  updatedComics: number;
  conflictComics: number;
}

export interface SyncConflict {
  comicId: string;
  title: string;
  nettruyen: TrackedComic;
  truyenqq: TrackedComic;
  resolution?: 'keep_nettruyen' | 'keep_truyenqq' | 'merge';
}

export interface SyncSettings {
  autoResolveConflicts: boolean;
  preferredSource: 'nettruyen' | 'truyenqq' | 'latest';
  syncInterval: number; // in hours
  enableNotifications: boolean;
  backupBeforeSync: boolean;
}

export interface SyncHistory {
  id: string;
  timestamp: Date;
  result: SyncResult;
  settings: SyncSettings;
}
