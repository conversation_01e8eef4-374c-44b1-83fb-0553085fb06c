// ===== SYNC TRACKING MODELS =====

export interface SyncCredentials {
  sourceFrom: 'nettruyen' | 'truyenqq';
  sourceTo: 'local'; // Always sync to local database
  method: 'account' | 'script';
  // For account method
  username?: string;
  password?: string;
  // For script method
  scriptData?: string;
}

export interface SyncProgress {
  stage: SyncStage;
  progress: number; // 0-100
  message: string;
  currentSite?: string;
  currentAction?: string;
  startTime?: Date;
  endTime?: Date;
  totalComics?: number;
  processedComics?: number;
  errors?: string[];
}

export enum SyncStage {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  FETCHING = 'fetching',
  COMPARING = 'comparing',
  SYNCING = 'syncing',
  COMPLETED = 'completed',
  ERROR = 'error'
}

export interface SyncResult {
  success: boolean;
  totalComics: number;
  syncedComics: number;
  errors: SyncError[];
  summary: SyncSummary;
  duration: number; // in seconds
}

export interface SyncError {
  comicId: string;
  comicTitle: string;
  error: string;
  source: 'nettruyen' | 'truyenqq';
}

export interface SyncSummary {
  comics: number;
  newComics: number;
  updatedComics: number;
  conflictComics: number;
}

export interface SyncConflict {
  comicId: string;
  title: string;
  resolution?: 'keep_nettruyen' | 'keep_truyenqq' | 'merge';
}

export interface SyncSettings {
  autoResolveConflicts: boolean;
  preferredSource: 'nettruyen' | 'truyenqq' | 'latest';
  syncInterval: number; // in hours
  enableNotifications: boolean;
  backupBeforeSync: boolean;
}

export interface SyncHistory {
  id: string;
  timestamp: Date;
  result: SyncResult;
  settings: SyncSettings;
}
