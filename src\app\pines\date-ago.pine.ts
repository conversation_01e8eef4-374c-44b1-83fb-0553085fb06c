import { Pipe, PipeTransform } from '@angular/core';
import { DateTimeService } from '@services/datetime.service';

// moment.tz.setDefault('Asia/Ho_Chi_Minh');


@Pipe({
    name: 'dateAgo',
    standalone: true
})
export class DateAgoPipe implements PipeTransform {
  constructor(private dateTimeService: DateTimeService) { }
  transform(date?: string | Date | number, ago = 'trước', offset = 0): string {
    if (!date) { 
      return ''
    }
    const dateTime = new Date(date);
    const now = new Date();
    const seconds = (now.getTime() - dateTime.getTime()) / 1000 - offset;
    const isOverMonth = seconds >= 3600 * 24 * 30;
    if (isOverMonth) {
      return this.dateTimeService.formatDateTo(dateTime);
    }
    const days = Math.floor(seconds / 3600 / 24);
    const hours = Math.floor(seconds / 3600) % 24;
    const minutes = Math.floor(seconds / 60) % 60;
    if (days > 0) {
      return days + ' ngày ' + ago;
    }
    if (hours > 0) {
      return hours + ' giờ ' + ago;
    }
    if (minutes > 0) {
      return minutes + ' phút ' + ago;
    }
    return 1 + ' phút ' + ago;

  }

}
