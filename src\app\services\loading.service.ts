import { Injectable, inject } from '@angular/core';
import { NavigationStart, Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { distinctUntilChanged, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class LoadingService {
  private readonly router = inject(Router);

  // Private state management
  private readonly tasksSubject = new BehaviorSubject<string[]>([]);

  // Public observables
  readonly tasks$ = this.tasksSubject.asObservable();


  readonly taskCount$ = this.tasks$.pipe(
    map(tasks => tasks.length),
    distinctUntilChanged()
  );

  // Getter for current tasks (for backward compatibility)
  get tasks(): string[] {
    return this.tasksSubject.value;
  }

  constructor() {
    this.setupRouterEvents();
  }

  private setupRouterEvents(): void {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.clearAll();
      }
    });
  }

  addTask(task: string, options?: {
    timeout?: number;
  }): void {

    if (this.tasks.includes(task)) {
      return;
    }
    const currentTasks = this.tasksSubject.value;
    this.tasksSubject.next([...currentTasks, task]);
  }

  removeTask(task: string): void {
    const currentTasks = this.tasksSubject.value;
    const filteredTasks = currentTasks.filter(t => t !== task);

    if (filteredTasks.length !== currentTasks.length) {
      this.tasksSubject.next(filteredTasks);
    }
  }

  clearAll(): void {
    if (this.tasksSubject.value.length > 0) {
      this.tasksSubject.next([]);
    }
  }

  isLoading(): boolean {
    return this.tasks.length > 0;
  }
  
  getTaskCount(): number {
    return this.tasks.length;
  }
}
