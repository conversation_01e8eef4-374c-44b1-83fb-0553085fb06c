import { CommonModule, isPlatformBrowser } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  HostListener,
  Inject,
  Input,
  OnInit,
  PLATFORM_ID,
  SimpleChange,
  ViewChild, OnChanges,
  SimpleChanges,
} from '@angular/core';
import { RouterModule } from '@angular/router';
import { LoopScrollComponent } from '@components/common/loop-scroll/loop-scroll.component';
import { SelectionComponent } from '@components/common/selection/selection.component';
import { SpinnerComponent } from '@components/common/spinner/spinner.component';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { Chapter, Comic } from '@schema';
import { HistoryService } from '@services/history.service';
import config from 'GlobalConfig';
import { IOption } from 'src/app/dataSource/schema/IOption';

@Component({
  selector: 'app-chapter-list',
  templateUrl: './chapter-list.component.html',
  styleUrl: './chapter-list.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, RouterModule, SpinnerComponent, SelectionComponent, DateAgoPipe, LoopScrollComponent],

})
export class ChapterListComponent implements OnInit, AfterViewInit, OnChanges {

  @Input()
  comic?: Comic;
  @Input()
  allchapters: Chapter[] = [];
  @Input()
  isChapterLoading = false;
  preLoadChapters: Chapter[] = [];
  history: number[] = [];
  options: IOption[] = [];
  // Constants
  gridSize = 3;
  curOptioneValue = 0;
  distance = 100;

  @ViewChild('LoopScrollComponent')
  loopScrollComponent!: LoopScrollComponent;

  constructor(
    private cd: ChangeDetectorRef,
    private historyService: HistoryService,
    @Inject(PLATFORM_ID) private platformId: object,
  ) { }
  ngOnInit() {
    if (isPlatformBrowser(this.platformId)) {
      this.CalcGirdSize();
    }
  }
  ngAfterViewInit() {
  }
  SetUpHistory() {
    const comic = this.historyService.GetHistory(this.comic?.id!);
    if (comic) {
      this.history = comic.chapters!.map((chapter) => {
        return chapter.id;
      });
    }
  }

  onScrollChange(idx: number) {
    this.RefreshChapterOptions(idx);
  }
  ngOnChanges(change: SimpleChanges) {
    if (isPlatformBrowser(this.platformId)) {
      this.CalcGirdSize();
    }
    if (!this.allchapters || this.allchapters.length == 0) {
      this.cd.detectChanges();

    }
    this.calDistance();
    const _length = Math.floor(((this.comic?.numChapter || this.allchapters.length) - 1) / this.distance + 1);
    this.options = Array.from(
      { length: _length },
      (_, i) => {
        return {
          label: `${i * this.distance} - ${(i + 1) * this.distance}`,
          value: _length - i - 1,
        };
      },
    ).reverse();

    this.curOptioneValue = 0;
    this.preLoadChapters = this.allchapters;
    this.cd.detectChanges();

  }
  CalcGirdSize() {
    if (window.innerWidth < config.GetScreenSize('sm')) {
      //sm breakpoint
      this.gridSize = 2;
    } else if (window.innerWidth < config.GetScreenSize('xl')) {
      // xl break point
      this.gridSize = 3;
    } else {
      this.gridSize = 4;
    }

    return this.gridSize;
  }
  calDistance() {
    this.distance = 30;
    if (this.allchapters.length > 1000) {
      this.distance = 100;
    } else if (this.allchapters.length > 200) {
      this.distance = 50;
    }
  }
  @HostListener('window:resize', ['$event'])
  onWindowResize() {
    this.CalcGirdSize();
  }

  onSearchChapter(e: any) {
    const value: string = e.target.value?.toLowerCase();
    if (!value) {
      this.preLoadChapters = this.allchapters;
      return;
    }
    this.preLoadChapters = this.allchapters.filter((chapter) =>
      chapter.title?.toLowerCase().includes(value),
    );
    this.cd.detectChanges();
  }


  RefreshChapterOptions(curRow: number) {
    if (Math.abs(curRow * this.gridSize + this.distance / 2 - this.curOptioneValue * this.distance) >= 1) {
      this.curOptioneValue = Math.round(curRow * this.gridSize / this.distance);
      this.cd.detectChanges();

    }

  }

  selectChapterRange(id: number) {
    const value = (id * this.distance) / this.gridSize;
    this.loopScrollComponent.GoToItem(value);
  }
}
