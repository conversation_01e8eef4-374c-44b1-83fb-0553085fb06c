import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ComicHotComponent } from './comic-hot.component';
import { RouterModule } from '@angular/router';
import { TopListComponent } from '@components/common/top-list/top-list.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';



@NgModule({
  declarations: [ComicHotComponent],
  imports: [
    CommonModule,
    TopListComponent,
    PaginationComponent,
    BreadcrumbComponent,
    GridComicComponent,
    RouterModule.forChild([{
      path: '', component: ComicHotComponent,
    }])
  ]

})
export class ComicHotModule { }
