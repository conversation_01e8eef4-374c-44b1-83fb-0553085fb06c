import { Pipe, PipeTransform } from '@angular/core';
import { ServiceUtility } from '@services/utils.service';

@Pipe({
    name: 'fillDescription',
    standalone: true
})
export class ComicDescriptionlPipe implements PipeTransform {
  transform(value: string | null | undefined, id: number, title: string, url: string): string {

    return ServiceUtility.fillDescription(value
      , { url: url, id: id, title: title }, true
    );

  }

}
