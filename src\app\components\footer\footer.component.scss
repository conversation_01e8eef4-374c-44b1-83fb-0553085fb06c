// Modern Comic-themed Footer Styles
.footer-container {
  @apply relative bg-white dark:bg-neutral-800;
  @apply text-gray-900 dark:text-white overflow-hidden;
  @apply border-t border-orange-300 border-opacity-50;
}

// Background Elements

// Main Content
.footer-content {
  @apply relative z-10 max-w-7xl mx-auto px-4 py-8;
}

.footer-grid {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8;
}

// Brand Section
.footer-brand {
  @apply space-y-4;
}

.brand-logo {
  @apply flex items-center gap-4;
}

.logo-image {
  @apply h-16 w-auto object-contain;
}

.brand-text {
  @apply flex-1;
}

.brand-name {
  @apply text-xl font-bold text-gray-900 dark:text-white mb-1;
}

.brand-tagline {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.brand-description {
  @apply text-gray-700 dark:text-gray-300 text-sm mb-6;
}

// Contact Section
.contact-section {
  @apply space-y-4 mb-6;
}

.contact-email {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.footer-links {
  @apply flex gap-4;
}

.footer-link {
  @apply text-gray-600 dark:text-gray-400 hover:text-primary-100 dark:hover:text-primary-100;
  @apply text-sm hover:underline;
}

// Social Media
.social-section {
  @apply space-y-4;
}

.social-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.social-links {
  @apply flex gap-3;
}

.social-link {
  @apply flex items-center gap-2 px-4 py-2;
  @apply bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600;
  @apply text-gray-700 dark:text-gray-300;
  @apply rounded-lg text-sm;
  @apply hover:scale-105 hover:shadow-lg;

  &.facebook {
    @apply hover:bg-blue-100 dark:hover:bg-blue-600 hover:text-blue-600 dark:hover:text-white;
  }

  &.twitter {
    @apply hover:bg-blue-50 dark:hover:bg-blue-400 hover:text-blue-400 dark:hover:text-white;
  }

  &.discord {
    @apply hover:bg-indigo-100 dark:hover:bg-indigo-600 hover:text-indigo-600 dark:hover:text-white;
  }
}

.social-icon {
  @apply w-4 h-4;
}

// Footer Social Section
.footer-social {
  @apply space-y-6;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

// Facebook Section
.facebook-section {
  @apply mb-6;
}

// Popular Tags
.footer-tags {
  @apply space-y-4;
}

.tags-container {
  @apply flex flex-wrap gap-2;
}

.tag-item {
  @apply px-3 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-primary-100 dark:hover:bg-primary-100;
  @apply text-gray-700 dark:text-gray-300 hover:text-white dark:hover:text-white;
  @apply rounded-full text-xs;
  @apply hover:scale-105 border border-gray-300 dark:border-gray-600 hover:border-primary-100;
}

// Footer Bottom
.footer-bottom {
  @apply border-t border-gray-200 dark:border-gray-700 pt-8;
}

.footer-bottom-content {
  @apply flex flex-col lg:flex-row justify-between items-center gap-6;
}

.copyright-section {
  @apply text-center lg:text-left;
}

.copyright-text {
  @apply text-gray-900 dark:text-white font-medium mb-2;
}

.disclaimer-text {
  @apply text-gray-600 dark:text-gray-400 text-sm leading-relaxed;
}

.footer-stats {
  @apply flex gap-8;
}

.stat-item {
  @apply text-center;
}

.stat-number {
  @apply block text-2xl font-bold text-primary-100;
}

.stat-label {
  @apply text-gray-600 dark:text-gray-400 text-sm;
}

// Responsive Design
@media (max-width: 768px) {
  .footer-grid {
    @apply grid-cols-1 gap-6;
  }

  .brand-logo {
    @apply justify-center text-center;
  }

  .social-links {
    @apply justify-center;
  }

  .tags-container {
    @apply justify-center;
  }

  .footer-stats {
    @apply gap-4;
  }

  .stat-number {
    @apply text-xl;
  }

  .comic-bubble {
    @apply text-lg;
  }
}