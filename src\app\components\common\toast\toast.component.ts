// src/app/toast/toast.component.ts
import {
    animate,
    state,
    style,
    transition,
    trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, ViewEncapsulation } from '@angular/core';
import { Toast, ToastService, ToastType } from '@services/toast.service';


@Component({
    selector: 'app-toast',
    templateUrl: './toast.component.html',
    styleUrl: './toast.component.scss',
    animations: [
        trigger('toastAnimation', [
            state('void', style({
                opacity: 0,
                transform: 'translateX(100%) scale(0.8)',
            })),
            state('enter', style({
                opacity: 1,
                transform: 'translateX(0) scale(1)',
            })),
            state('leave', style({
                opacity: 0,
                transform: 'translateX(100%) scale(0.8)',
                height: 0,
                marginTop: 0,
                marginBottom: 0,
                paddingTop: 0,
                paddingBottom: 0,
            })),
            transition('void => enter', [
                animate('400ms cubic-bezier(0.25, 0.8, 0.25, 1)')
            ]),
            transition('enter => leave', [
                animate('300ms cubic-bezier(0.4, 0.0, 0.2, 1)')
            ]),
        ]),
    ],
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    encapsulation: ViewEncapsulation.None,
    imports: [CommonModule],
})
export class ToastComponent {
    cd = inject(ChangeDetectorRef);

    constructor(public toastService: ToastService) { }

    ngAfterViewInit() {
        this.toastService.toasts$.subscribe(() =>
            this.cd.detectChanges()
        );
    }

    onDismiss(toastId: number): void {
        this.toastService.dismiss(toastId);
    }

    onAction(action: { label: string; handler: () => void }): void {
        action.handler();
    }

    trackByToastId(_: number, toast: Toast): number {
        return toast.id;
    }

    getToastIcon(type: ToastType): string {
        switch (type) {
            case ToastType.Success:
                return 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z';
            case ToastType.Error:
                return 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z';
            case ToastType.Warning:
                return 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.34 16.5c-.77.833.192 2.5 1.732 2.5z';
            case ToastType.Info:
                return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
            default:
                return 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
        }
    }
}
