// ===== CHAPTER NAVIGATION COMPONENT =====
// Optimized styles using Tailwind @apply

// ===== END CHAPTER NAVIGATION =====
.end-chapter-navigation {
  @apply w-full py-8 bg-white dark:bg-neutral-800 
         border-t border-gray-200 dark:border-neutral-700;
}

.end-chapter-content {
  @apply flex items-center justify-between max-w-4xl mx-auto px-4;
}

.end-nav-button {
  @apply flex items-center gap-3 px-6 py-4 text-base font-medium 
         text-gray-600 dark:text-gray-300 
         hover:text-white 
         bg-gray-100 dark:bg-neutral-700 
         hover:bg-primary-100 dark:hover:bg-primary-100
         rounded-lg 
         border border-gray-200 dark:border-neutral-600 
         hover:border-primary-100
         disabled:opacity-50 disabled:cursor-not-allowed
         transition-all duration-200;

  &.end-nav-active {
    @apply text-white bg-primary-100 border-primary-100;
  }

  &.end-nav-prev {
    @apply flex-row;
  }

  &.end-nav-next {
    @apply flex-row-reverse;
  }
}

.end-nav-icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.end-nav-text {
  @apply font-semibold;
}

.end-chapter-info {
  @apply text-center space-y-2;
}

.end-chapter-title {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.end-chapter-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400 font-medium;
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .end-chapter-content {
    @apply flex-col gap-6 px-4;
  }
  
  .end-nav-button {
    @apply w-full justify-center px-4 py-3 text-sm;
    
    &.end-nav-prev,
    &.end-nav-next {
      @apply flex-row;
    }
  }
  
  .end-chapter-info {
    @apply order-first;
  }
  
  .end-chapter-title {
    @apply text-lg;
  }
}

@media (max-width: 480px) {
  .end-chapter-content {
    @apply px-2;
  }
  
  .end-nav-button {
    @apply px-3 py-2 text-xs;
  }
  
  .end-nav-icon {
    @apply w-5 h-5;
  }
  
  .end-chapter-title {
    @apply text-base;
  }
  
  .end-chapter-subtitle {
    @apply text-xs;
  }
}
