import { isPlatformBrowser } from '@angular/common';
import { Component, ElementRef, Inject, Input, OnInit, PLATFORM_ID, Renderer2, ViewChild } from '@angular/core';

@Component({
    selector: 'app-banner',
    templateUrl: './banner.component.html',
    styleUrl: './banner.component.scss',
    standalone: true
})
export class BannerComponent implements OnInit {
  @Input()
  infos: {
    src: string;
    className: string;
  }[] = [];
  @ViewChild('banner', { static: true }) adContainer!: ElementRef;
  constructor(private renderer: Renderer2, private el: ElementRef, @Inject(PLATFORM_ID) private platformId: object) { }

  ngOnInit() {
    // Tải script cho quảng cáo
    if (isPlatformBrowser(this.platformId)) {
      // for (const info of this.infos) {
      //   this.loadScript2(info.src, info.className);
      // }
      // this.addScript();
      // this.addScript();
    }
  }

  addScript() {
    const script = this.renderer.createElement('script');
    script.type = 'text/javascript';
    script.innerHTML = `
      atOptions = {
        'key' : '6c8fd725ff75c41831efd1cdc6285a12',
        'format' : 'iframe',
        'height' : 90,
        'width' : 728,
        'params' : {}
      };
    `;
    this.renderer.appendChild(this.adContainer.nativeElement, script);

    const script2 = this.renderer.createElement('script');
    script2.type = 'text/javascript';
    script2.src = "//www.highperformanceformat.com/6c8fd725ff75c41831efd1cdc6285a12/invoke.js";
    this.renderer.appendChild(this.adContainer.nativeElement, script2);
  }

  private loadScript2(src: string, className: string) {
    const script = this.renderer.createElement('script');
    script.type = 'text/javascript';
    script.src = src;
    script.async = true;
    script.dataset.cfasync = 'false';
    script.className = className;
    this.renderer.appendChild(this.adContainer.nativeElement, script);
  }

  closeFooterBanner() {
    const bannerElement = this.el.nativeElement.querySelector('.fixed');
    if (bannerElement) {
      (bannerElement as HTMLElement).style.display = 'none';
    }
  }
}
