<!-- Enhanced User Profile Container -->
<div class="user-profile-container">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <div class="loading-content">
      <div class="loading-spinner"></div>
      <p class="loading-text"><PERSON><PERSON> tải thông tin người dùng...</p>
    </div>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="profile-layout">
    <!-- Enhanced Navigation Sidebar -->
    <aside class="profile-sidebar">
      <div class="sidebar-content">
        <!-- User Quick Info -->
        <div class="user-quick-info" *ngIf="user">
          <div class="user-avatar-container">
            <img 
              [src]="user.avatar || 'default_avatar.jpg'" 
              [alt]="user.firstName + ' ' + user.lastName"
              class="user-avatar"
              onerror="this.src='default_avatar.jpg'"
            />
            <div class="user-status-indicator"></div>
          </div>
          <div class="user-basic-info">
            <h3 class="user-name">{{ user.firstName }} {{ user.lastName }}</h3>
            <p class="user-join-date">Tham gia {{ user.createAt | date:'MM/yyyy' }}</p>
          </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="profile-navigation">
          <h4 class="nav-section-title">Tài khoản</h4>
          <ul class="nav-list">
            <li 
              *ngFor="let item of navigationItems; trackBy: trackByPath" 
              class="nav-item"
            >
              <a 
                [routerLink]="item.path" 
                routerLinkActive="nav-link-active"
                class="nav-link"
                [title]="item.description"
              >
                <div class="nav-link-content">
                  <div class="nav-icon-wrapper">
                    <svg class="nav-icon" [attr.data-icon]="item.icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <!-- User Icon -->
                      <g *ngIf="item.icon === 'user'">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                        <circle cx="12" cy="7" r="4"/>
                      </g>
                      <!-- Heart Icon -->
                      <g *ngIf="item.icon === 'heart'">
                        <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                      </g>
                      <!-- Clock Icon -->
                      <g *ngIf="item.icon === 'clock'">
                        <circle cx="12" cy="12" r="10"/>
                        <polyline points="12,6 12,12 16,14"/>
                      </g>
                      <!-- Chart Icon -->
                      <g *ngIf="item.icon === 'chart'">
                        <line x1="18" y1="20" x2="18" y2="10"/>
                        <line x1="12" y1="20" x2="12" y2="4"/>
                        <line x1="6" y1="20" x2="6" y2="14"/>
                      </g>
                      <!-- Star Icon -->
                      <g *ngIf="item.icon === 'star'">
                        <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
                      </g>                  
                      <!-- Trophy Icon -->
                      <g *ngIf="item.icon === 'trophy'">
                        <path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"/>
                        <path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"/>
                        <path d="M4 22h16"/>
                        <path d="M10 14.66V17c0 .55.47.98.97 1.21C12.04 18.75 14 20 14 20s1.96-1.25 3.03-1.79c.5-.23.97-.66.97-1.21v-2.34"/>
                        <path d="M18 2H6v7a6 6 0 0 0 12 0V2z"/>
                      </g>
                      <!-- Target Icon -->
                      <g *ngIf="item.icon === 'target'">
                        <circle cx="12" cy="12" r="10"/>
                        <circle cx="12" cy="12" r="6"/>
                        <circle cx="12" cy="12" r="2"/>
                      </g>
                      <!-- package Icon -->
                      <g *ngIf="item.icon === 'package'">
                        <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8z"/>
                      </g>

                    </svg>
                  </div>

                  <!-- Mobile: Small label under icon -->
                  <span class="nav-mobile-label md:hidden">{{ item.label }}</span>

                  <!-- Desktop: Full text content -->
                  <div class="nav-text-content hidden md:block">
                    <span class="nav-label">{{ item.label }}</span>
                    <span class="nav-description">{{ item.description }}</span>
                  </div>

                  <div class="nav-badge" *ngIf="item.badge && item.badge > 0">
                    {{ item.badge > 99 ? '99+' : item.badge }}
                  </div>
                </div>
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </aside>

    <!-- Main Content Area -->
    <main class="profile-main-content">
      <router-outlet></router-outlet>
    </main>
  </div>
</div>
