
using System.Threading.Channels;
using ComicAPI.Services;

namespace ComicAPI.BackroundTask;
public interface IBackgroundTaskQueue
{
    void QueueBackgroundWorkItem(Func<CancellationToken, Task> workItem);
    Task<Func<CancellationToken, Task>> DequeueAsync(CancellationToken cancellationToken);

    void OnStopping();
}


public class BackgroundTaskQueue : IBackgroundTaskQueue
{

    private readonly Channel<Func<CancellationToken, Task>> _queue =
        Channel.CreateUnbounded<Func<CancellationToken, Task>>();

    public void QueueBackgroundWorkItem(Func<CancellationToken, Task> workItem)
    {
        if (workItem == null) throw new ArgumentNullException(nameof(workItem));
        _queue.Writer.TryWrite(workItem);
    }

    public async Task<Func<CancellationToken, Task>> DequeueAsync(CancellationToken cancellationToken)
    {
        return await _queue.Reader.ReadAsync(cancellationToken);
    }
    public void OnStopping()
    {
        _queue.Writer.Complete();
    }
}



public class QueueBackgroundService : BackgroundService
{
    private readonly IHostApplicationLifetime _appLifetime;
    private readonly IBackgroundTaskQueue _taskQueue;
    private readonly ILogger<QueueBackgroundService> _logger;

    public QueueBackgroundService(IServiceProvider services, IBackgroundTaskQueue taskQueue, ILogger<QueueBackgroundService> logger)
    {
        _taskQueue = taskQueue;
        _logger = logger;
        _appLifetime = services.GetRequiredService<IHostApplicationLifetime>();

    }

    public override Task StartAsync(CancellationToken cancellationToken)
    {
        base.StartAsync(cancellationToken);
        return Task.CompletedTask;
    }



    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Unified Background Service is starting.");
        await ProcessQueueAsync(stoppingToken);
        _logger.LogInformation("Unified Background Service is stopping.");
    }


    private async Task ProcessQueueAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                var workItem = await _taskQueue.DequeueAsync(stoppingToken);
                await workItem(stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred executing queued task.");
            }
        }
    }
}