.confirm-popup-overlay {
    @apply fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50;
  }

  .confirm-popup-content {
    @apply bg-white dark:bg-neutral-800 rounded-lg p-6 shadow-lg w-full max-w-md relative mx-5 lg:mx-0;
  }

  .confirm-popup-title {
    @apply text-lg font-bold text-neutral-800 dark:text-white flex items-center justify-between gap-1;
  }

  .confirm-popup-title-icon {
    @apply flex items-center gap-1;
  }

  .confirm-popup-icon {
    @apply h-6 w-6 text-yellow-500;
  }

  .confirm-popup-close-button {
    @apply text-neutral-600 dark:text-white hover:text-neutral-800 dark:hover:text-neutral-300 absolute right-3 top-3;
  }

  .confirm-popup-close-icon {
    @apply w-6 h-6 dark:text-neutral-500;
  }

  .confirm-popup-message {
    @apply mt-4 text-neutral-600 dark:text-white text-base;
  }

  .confirm-popup-actions {
    @apply mt-6 flex justify-end space-x-3;
  }

  .confirm-popup-cancel-button {
    @apply px-4 py-2 bg-neutral-200 hover:bg-neutral-300 text-neutral-700 dark:text-white dark:bg-neutral-600 rounded-md;
  }

  .confirm-popup-confirm-button {
    @apply px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md font-semibold;
  }