
using Microsoft.AspNetCore.Mvc;
using ComicAPI.Services;
using ComicAPI.DTOs;
using ComicAPI.Reposibility;
namespace ComicAPI.Controllers;

[ApiController]
[Route("api/admin/")]
public class AdminController : ControllerBase
{
    IComicReposibility comicReposibility;
    AdminService _adminService;

    UrlService urlService;
    //Contructor
    public AdminController(IComicReposibility comicReposibility, AdminService adminService, UrlService urlService)
    {
        this.comicReposibility = comicReposibility;
        this.urlService = urlService;
        this._adminService = adminService;


    }
    [HttpPost]
    [Route("notify-new-chapter")]
    public async Task<ActionResult> NotifyNewChapter([FromBody] NotifyComicChangeDTO body)
    {
        int comicid = body.comicid;
        Console.WriteLine($"NotifyNewChapter: {comicid}");
        var comic = await comicReposibility.GetComic(comicid.ToString());
        if (comic == null) return BadRequest("Comic not found");
        await _adminService.NotifyNewChapterToFollowers(comic.ID, comic.Title, $"/truyen-tranh/{comic.Url}-{comic.ID}", comic.CoverImage!);
        return Ok();
    }



}

