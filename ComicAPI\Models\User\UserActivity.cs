using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicAPI.Models
{
    /// <summary>
    /// Tracks detailed user activity for analytics and session management
    /// </summary>
    public class UserActivity
    {
        [Key, Column("id")]
        public long ID { get; set; }

        [Required, Column("userid")]
        public int UserId { get; set; }

        [Column("activitytype")]
        [MaxLength(50)]
        public string ActivityType { get; set; } = "page_view";

        [Column("endpoint")]
        [MaxLength(255)]
        public string? Endpoint { get; set; }

        [Column("ipaddress")]
        [MaxLength(50)]
        public string? IpAddress { get; set; }

        [Column("useragent")]
        [MaxLength(500)]
        public string? UserAgent { get; set; }

        [Column("sessionid")]
        [MaxLength(100)]
        public string? SessionId { get; set; }

        [Column("timestamp")]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        [Column("duration")]
        public int? Duration { get; set; } // Duration in seconds for session activities

        [Column("metadata")]
        public string? Metadata { get; set; } // JSON string for additional data

        // Navigation property
        [ForeignKey("UserId")]
        public virtual User? User { get; set; }
    }

    /// <summary>
    /// Enum for different types of user activities
    /// </summary>
    public enum UserActivityType
    {
        Login,
        Logout,
        PageView,
        ComicView,
        ChapterRead,
        Search,
        Comment,
        Vote,
        Follow,
        ApiCall
    }
}
