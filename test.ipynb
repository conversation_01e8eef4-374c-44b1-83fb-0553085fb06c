{"cells": [{"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import httpx\n", "import asyncio\n", "async with httpx.AsyncClient() as client:\n", "    a = await asyncio.gather(*[client.get('http://localhost:5080/api/comic/similar/234234') for _ in range(2)])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}