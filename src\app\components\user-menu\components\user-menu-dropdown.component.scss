
// Dropdown Container
.user-dropdown-container {
    @apply absolute right-0 top-full mt-3 w-72 md:w-80 z-50;

    &.hidden {
        @apply opacity-0 invisible transform scale-95 translate-y-2;
    }

    &._visible {
        @apply opacity-100 visible transform scale-100 translate-y-0;
    }
}

.user-dropdown {
    @apply bg-white dark:bg-neutral-800 rounded-2xl shadow-2xl border border-neutral-200 dark:border-neutral-700 overflow-hidden;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);

    .dark & {
        background: rgba(38, 38, 38, 0.95);
    }
}

// User Profile Header
.user-profile-header {
    @apply p-6 bg-gradient-to-br from-sky-50 to-indigo-100 dark:from-neutral-800 dark:to-neutral-900 border-b border-neutral-200 dark:border-neutral-700;
}

.user-avatar-large {
    @apply relative mb-4;

    img {
        @apply w-16 h-16 rounded-full object-cover border-4 border-white dark:border-neutral-700 shadow-lg;
    }
}

.user-status-badge {
    @apply absolute -bottom-1 -right-1 flex items-center gap-1 bg-lime-500 text-white text-xs px-2 py-1 rounded-full;
}

.status-dot {
    @apply w-2 h-2 bg-white rounded-full animate-pulse;
}

.status-text {
    @apply font-medium;
}

.user-profile-info {
    @apply space-y-2;
}

.user-full-name {
    @apply text-lg font-bold text-neutral-900 dark:text-white;
}

.user-email {
    @apply text-sm text-neutral-600 dark:text-neutral-400;
}

.user-stats {
    @apply mt-3;
}

.stat-item {
    @apply flex items-center gap-2 text-sm text-neutral-700 dark:text-neutral-300;
}

.stat-icon {
    @apply w-4 h-4;
}

// Quick Actions
.quick-actions {
    @apply flex items-center justify-center gap-2 p-4 border-b border-neutral-200 dark:border-neutral-700;
}

.quick-action-btn {
    @apply relative p-3 bg-neutral-100 dark:bg-neutral-700 hover:bg-sky-100 dark:hover:bg-sky-900/30 rounded border-none cursor-pointer;

    svg {
        @apply w-5 h-5 text-neutral-600 dark:text-neutral-400;
    }

    &:hover svg {
        @apply text-sky-600 dark:text-sky-400;
    }


}

// Navigation Menu
.user-navigation {
    @apply py-2;
}

.nav-menu-list {
    @apply space-y-1;
}

.nav-menu-item {
    @apply mx-2;
}

.nav-link {
    @apply flex items-center gap-3 px-4 py-3 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700 rounded;

    &.nav-button {
        @apply border-none bg-transparent cursor-pointer w-full text-left;
    }

    &:hover {
        @apply text-neutral-900 dark:text-white;

        .nav-arrow {
            @apply text-sky-500 transform translate-x-1;
        }
    }
}

.nav-icon {
    @apply w-5 h-5 text-neutral-500 dark:text-neutral-400;
}

.nav-text {
    @apply flex-1 font-medium;
}

.nav-arrow {
    @apply w-4 h-4 text-neutral-400 dark:text-neutral-500;
}

// Menu Divider
.menu-divider {
    @apply h-px bg-neutral-200 dark:bg-neutral-700 mx-4 my-2;
}

// Logout Section
.logout-section {
    @apply p-2;
}

.logout-button {
    @apply flex items-center gap-3 w-full px-4 py-3 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded border-none bg-transparent cursor-pointer;

}

.logout-icon {
    @apply w-5 h-5;
}

// Backdrop
// .dropdown-backdrop {
//   @apply fixed inset-0 z-40 bg-black/20 backdrop-blur-sm;
// }


// Guest User Styles
.guest-avatar-container {
    @apply relative flex items-center justify-center;
}

.guest-avatar-icon {
    @apply size-6 text-neutral-600 dark:text-neutral-400;
}

.guest-info-preview {
    @apply flex items-center gap-2;
}

.guest-name-preview {
    @apply font-medium text-neutral-700 dark:text-neutral-300 text-sm;
}

// Guest Dropdown Styles
.guest-dropdown {
    @apply w-72 md:w-80;
}

.guest-profile-header {
    @apply p-6 bg-gradient-to-br from-neutral-50 to-sky-50 dark:from-neutral-800 dark:to-sky-900/20 border-b border-neutral-200 dark:border-neutral-700;
}

.guest-avatar-large {
    @apply flex items-center justify-center mb-4;
}

.guest-avatar-icon-large {
    @apply  md:size-20 size-16 text-neutral-400 dark:text-neutral-500 p-4 bg-neutral-100 dark:bg-neutral-700 rounded-full;
}

.guest-profile-info {
    @apply text-center space-y-3;
}

.guest-welcome-title {
    @apply text-xl font-bold text-neutral-900 dark:text-white;
}

.guest-welcome-subtitle {
    @apply text-neutral-600 dark:text-neutral-400;
}

.guest-features {
    @apply space-y-2 mt-4;
}

.feature-item {
    @apply flex items-center gap-2 text-sm text-neutral-700 dark:text-neutral-300;
}

.feature-icon {
    @apply w-4 h-4 text-sky-500 dark:text-sky-400;
}

// Authentication Section
.auth-section {
    @apply p-4 space-y-3;
}

.auth-button {
    @apply flex items-center justify-center gap-3 w-full px-4 py-3 rounded font-medium border-none cursor-pointer;

    &.login-button {
        @apply bg-primary-100 text-white hover:bg-primary-200 shadow-lg hover:shadow-xl;
    }

    &.register-button {
        @apply bg-neutral-100 dark:bg-neutral-700 text-neutral-900 dark:text-white hover:bg-neutral-200 dark:hover:bg-neutral-600;
    }

    &:focus {
        @apply outline-none ring-2 ring-sky-500/50;
    }
}

.auth-icon {
    @apply w-5 h-5;
}

// Guest Quick Links
.guest-quick-links {
    @apply p-4 border-t border-neutral-200 dark:border-neutral-700;
}

.quick-links-title {
    @apply text-sm font-semibold text-neutral-900 dark:text-white mb-3;
}

.quick-links-grid {
    @apply grid grid-cols-3 gap-2;
}

.quick-link-item {
    @apply flex flex-col items-center gap-2 p-3 bg-neutral-50 dark:bg-neutral-700 hover:bg-sky-50 dark:hover:bg-sky-900/20 rounded-lg;

    &:hover {
        @apply text-sky-600 dark:text-sky-400;

        .quick-link-icon {
            @apply text-sky-600 dark:text-sky-400;
        }
    }
}

.quick-link-icon {
    @apply w-6 h-6 text-neutral-500 dark:text-neutral-400;
}

.quick-link-item span {
    @apply text-xs font-medium text-neutral-700 dark:text-neutral-300;
}


// Fix for visible class
.user-dropdown-container.visible1 {
    @apply opacity-100 visible translate-y-0;
}