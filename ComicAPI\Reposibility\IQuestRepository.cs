using ComicAPI.DTOs;

namespace ComicAPI.Reposibility
{
    public interface IQuestRepository
    {
        // Daily Quests
        Task<List<DailyQuestDTO>> GetDailyQuestsAsync(int userId);
        Task<List<WeeklyQuestDTO>> GetWeeklyQuestsAsync(int userId);
        
        // Quest Management
        Task<bool> CreateDailyQuestsForUserAsync(int userId);
        Task<bool> CreateWeeklyQuestsForUserAsync(int userId);
        Task<ClaimRewardResponse> ClaimQuestRewardAsync(int userId, int questId);
        // User Quest Stats
        // Progress Tracking
        Task<bool> TrackReadChapterAsync(int userId, int chapterId);
        Task<bool> TrackCommentAsync(int userId, int comicId);
        Task<bool> TrackFavoriteAsync(int userId, int comicId);
        Task<bool> TrackRatingAsync(int userId, int comicId);
        Task<bool> TrackLoginAsync(int userId);
        
        // Utility
        Task<bool> ExpireOldQuestsAsync();
        Task<bool> ResetDailyQuestsAsync();
        Task<bool> ResetWeeklyQuestsAsync();


    }
}
