import { NgModule } from '@angular/core';
import { AuthRoutingModule } from './auth.routing';
import { LoginFormComponent } from './login-form/login-form.component';
import { ForgotPasswordComponent } from './forgot-password/forgot-password.component';
import { RegisterFormComponent } from './register-form/register-form.component';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import {
  SocialLoginModule,
  SocialAuthServiceConfig,
  GoogleLoginProvider,
} from '@abacritt/angularx-social-login';
import { GoogleSigninButtonModule } from '@abacritt/angularx-social-login';
import { ConfirmEmailComponent } from './confirm-email/confirm-email.component';
import { EyeIconComponent } from '@components/common/eye-icon/eye-icon.component';
@NgModule({
  declarations: [
    RegisterFormComponent,
    LoginFormComponent,
    ConfirmEmailComponent,
    ForgotPasswordComponent,
  ],
  imports: [
    AuthRoutingModule,
    CommonModule,
    ReactiveFormsModule,
    SocialLoginModule,
    GoogleSigninButtonModule,
    EyeIconComponent
  ],
  providers: [
    {
      provide: 'SocialAuthServiceConfig',
      useValue: {
        autoLogin: false,
        providers: [
          {
            id: GoogleLoginProvider.PROVIDER_ID,
            provider: new GoogleLoginProvider(
              '*************-c80gc259j5pn04oet0rptraa5c2l7c8d.apps.googleusercontent.com',
              {
                scopes:
                  'https://www.googleapis.com/auth/cloud-platform.read-only',
              },
            ),
          },
        ],
        onError: (err) => {
          console.error(err);
        },
      } as SocialAuthServiceConfig,
    },
  ],
})
export class AuthModule {}
