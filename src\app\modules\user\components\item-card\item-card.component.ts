import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { 
  ItemDisplayInfo, 
  ItemActionType, 
  ITEM_RARITY_INFO, 
  ITEM_CATEGORY_INFO,
  ItemRarity,
  ItemCategory,
  EquipmentSlotType
} from '../../interfaces/item.interface';

export interface ItemAction {
  type: ItemActionType;
  label: string;
  icon: string;
  enabled: boolean;
  primary?: boolean;
}

@Component({
  selector: 'app-item-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './item-card.component.html',
  styleUrl: './item-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ItemCardComponent extends OptimizedBaseComponent {
  @Input() item!: ItemDisplayInfo;
  @Input() showActions = true;
  @Input() showQuantity = true;
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() selectable = false;
  @Input() selected = false;

  @Output() actionClicked = new EventEmitter<{ action: ItemActionType; item: ItemDisplayInfo }>();
  @Output() itemClicked = new EventEmitter<ItemDisplayInfo>();
  @Output() selectionChanged = new EventEmitter<{ item: ItemDisplayInfo; selected: boolean }>();

  // Signals for reactive state
  private readonly hoveredSignal = signal<boolean>(false);
  private readonly showActionsMenuSignal = signal<boolean>(false);

  // Computed properties
  readonly isHovered = computed(() => this.hoveredSignal());
  readonly showActionsMenu = computed(() => this.showActionsMenuSignal());

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object
  ) {
    super(cdr, platformId);
  }

  // Computed getters
  get rarityInfo() {
    return ITEM_RARITY_INFO[this.item.template.rarity];
  }

  get categoryInfo() {
    return ITEM_CATEGORY_INFO[this.item.template.category];
  }

  get displayName(): string {
    return this.item.template.name;
  }

  get displayDescription(): string {
    return this.item.template.description || 'Không có mô tả';
  }

  get displayQuantity(): string {
    if (!this.showQuantity || !this.item.quantity) return '';
    return this.item.quantity > 999 ? '999+' : this.item.quantity.toString();
  }

  get isExpired(): boolean {
    if (!this.item.expiresAt) return false;
    return new Date(this.item.expiresAt) < new Date();
  }

  get isExpiringSoon(): boolean {
    if (!this.item.expiresAt) return false;
    const expiryDate = new Date(this.item.expiresAt);
    const now = new Date();
    const timeDiff = expiryDate.getTime() - now.getTime();
    const daysDiff = timeDiff / (1000 * 3600 * 24);
    return daysDiff <= 7 && daysDiff > 0;
  }

  get availableActions(): ItemAction[] {
    const actions: ItemAction[] = [];

    // Use action
    if (this.item.canUse && this.item.template.category === ItemCategory.CONSUMABLE) {
      actions.push({
        type: ItemActionType.USE,
        label: 'Sử dụng',
        icon: 'use',
        enabled: !this.isExpired,
        primary: true
      });
    }

    // Equip/Unequip actions
    if (this.item.canEquip && this.isEquippableCategory(this.item.template.category)) {
      if (this.item.isEquipped) {
        actions.push({
          type: ItemActionType.UNEQUIP,
          label: 'Tháo',
          icon: 'unequip',
          enabled: true,
          primary: true
        });
      } else {
        actions.push({
          type: ItemActionType.EQUIP,
          label: 'Trang bị',
          icon: 'equip',
          enabled: !this.isExpired,
          primary: true
        });
      }
    }

    // View details action
    actions.push({
      type: ItemActionType.VIEW_DETAILS,
      label: 'Chi tiết',
      icon: 'info',
      enabled: true
    });

    return actions;
  }

  get cardClasses(): string {
    const classes = [
      'item-card',
      `item-card--${this.size}`,
      `item-card--${this.item.template.rarity}`,
      `item-card--${this.item.template.category}`
    ];

    if (this.selected) classes.push('item-card--selected');
    if (this.isHovered()) classes.push('item-card--hovered');
    if (this.item.isEquipped) classes.push('item-card--equipped');
    if (this.isExpired) classes.push('item-card--expired');
    if (this.isExpiringSoon) classes.push('item-card--expiring');

    return classes.join(' ');
  }

  // Event handlers
  onCardClick(): void {
    if (this.selectable) {
      this.selected = !this.selected;
      this.selectionChanged.emit({ item: this.item, selected: this.selected });
    } else {
      this.itemClicked.emit(this.item);
    }
  }

  onActionClick(action: ItemActionType, event: Event): void {
    event.stopPropagation();
    this.actionClicked.emit({ action, item: this.item });
    this.showActionsMenuSignal.set(false);
  }

  onMouseEnter(): void {
    this.hoveredSignal.set(true);
  }

  onMouseLeave(): void {
    this.hoveredSignal.set(false);
    this.showActionsMenuSignal.set(false);
  }

  onActionsMenuToggle(event: Event): void {
    event.stopPropagation();
    this.showActionsMenuSignal.set(!this.showActionsMenu());
  }

  // Utility methods
  private isEquippableCategory(category: ItemCategory): boolean {
    return [
      ItemCategory.AVATAR_FRAME,
      ItemCategory.TITLE,
      ItemCategory.BADGE,
      ItemCategory.EQUIPMENT
    ].includes(category);
  }

  getSlotTypeFromCategory(category: ItemCategory): EquipmentSlotType | null {
    switch (category) {
      case ItemCategory.AVATAR_FRAME:
        return EquipmentSlotType.AVATAR_FRAME;
      case ItemCategory.TITLE:
        return EquipmentSlotType.TITLE;
      case ItemCategory.BADGE:
        return EquipmentSlotType.BADGE;
      default:
        return null;
    }
  }

  // TrackBy functions
  trackByActionType = (index: number, action: ItemAction): ItemActionType => {
    return action.type;
  };
}
