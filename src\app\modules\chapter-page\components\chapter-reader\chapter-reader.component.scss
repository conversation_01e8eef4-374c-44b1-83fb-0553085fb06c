// ===== CHAPTER READER COMPONENT =====
// Optimized styles using Tailwind @apply

// ===== READING CONTAINER =====
.reading-container {
  @apply relative w-full h-full;
}

.reading-content {
  @apply flex-grow min-h-screen mx-0 lg:mx-12 xl:mx-20;
}

// ===== SCROLL NAVIGATION =====
.scroll-navigation {
  @apply absolute flex justify-between w-full z-20 h-1/4 top-1/3 px-4;

  &.scroll-navigation-hidden {
    @apply hidden;
  }
}

.scroll-btn {
  @apply flex items-center gap-2 px-4 py-3
         bg-black/20 hover:bg-black/40 text-white
         rounded-lg backdrop-blur-sm
         border border-white/20
         transition-all duration-200;

  &.scroll-btn-prev {
    @apply bg-gradient-to-r from-black/30 to-transparent;
  }

  &.scroll-btn-next {
    @apply bg-gradient-to-l from-black/30 to-transparent;
  }
}

.scroll-btn-icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.scroll-btn-text {
  @apply font-medium hidden sm:inline;
}

// ===== LOADING STATE =====
.loading-container {
  @apply flex flex-col items-center justify-center min-h-screen space-y-8 p-8;
}

.loading-content {
  @apply flex flex-col items-center space-y-4;
}

.loading-spinner {
  @apply relative;
}

.loading-icon {
  @apply w-16 h-16 text-primary-100;
}

.loading-circle-bg {
  @apply opacity-25;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
}

.loading-circle-progress {
  @apply opacity-75;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  animation: loading-progress 2s ease-in-out infinite;
  stroke-dasharray: 31.416;
  stroke-dashoffset: 31.416;
}

@keyframes loading-progress {
  0% {
    stroke-dashoffset: 31.416;
  }
  50% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -31.416;
  }
}

.loading-text {
  @apply text-center space-y-2;
}

.loading-title {
  @apply text-xl font-bold text-gray-900 dark:text-white;
}

.loading-subtitle {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.loading-skeleton {
  @apply space-y-4 w-full max-w-2xl;
}

.skeleton-page {
  @apply h-96 bg-gray-200 dark:bg-neutral-700 rounded-lg animate-pulse;
}

// ===== CHAPTER IMAGES =====
.chapter-page-container {
  @apply block relative object-contain ease-in-out mx-auto;
}

.chapter-page-image {
  @apply object-cover w-full h-full min-h-48
         transition-all duration-200;

  &.chapter-page-horizontal {
    @apply min-w-80 h-full;
  }

  &.night-mode {
    @apply brightness-90;
    transition: all 1.5s ease-in-out;
  }

  // Loading state
  &.loading-state {
    @apply min-h-48 bg-gray-200 dark:bg-neutral-700 animate-pulse;
  }

  // Error state
  &.error-state {
    @apply hidden;
  }
}

// ===== RESPONSIVE DESIGN =====
@media (max-width: 768px) {
  .reading-content {
    @apply mx-2;
  }
  
  .scroll-navigation {
    @apply px-2;
  }
  
  .scroll-btn {
    @apply px-2 py-2;
  }
  
  .scroll-btn-text {
    @apply hidden;
  }
}

@media (max-width: 480px) {
  .loading-container {
    @apply p-4;
  }
  
  .loading-icon {
    @apply w-12 h-12;
  }
  
  .loading-title {
    @apply text-lg;
  }
}
