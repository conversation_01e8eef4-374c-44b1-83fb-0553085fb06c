// Remove unused import
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';

import { Chapter, Comic, IServiceResponse } from '@schema';
import { AccountService } from '@services/account.service';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { ToastService, ToastType } from '@services/toast.service';
import { UrlService } from '@services/url.service';
import { ServiceUtility } from '@services/utils.service';

@Component({
    selector: 'app-comic-detail',
    templateUrl: './comic-detail.component.html',
    styleUrl: './comic-detail.component.scss',
    standalone: false,
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ComicDetailComponent extends OptimizedBaseComponent implements OnInit {
  // Component state
  comic!: Comic;
  isFollowed = false;
  allchapters: Chapter[] = [];
  lastChapter?: Chapter;
  similarComics: Comic[] = [];
  isOpen = false;
  comicHistory?: Comic;
  latestHistoryChapter?: Chapter;
  isChapterLoading = false;

  // Constants
  private followtime = 0;

  // Performance optimizations
  private debouncedToggleDescription!: Function;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private comicService: ComicService,
    private accountService: AccountService,
    private toastService: ToastService,
    private seoService: SeoService,
    private historyService: HistoryService,
    private popupService: PopupService,
    private urlService: UrlService,
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
  ) {
    super(cdr, platformId);
    this.setupDebouncedMethods();
  }

  // Computed properties for better performance
  stars: number[] = [100, 100, 100, 100, 100];


  get hasChapters(): boolean {
    return this.allchapters.length > 0;
  }

  get hasSimilarComics(): boolean {
    return this.similarComics.length > 0;
  }

  get isComicFollowed(): boolean {
    return this.comic?.isFollow || false;
  }

  get hasComicHistory(): boolean {
    return !!this.comicHistory;
  }

  get canShowReadFromBeginning(): boolean {
    return !this.comicHistory && this.hasChapters;
  }

  get canShowContinueReading(): boolean {
    return this.hasComicHistory && !!this.latestHistoryChapter;
  }

  get followButtonClass(): string {
    return this.isComicFollowed ? 'btn-unfollow' : 'btn-follow';
  }

  get statusIndicatorClass(): string {
    return this.comic?.status === 0 ? 'status-ongoing' : 'status-completed';
  }

  get descriptionClass(): string {
    return this.isOpen ? 'description-expanded' : 'description-collapsed';
  }

  // TrackBy functions for ngFor optimization
  trackByStarIndex = (index: number): number => index;

  trackByGenreId = (_index: number, genre: any): number => genre.id;

  private setupDebouncedMethods(): void {
    this.debouncedToggleDescription = this.debounce(() => {
      this.performToggleDescription();
    }, 150);
  }
  private performToggleDescription(): void {
    this.isOpen = !this.isOpen;
    this.safeMarkForCheck();
  }

  ngOnInit(): void {
    this.addSubscription(
      this.route.data.subscribe(({ comicRes }) => {
        comicRes = comicRes as IServiceResponse<Comic>;

        if (comicRes === null || comicRes.data === null) {
          this.router.navigate(['/']);
          return;
        }
        this.comic = comicRes.data;
        this.isFollowed = this.comic?.isFollow || false;
        this.SetUpSEO(this.comic!);
        const chapters = this.comic?.chapters ?? [];
        this.lastChapter = chapters[chapters.length - 1];
        this.allchapters = [this.lastChapter];
        this.isChapterLoading = true;

        this.stars = this.stars.map((star, index) => {
          return this.getStarWidth(index+1);
        });

        this.runInBrowser(() => {
          this.getChapters();
          this.getSimilarComics();
          this.setupComicHistory();
        });

        this.safeMarkForCheck();
      })
    );
  }

  private setupComicHistory(): void {
    this.comicHistory = this.historyService.GetHistory(this.comic!.id);
    if (this.comicHistory?.chapters?.length) {
      this.latestHistoryChapter = this.comicHistory.chapters.reduce(
        (maxSlugChapter, currentChapter) => {
          return Number(currentChapter.slug) > Number(maxSlugChapter.slug)
            ? currentChapter
            : maxSlugChapter;
        },
        this.comicHistory.chapters[0],
      );
    }
  }

  getChapters(): void {
    this.isChapterLoading = true;
    this.addSubscription(
      this.comicService.getChapters(this.comic!.id).subscribe((res: any) => {
        this.allchapters = res.data;
        this.isChapterLoading = false;
        this.safeMarkForCheck();
      })
    );
  }

  getSimilarComics(): void {
    this.addSubscription(
      this.comicService.getSimilarComic(this.comic!.id).subscribe((res: any) => {
        this.similarComics = res.data;
        this.safeMarkForCheck();
      })
    );
  }

  Follow(isFollow: boolean): void {
    const now = Date.now();
    if (this.accountService.GetLocalUser() === null) {
      this.router.navigate(['/auth/login']);
      return;
    }
    if (this.followtime + 5000 > now) {
      this.toastService.show(ToastType.Info, `Thao tác quá nhanh!`);
      return;
    }
    this.followtime = now;
    this.accountService
      .Follow(this.comic!.id, isFollow)
      .subscribe((res: IServiceResponse<number>) => {
        if (res.status === 1) {
          this.comic!.isFollow = !this.comic!.isFollow;
          this.toastService.show(
            ToastType.Success,
            this.comic!.isFollow ? 'Đã theo dõi' : 'Đã hủy theo dõi',
          );
        } else {
          this.toastService.show(ToastType.Error, res.message!);
        }
        this.cd.detectChanges();

      });
  }

  toggleDescription(): void {
    this.debouncedToggleDescription();
  }

  getHistoryChapter() {
    if (!this.comicHistory || !this.latestHistoryChapter) {
      return;
    }
    return this.latestHistoryChapter?.title?.match(/[\.\d]+/iu);
  }

  SetUpSEO(comic: Comic) {
    const title = `${comic.title} [Tới Chương ${comic.chapters![0].slug}] - MeTruyenMoi`;
    const description = `${ServiceUtility.fillSeoDescription(comic.description, { title: comic.title })}`;
    const url = `${this.urlService.BASE_URL}/truyen-tranh/${comic["url"]}-${comic["id"]}`;
    this.seoService.setTitle(title);
    this.seoService.addTags([
      { name: 'description', content: description },
      {
        name: 'keywords',
        content: `${comic.title}, truyện tranh ${comic.title}, ${comic.title} - tới chương ${comic.chapters![0].slug}`,
      },

      { property: 'og:title', content: title },
      { property: 'og:description', content: description },
      {
        property: 'og:image',
        content: comic.coverImage ?? `${this.urlService.BASE_URL}/favicon.png`,
      },
      { property: 'og:url', content: url },
      { property: 'og:type', content: 'article' },
      { property: 'og:site_name', content: 'MeTruyenMoi' },
      { itemprop: 'name', content: comic.title },
      { itemprop: 'description', content: description },
    ]);
    this.seoService.updateLink('canonical', url);
  }
  rateStar(starIndex: number) {
    this.popupService.showRateComic({
      comicID: this.comic!.id,
      initialRating: starIndex,
    });
  }
  getStarWidth(index: number): number {
    if (index <= this.comic!.rating) {
      return 100;
    }
    if (index - this.comic!.rating < 1) {      
      return (1 - (index - this.comic!.rating)) * 100;
    }
    return 0;
  }
}
