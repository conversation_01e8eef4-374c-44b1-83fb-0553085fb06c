// ===== CHAPTER HEADER COMPONENT =====
// Optimized styles using Tailwind @apply

// ===== CHAPTER HEADER =====
.chapter-header-container {
  @apply mx-auto container flex flex-col items-center font-bold text-base static w-full;
}

.chapter-header-card {
  @apply bg-white dark:bg-neutral-800 border text-black dark:text-white 
         p-4 lg:p-6 w-full rounded-t-xl dark:border-neutral-700 z-20;
}

.chapter-info-section {
  @apply text-center space-y-6;
}

// ===== REPORT ERROR BUTTON =====
.report-error-button {
  @apply flex flex-col items-center gap-2 px-4 py-3 mx-auto
         bg-yellow-50 dark:bg-yellow-900/20 
         hover:bg-yellow-100 dark:hover:bg-yellow-900/30 
         text-yellow-600 dark:text-yellow-400 
         rounded-lg 
         border border-yellow-200 dark:border-yellow-800 
         hover:border-yellow-300 dark:hover:border-yellow-700
         transition-colors duration-200;
}

.report-icon {
  @apply w-6 h-6;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.report-text {
  @apply text-xs font-bold;
}

// ===== COMIC TITLE =====
.comic-title-section {
  @apply space-y-2;
}

.comic-title {
  @apply text-center;
}

.comic-title-link {
  @apply text-xl lg:text-2xl font-bold uppercase 
         text-primary-100 hover:text-primary-200 
         hover:underline transition-colors duration-200 cursor-pointer;
}

// ===== CHAPTER DETAILS =====
.chapter-details {
  @apply space-y-3;
}

.chapter-title {
  @apply text-base lg:text-lg font-semibold text-gray-900 dark:text-white;
}

.chapter-date {
  @apply text-xs font-medium text-gray-500 dark:text-gray-300;
}

// ===== SERVER SELECTION =====
.server-selection-section {
  @apply mt-6;
}

.server-list {
  @apply flex flex-wrap items-center justify-center gap-3;
}

.server-button {
  @apply flex items-center gap-2 px-4 py-2 
         bg-gray-100 dark:bg-neutral-700 
         hover:bg-gray-200 dark:hover:bg-neutral-600 
         text-gray-600 dark:text-gray-300 
         rounded-lg 
         border border-gray-200 dark:border-neutral-600 
         text-sm font-medium
         transition-all duration-200;

  &.server-button-active {
    @apply text-cyan-500 hover:bg-primary-50 border-cyan-500;
  }
}

.server-icon {
  @apply w-5 h-5;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.server-text {
  @apply font-medium;
}

.server-expand-button {
  @apply flex items-center justify-center p-2 
         bg-gray-100 dark:bg-neutral-700 
         hover:bg-gray-200 dark:hover:bg-neutral-600 
         text-gray-600 dark:text-gray-300 
         rounded-lg 
         border border-gray-200 dark:border-neutral-600
         transition-all duration-200;
}

.expand-icon {
  @apply w-5 h-5 transition-transform duration-200;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;

  &.expand-icon-rotated {
    @apply rotate-180;
  }
}
