import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { UrlService } from './url.service';
@Injectable({
  providedIn: 'root',
})
export class ReportErrorService {
  constructor(private http: HttpClient, private urlService: UrlService) { }

  sendReport(body: {
    name: string;
    errorType: string;
    message: string;
    chapterid?: number;
  }): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    return this.http.post(`${this.urlService.apiUrl}/comic/report`, body, {
      headers,
    });
  }


  sendFeedback(body: {
    mail: string;
    content: string;
  }): Observable<any> {
    const headers = new HttpHeaders({ 'Content-Type': 'application/json' });
    return this.http.post(`${this.urlService.apiUrl}/comic/feedback`, body, {
      headers,
    });
  }
}
