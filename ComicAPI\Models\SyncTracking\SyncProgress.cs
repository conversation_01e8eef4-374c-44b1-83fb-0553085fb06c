using System.ComponentModel.DataAnnotations;

namespace ComicApp.Models.SyncTracking
{
    public class SyncProgress
    {
        [Required]
        public string SessionId { get; set; } = string.Empty;

        [Required]
        public SyncStage Stage { get; set; } = SyncStage.Idle;

        [Range(0, 100)]
        public int Progress { get; set; } = 0;

        [Required]
        public string Message { get; set; } = string.Empty;

        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        public DateTime? EndTime { get; set; }

        public int? TotalComics { get; set; }

        public int? ProcessedComics { get; set; }

        public List<string> Errors { get; set; } = new List<string>();

        
    }

    public enum SyncStage
    {
        Idle,
        Connecting,
        Fetching,
        Comparing,
        Syncing,
        Completed,
        Error
    }
}
