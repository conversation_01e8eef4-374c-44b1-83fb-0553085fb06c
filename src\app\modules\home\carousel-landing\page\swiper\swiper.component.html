<div class="mt-3 relative z-30 flex w-[94px] h-3 items-center overflow-hidden">
  <button
    #btn
    *ngFor="let e of Btns; let i = index"
    [style.left]="getLeftPositon(i)"
    [class]="
      current === i ? 'bg-primary-100' : 'dark:bg-neutral-600 bg-slate-300'
    "
    [ngClass]="{
      'size-3': current === i,
      'size-2.5': current - i === 1 || current - i === -1,
      'size-2': current - i > 1 || current - i < -1,
    }"
    (click)="click(i)"
    type="button"
    class="absolute swiper-btn rounded-full transition-[left,width,height] duration-500 ease-in-out"
    aria-current="true"
    aria-label="button 1"
    data-carousel-slide-to="0"
  ></button>
</div>
