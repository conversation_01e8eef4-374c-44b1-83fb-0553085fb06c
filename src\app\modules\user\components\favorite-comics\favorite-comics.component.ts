import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { Router } from '@angular/router';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { ComicCardComponent } from '@components/common/comic-card/Ver1/comic-card.component';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { Comic } from '@schema';
import { AccountService } from '@services/account.service';

@Component({
  selector: 'app-favorite-comics',
  standalone: true,
  imports: [CommonModule, ComicCardComponent, PaginationComponent],
  templateUrl: './favorite-comics.component.html',
  styleUrl: './favorite-comics.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FavoriteComicsComponent extends OptimizedBaseComponent implements OnInit {
  // Signals for reactive state
  private readonly favoriteComicsSignal = signal<Comic[]>([]);
  private readonly isLoadingSignal = signal<boolean>(false);
  private readonly currentPageSignal = signal<number>(1);
  private readonly totalPagesSignal = signal<number>(1);
  private readonly errorMessageSignal = signal<string>('');

  // Computed properties
  readonly favoriteComics = computed(() => this.favoriteComicsSignal());
  readonly isLoading = computed(() => this.isLoadingSignal());
  readonly currentPage = computed(() => this.currentPageSignal());
  readonly totalPages = computed(() => this.totalPagesSignal());
  readonly errorMessage = computed(() => this.errorMessageSignal());

  // Computed getters
  get hasComics(): boolean {
    return this.favoriteComics().length > 0;
  }

  get showPagination(): boolean {
    return this.totalPages() > 1 && this.hasComics;
  }

  get hasError(): boolean {
    return this.errorMessage().length > 0;
  }

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private accountService: AccountService,
    private router: Router,
  ) {
    super(cdr, platformId);
  }

  ngOnInit(): void {
    this.loadFavoriteComics(1);

  }



  private loadFavoriteComics(page: number = 1): void {
    this.isLoadingSignal.set(true);
    this.errorMessageSignal.set('');
    this.runInBrowser(() => {
      this.addSubscription(
        this.accountService.GetFollowedComics(page, 18).subscribe({
          next: (response: any) => {

            if (response?.data) {
              const comics = response.data.comics || [];
              this.favoriteComicsSignal.set(comics);
              this.totalPagesSignal.set(response.data.totalpage || 1);
              this.currentPageSignal.set(page);

            } else if (response?.success === false) {
              // Handle API error response
              this.favoriteComicsSignal.set([]);
              this.totalPagesSignal.set(1);
              this.currentPageSignal.set(1);
              this.errorMessageSignal.set(response.message || 'Không thể tải danh sách truyện yêu thích');
            } else {
              this.favoriteComicsSignal.set([]);
              this.totalPagesSignal.set(1);
              this.currentPageSignal.set(1);
            }
            this.isLoadingSignal.set(false);
            this.safeMarkForCheck();
          },
          error: (error) => {
            console.error('Error loading favorite comics:', error);
            this.favoriteComicsSignal.set([]);
            this.isLoadingSignal.set(false);

            // Set user-friendly error message
            if (error.status === 401) {
              this.errorMessageSignal.set('Bạn cần đăng nhập để xem danh sách truyện yêu thích');
            } else if (error.status === 0) {
              this.errorMessageSignal.set('Không thể kết nối đến server. Vui lòng kiểm tra kết nối mạng');
            } else {
              this.errorMessageSignal.set('Có lỗi xảy ra khi tải danh sách truyện yêu thích');
            }

            this.safeMarkForCheck();
          }
        })
      );
    });
  }

  // Event handlers
  onPageChange(page: number): void {
    this.loadFavoriteComics(page);
  }

  onComicClick(comicId: number): void {
    // Handle comic click - navigate to comic detail page
    const comic = this.favoriteComics().find(c => c.id === comicId);
    if (comic) {
      // Navigate to comic detail page
        this.router.navigate(['/truyen-tranh', `${comic.url}-${comic.id}`]);
    }
  }

  onExploreClick(): void {
    // Navigate to explore page
     this.router.navigate(['/tim-truyen']);
  }

  onRetryClick(): void {
    this.loadFavoriteComics(this.currentPage());
  }

  // TrackBy functions
  trackByComicId = (_index: number, comic: Comic): number => {
    return comic.id;
  };
}
