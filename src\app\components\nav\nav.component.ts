import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  HostListener,
  Inject,
  OnDestroy,
  OnInit,
  PLATFORM_ID,
  signal
} from '@angular/core';
import { RouterLink } from '@angular/router';
import { GenreCatagoriesComponent } from '@components/common/genre-catagories/genre-catagories.component';
import { Genre, IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { ComicService } from '@services/comic.service';
import { PopupService } from '@services/popup.service';
import { SettingService } from '@services/setting.service';
import { OptimizedBaseComponent } from '../common/base/optimized-base.component';
import { NotifyComponent } from '../common/notify/notify.component';
import { UserMenuComponent } from '../user-menu/user-menu.component';
import { ListSearchComicComponent } from './list-search-comic/list-search-comic.component';
@Component({
  selector: 'app-nav',
  templateUrl: './nav.component.html',
  styleUrl: './nav.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule, NotifyComponent, RouterLink, ListSearchComicComponent, GenreCatagoriesComponent, UserMenuComponent],
})

export class NavComponent extends OptimizedBaseComponent implements OnInit, AfterViewInit, OnDestroy {
  onSettingClick() {
    this.popupService.showSetting();
  }
  toggleTheme() {
    this.setingService.setSettingValue('theme', this.isDarkMode() ? 'light' : 'dark');
  }


  // Component state
  listGenres: Genre[] = [];
  searchText = '';
  maxHeight = 0;
  avatar = '';
  user?: IUser;
  showSidebar = false;
  isShowGenre = false;

  isDarkModeSignal = signal(false);
  isDarkMode = computed(() => {
    return this.isDarkModeSignal()
  });


  // Performance optimizations
  private debouncedToggleSidebar: Function;

  constructor(
    cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) platformId: object,
    private comicService: ComicService,
    private setingService: SettingService,
    public popupService: PopupService,
    private accountService: AccountService,
  ) {
    super(cdr, platformId);
    this.debouncedToggleSidebar = this.debounce(this.performToggleSidebar.bind(this), 100);
  }


  get hasGenres(): boolean {
    return this.listGenres.length > 0;
  }



  // TrackBy functions for ngFor optimization
  trackByGenreId = (index: number, genre: Genre): number => {
    return genre.id;
  };
  ngOnInit(): void {
    this.accountService.GetLocalUser().subscribe((user) => {
      this.user = user;
      this.safeMarkForCheck();
    });
    this.isDarkModeSignal.set(this.setingService.getSettingValue('theme') === 'dark')
    this.setingService.settingChanges$.subscribe((event) => {
      if (event.settingId === 'theme') {
        this.isDarkModeSignal.set(event.newValue === 'dark');
      }
    });
  }

  ngAfterViewInit(): void {

  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  onFeedbackClick(): void {
    this.popupService.showFeedback();
  }

  @HostListener('window:scroll', ['$event'])
  onWindowScroll(event: Event) {
    if (this.showSidebar) {
      this.showSidebar = false;
      this.safeMarkForCheck();

    }
  }


  toggleSidebar(): void {
    this.debouncedToggleSidebar();
  }

  private performToggleSidebar(): void {
    this.showSidebar = !this.showSidebar;
    // this.runInBrowser(() => {
    //   const height = this.showSidebar ? `${this.maxHeight}px` : '0px';
    //   this.dropdownNavbar.nativeElement.style.height = height;
    // });
    this.safeMarkForCheck();
  }



}
