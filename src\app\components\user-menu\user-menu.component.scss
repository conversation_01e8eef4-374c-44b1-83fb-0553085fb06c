// User Menu Component Styles
.user-menu-wrapper {
  @apply relative;
}

.user-menu-trigger {
  @apply flex items-center gap-3 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer border-none bg-transparent;


  // Guest user trigger styles
  &.guest {
    @apply bg-gray-200 dark:bg-gray-700;

    &:hover {
      @apply bg-gray-300 dark:bg-gray-600;
    }


  }
}

.user-avatar-container {
  @apply relative;
}

.user-avatar {
  @apply w-10 h-10 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600;
}

.user-status-indicator {
  @apply absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full;
}

.user-info-preview {
  @apply flex items-center gap-2;
}

.user-name-preview {
  @apply font-medium text-gray-900 dark:text-white text-sm;
}

.dropdown-chevron {
  @apply w-4 h-4 text-gray-500 dark:text-neutral-400;

  &.rotated {
    transform: rotate(180deg);
  }
}

.guest-avatar-container {
  @apply relative flex items-center justify-center;
}

.guest-avatar-icon {
  @apply size-6 text-gray-600 dark:text-neutral-400;
}

.guest-info-preview {
  @apply flex items-center gap-2;
}

.guest-name-preview {
  @apply font-medium text-gray-700 dark:text-neutral-300 text-sm;
}

// // Backdrop
.dropdown-backdrop {
  @apply fixed inset-0 z-40 bg-black/20;
}