<!-- Sync Results Component -->
<div *ngIf="true" class="sync-results-container" [class.success]="isSuccess()" [class.error]="!isSuccess()">
  <!-- Results Header -->
  <div class="results-header">
    <div class="header-icon" [class.success]="isSuccess()" [class.error]="!isSuccess()">
      <svg *ngIf="isSuccess()" class="icon success" viewBox="0 0 24 24">
        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      <svg *ngIf="!isSuccess()" class="icon error" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10"/>
        <line x1="15" y1="9" x2="9" y2="15"/>
        <line x1="9" y1="9" x2="15" y2="15"/>
      </svg>
    </div>
    
    <div class="header-content">
      <h2 class="header-title">
        {{ isSuccess() ? 'Đồng bộ hoàn tất!' : 'Đồng bộ thất bại' }}
      </h2>
      <p class="header-subtitle">
        {{ isSuccess() ? 'Dữ liệu đã được đồng bộ thành công' : 'Có lỗi xảy ra trong quá trình đồng bộ' }}
      </p>
    </div>
    
    <div class="success-rate" *ngIf="isSuccess()">
      <div class="rate-circle">
        <span class="rate-text">{{ successRate() }}%</span>
      </div>
    </div>
  </div>

  <!-- Summary Stats -->
  <div class="summary-stats">
    <div 
      *ngFor="let stat of summaryStats(); trackBy: trackByLabel"
      class="stat-card"
      [ngClass]="getStatBgColor(stat.color)"
    >
      <div class="stat-value" [ngClass]="getStatColor(stat.color)">{{ stat.value }}</div>
      <div class="stat-label">{{ stat.label }}</div>
    </div>
  </div>

  <!-- Detailed Stats (if showDetails is true) -->
  <div *ngIf="result()?.summary" class="detail-stats">
    <h3 class="detail-title">Chi tiết đồng bộ</h3>
    <div class="detail-grid">
      <div 
        *ngFor="let stat of detailStats(); trackBy: trackByLabel"
        class="detail-item"
      >
        <div class="detail-icon" [ngClass]="getStatBgColor(stat.color)">
          <div class="detail-dot" [ngClass]="getStatColor(stat.color)"></div>
        </div>
        <div class="detail-content">
          <span class="detail-value">{{ stat.value }}</span>
          <span class="detail-label">{{ stat.label }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Tabs Navigation -->


  <!-- Tab Content -->


  <!-- Action Buttons -->
  <div class="results-actions">
    <button class="action-button secondary" (click)="downloadReport()">
      <svg class="button-icon" viewBox="0 0 24 24">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="7,10 12,15 17,10"/>
        <line x1="12" y1="15" x2="12" y2="3"/>
      </svg>
      <span class="button-text">Tải báo cáo</span>
    </button>

    <button class="action-button tertiary" (click)="shareResults()">
      <svg class="button-icon" viewBox="0 0 24 24">
        <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
        <polyline points="16,6 12,2 8,6"/>
        <line x1="12" y1="2" x2="12" y2="15"/>
      </svg>
      <span class="button-text">Chia sẻ</span>
    </button>

    <button class="action-button primary" (click)="startNewSync()">
      <svg class="button-icon" viewBox="0 0 24 24">
        <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
        <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
      </svg>
      <span class="button-text">Đồng bộ mới</span>
    </button>
  </div>
</div>
