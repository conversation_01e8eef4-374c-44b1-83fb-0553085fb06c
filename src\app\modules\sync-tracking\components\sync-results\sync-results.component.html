<!-- Sync Results Component -->
<div *ngIf="hasResult()" class="sync-results-container" [class.success]="isSuccess()" [class.error]="!isSuccess()">
  <!-- Results Header -->
  <div class="results-header">
    <div class="header-icon" [class.success]="isSuccess()" [class.error]="!isSuccess()">
      <svg *ngIf="isSuccess()" class="icon success" viewBox="0 0 24 24">
        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
      </svg>
      <svg *ngIf="!isSuccess()" class="icon error" viewBox="0 0 24 24">
        <circle cx="12" cy="12" r="10"/>
        <line x1="15" y1="9" x2="9" y2="15"/>
        <line x1="9" y1="9" x2="15" y2="15"/>
      </svg>
    </div>
    
    <div class="header-content">
      <h2 class="header-title">
        {{ isSuccess() ? 'Đồng bộ hoàn tất!' : 'Đồng bộ thất bại' }}
      </h2>
      <p class="header-subtitle">
        {{ isSuccess() ? 'Dữ liệu đã được đồng bộ thành công' : 'Có lỗi xảy ra trong quá trình đồng bộ' }}
      </p>
    </div>
    
    <div class="success-rate" *ngIf="isSuccess()">
      <div class="rate-circle">
        <span class="rate-text">{{ successRate() }}%</span>
      </div>
    </div>
  </div>

  <!-- Summary Stats -->
  <div class="summary-stats">
    <div 
      *ngFor="let stat of summaryStats(); trackBy: trackByLabel"
      class="stat-card"
      [ngClass]="getStatBgColor(stat.color)"
    >
      <div class="stat-value" [ngClass]="getStatColor(stat.color)">{{ stat.value }}</div>
      <div class="stat-label">{{ stat.label }}</div>
    </div>
  </div>

  <!-- Detailed Stats (if showDetails is true) -->
  <div *ngIf="showDetails && result()?.summary" class="detail-stats">
    <h3 class="detail-title">Chi tiết đồng bộ</h3>
    <div class="detail-grid">
      <div 
        *ngFor="let stat of detailStats(); trackBy: trackByLabel"
        class="detail-item"
      >
        <div class="detail-icon" [ngClass]="getStatBgColor(stat.color)">
          <div class="detail-dot" [ngClass]="getStatColor(stat.color)"></div>
        </div>
        <div class="detail-content">
          <span class="detail-value">{{ stat.value }}</span>
          <span class="detail-label">{{ stat.label }}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Tabs Navigation -->
  <div *ngIf="showDetails" class="tabs-navigation">
    <div class="tabs-container">
      <button
        *ngFor="let tab of tabs(); trackBy: trackByKey"
        class="tab-button"
        [class.active]="selectedTab() === tab.key"
        (click)="onTabClick(tab.key)"
      >
        <span class="tab-label">{{ tab.label }}</span>
        <span *ngIf="tab.count !== null" class="tab-count">{{ tab.count }}</span>
      </button>
    </div>
  </div>

  <!-- Tab Content -->
  <div *ngIf="showDetails" class="tab-content">
    <!-- Summary Tab -->
    <div *ngIf="selectedTab() === 'summary'" class="tab-panel summary-panel">
      <div class="summary-grid">
        <div class="summary-section">
          <h4 class="section-title">Thống kê tổng quan</h4>
          <div class="section-content">
            <div class="summary-item">
              <span class="item-label">Tổng số truyện:</span>
              <span class="item-value">{{ result()?.totalComics || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="item-label">Đã đồng bộ:</span>
              <span class="item-value success">{{ result()?.syncedComics || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="item-label">Lỗi:</span>
              <span class="item-value error">{{ result()?.errors?.length || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="item-label">Thời gian:</span>
              <span class="item-value">{{ formattedDuration() }}</span>
            </div>
          </div>
        </div>

        <div class="summary-section">
          <h4 class="section-title">Chi tiết theo nguồn</h4>
          <div class="section-content">
            <div class="summary-item">
              <span class="item-label">NetTruyen:</span>
              <span class="item-value">{{ result()?.summary?.nettruyenComics || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="item-label">TruyenQQ:</span>
              <span class="item-value">{{ result()?.summary?.truyenqqComics || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="item-label">Mới thêm:</span>
              <span class="item-value new">{{ result()?.summary?.newComics || 0 }}</span>
            </div>
            <div class="summary-item">
              <span class="item-label">Cập nhật:</span>
              <span class="item-value updated">{{ result()?.summary?.updatedComics || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Errors Tab -->
    <div *ngIf="selectedTab() === 'errors'" class="tab-panel errors-panel">
      <div *ngIf="!hasErrors()" class="empty-state">
        <div class="empty-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
        </div>
        <h4 class="empty-title">Không có lỗi</h4>
        <p class="empty-subtitle">Tất cả truyện đã được đồng bộ thành công!</p>
      </div>

      <div *ngIf="hasErrors()" class="errors-list">
        <div 
          *ngFor="let error of result()?.errors; trackBy: trackByErrorIndex"
          class="error-item"
        >
          <div class="error-icon">
            <svg class="icon" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
          </div>
          <div class="error-content">
            <h5 class="error-title">{{ error.comicTitle }}</h5>
            <p class="error-message">{{ error.error }}</p>
            <span class="error-source">{{ error.source }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Comics Tab -->
    <div *ngIf="selectedTab() === 'comics'" class="tab-panel comics-panel">
      <div class="comics-placeholder">
        <div class="placeholder-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/>
            <path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/>
          </svg>
        </div>
        <h4 class="placeholder-title">Danh sách truyện</h4>
        <p class="placeholder-subtitle">Chi tiết danh sách truyện đã đồng bộ sẽ được hiển thị ở đây</p>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="results-actions">
    <button class="action-button secondary" (click)="downloadReport()">
      <svg class="button-icon" viewBox="0 0 24 24">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="7,10 12,15 17,10"/>
        <line x1="12" y1="15" x2="12" y2="3"/>
      </svg>
      <span class="button-text">Tải báo cáo</span>
    </button>

    <button class="action-button tertiary" (click)="shareResults()">
      <svg class="button-icon" viewBox="0 0 24 24">
        <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"/>
        <polyline points="16,6 12,2 8,6"/>
        <line x1="12" y1="2" x2="12" y2="15"/>
      </svg>
      <span class="button-text">Chia sẻ</span>
    </button>

    <button class="action-button primary" (click)="startNewSync()">
      <svg class="button-icon" viewBox="0 0 24 24">
        <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
        <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
      </svg>
      <span class="button-text">Đồng bộ mới</span>
    </button>
  </div>
</div>
