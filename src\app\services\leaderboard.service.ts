import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IServiceResponse } from '@schema';
import { Observable } from 'rxjs';
import { UrlService } from './url.service';

export interface IUserLite {
  id: number;
  firstName: string;
  lastName?: string;
  avatar: string;
  experience: number;
  maxim: string;
  typeLevel: number;
}



@Injectable({
  providedIn: 'root'
})
export class LeaderboardService {

  constructor(private http: HttpClient, private urlService: UrlService) {}

  /**
   * Get top users by experience points
   * @param limit Number of top users to return (default: 5, max: 20)
   * @returns Observable of top users response
   */
  getTopUsers(limit: number = 5): Observable<IServiceResponse<IUserLite[]>> {
    return this.http.get<IServiceResponse<IUserLite[]>>(`${this.urlService.API_URL}/top-users`, {
      transferCache: true,
      params: { limit: limit.toString(), expiration: 600 }
    });
  }


  /**
   * Get top 5 users (convenience method)
   * @returns Observable of top 5 users response
   */
  getTop5Users(): Observable<IServiceResponse<IUserLite[]>> {
    return this.getTopUsers(5);
  }

  /**
   * Get top 10 users (convenience method)
   * @returns Observable of top 10 users response
   */
  getTop10Users(): Observable<IServiceResponse<IUserLite[]>> {
    return this.getTopUsers(10);
  }
}
