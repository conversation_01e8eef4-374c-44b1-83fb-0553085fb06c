import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Pipe({
    name: 'emojiParser',
    standalone: true
})
export class EmojiPipe implements PipeTransform {
  constructor(private sanitizer: DomSanitizer) { }

  transform(value: string): SafeHtml {
    if (!value) {
      return value;
    }
    const parsed = value.replace(
      /<e>(.*?)<\/e>/g,
      (match, emoji) => {
        const formattedEmoji = emoji.replace('_', '/');
        return `<img  class="w-[50px] h-[50px] inline-block align-middle mx-[5px]" src="/emoji/data/${formattedEmoji}.gif" alt="${emoji}">`;
      }
    );

    return parsed
  }
}
