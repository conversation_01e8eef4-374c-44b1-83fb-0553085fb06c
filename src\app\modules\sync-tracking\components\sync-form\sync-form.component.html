<!-- Sync Form Component -->
<div class="sync-form-container">
  <!-- Main Form -->
  <form class="sync-form" [formGroup]="syncForm" (ngSubmit)="onSubmit()">

    <!-- Step 1: Source Selection -->
    <div *ngIf="currentStep() === 'source'" class="form-step source-step">
      <div class="step-header">
        <div class="step-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
            <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
          </svg>
        </div>
        <div class="step-content">
          <h2 class="step-title">Chọn nguồn đồng bộ</h2>
          <p class="step-subtitle">Chọn website nguồn và website đích để đồng bộ dữ liệu theo dõi truyện</p>
        </div>
      </div>

      <div class="source-selection">
        <!-- Source From -->
        <div class="source-group">
          <label class="source-label">Đồng bộ từ</label>
          <div class="source-options">
            <div
              *ngFor="let option of sourceOptions(); trackBy: trackByValue"
              class="source-option"
              [class.selected]="syncForm.get('sourceFrom')?.value === option.value"
              (click)="syncForm.get('sourceFrom')?.setValue(option.value)"
            >
              <div class="option-icon" [attr.data-color]="option.color">
                <svg class="icon" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 12l2 2 4-4"/>
                </svg>
              </div>
              <div class="option-content">
                <h3 class="option-title">{{ option.label }}</h3>
                <p class="option-description">Lấy dữ liệu từ {{ option.label }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Sync Direction Arrow -->
        <div class="sync-arrow" *ngIf="syncForm.get('sourceFrom')?.value">
          <svg class="arrow-icon" viewBox="0 0 24 24">
            <line x1="5" y1="12" x2="19" y2="12"/>
            <polyline points="12,5 19,12 12,19"/>
          </svg>
        </div>

        <!-- Source To -->
        <div class="source-group">
          <label class="source-label">Đồng bộ đến</label>
          <div class="source-options">
            <div
              *ngFor="let option of getAvailableTargets(); trackBy: trackByValue"
              class="source-option"
              [class.selected]="syncForm.get('sourceTo')?.value === option.value"
              [class.disabled]="!syncForm.get('sourceFrom')?.value"
              (click)="syncForm.get('sourceFrom')?.value && syncForm.get('sourceTo')?.setValue(option.value)"
            >
              <div class="option-icon" [attr.data-color]="option.color">
                <svg class="icon" viewBox="0 0 24 24">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 12l2 2 4-4"/>
                </svg>
              </div>
              <div class="option-content">
                <h3 class="option-title">{{ option.label }}</h3>
                <p class="option-description">Cập nhật dữ liệu vào {{ option.label }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sync Direction Summary -->
      <div *ngIf="syncDirections()" class="sync-summary">
        <div class="summary-card">
          <div class="summary-icon">
            <svg class="icon" viewBox="0 0 24 24">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
          </div>
          <div class="summary-content">
            <h3 class="summary-title">Hướng đồng bộ</h3>
            <p class="summary-text">{{ syncDirections() }}</p>
          </div>
        </div>
      </div>

      <!-- Step Actions -->
      <div class="step-actions">
        <button
          type="button"
          class="action-button primary"
          [disabled]="!isSourceStepValid()"
          (click)="nextStep()"
        >
          <span class="button-text">Tiếp tục</span>
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="9 18 15 12 9 6"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Step 2: Credentials -->
    <div *ngIf="currentStep() === 'credentials'" class="form-step credentials-step">
      <div class="step-header">
        <div class="step-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
          </svg>
        </div>
        <div class="step-content">
          <h2 class="step-title">Thông tin đăng nhập</h2>
          <p class="step-subtitle">Nhập thông tin tài khoản {{ syncForm.get('sourceFrom')?.value }} để truy cập dữ liệu</p>
        </div>
      </div>

      <div class="credentials-form">
        <div class="form-group">
          <label class="form-label">Tên đăng nhập</label>
          <div class="input-wrapper">
            <input
              type="text"
              class="form-input"
              formControlName="username"
              placeholder="Nhập tên đăng nhập"
              [class.error]="syncForm.get('username')?.invalid && syncForm.get('username')?.touched"
            />
            <svg class="input-icon" viewBox="0 0 24 24">
              <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
          </div>
          <div class="form-error" *ngIf="syncForm.get('username')?.invalid && syncForm.get('username')?.touched">
            Tên đăng nhập phải có ít nhất 3 ký tự
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">Mật khẩu</label>
          <div class="input-wrapper">
            <input
              [type]="showPassword() ? 'text' : 'password'"
              class="form-input"
              formControlName="password"
              placeholder="Nhập mật khẩu"
              [class.error]="syncForm.get('password')?.invalid && syncForm.get('password')?.touched"
            />
            <button
              type="button"
              class="input-action"
              (click)="togglePasswordVisibility()"
            >
              <svg class="action-icon" viewBox="0 0 24 24">
                <path *ngIf="!showPassword()" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle *ngIf="!showPassword()" cx="12" cy="12" r="3"/>
                <path *ngIf="showPassword()" d="M17.94 17.94A10.07 10.07 0 0112 20c-7 0-11-8-11-8a18.45 18.45 0 015.06-5.94M9.9 4.24A9.12 9.12 0 0112 4c7 0 11 8 11 8a18.5 18.5 0 01-2.16 3.19m-6.72-1.07a3 3 0 11-4.24-4.24"/>
                <line *ngIf="showPassword()" x1="1" y1="1" x2="23" y2="23"/>
              </svg>
            </button>
          </div>
          <div class="form-error" *ngIf="syncForm.get('password')?.invalid && syncForm.get('password')?.touched">
            Mật khẩu phải có ít nhất 6 ký tự
          </div>
        </div>

        <button
          type="button"
          class="test-button"
          (click)="testCredentials()"
          [disabled]="isLoading() || !isCredentialsStepValid()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span class="button-text">Kiểm tra kết nối</span>
        </button>

        <div *ngIf="credentialsValid()" class="validation-success">
          <svg class="success-icon" viewBox="0 0 24 24">
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
          </svg>
          <span class="success-text">Thông tin đăng nhập hợp lệ!</span>
        </div>
      </div>

      <!-- Step Actions -->
      <div class="step-actions">
        <button
          type="button"
          class="action-button secondary"
          (click)="prevStep()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="15 18 9 12 15 6"/>
          </svg>
          <span class="button-text">Quay lại</span>
        </button>

        <button
          type="button"
          class="action-button primary"
          [disabled]="!isCredentialsStepValid()"
          (click)="nextStep()"
        >
          <span class="button-text">Tiếp tục</span>
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="9 18 15 12 9 6"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Step 3: Settings -->
    <div *ngIf="currentStep() === 'settings'" class="form-step settings-step">
      <div class="step-header">
        <div class="step-icon">
          <svg class="icon" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="3"/>
            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
          </svg>
        </div>
        <div class="step-content">
          <h2 class="step-title">Cài đặt đồng bộ</h2>
          <p class="step-subtitle">Tùy chỉnh cách thức đồng bộ dữ liệu theo ý muốn</p>
        </div>
      </div>

      <div class="settings-grid">
        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              class="setting-checkbox"
              formControlName="autoResolveConflicts"
            />
            <div class="checkbox-custom">
              <svg class="checkbox-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="setting-content">
              <span class="setting-title">Tự động giải quyết xung đột</span>
              <span class="setting-description">Tự động chọn dữ liệu mới nhất khi có xung đột</span>
            </div>
          </label>
        </div>

        <div class="setting-item">
          <label class="setting-label">Nguồn ưu tiên</label>
          <select class="setting-select" formControlName="preferredSource">
            <option value="latest">Dữ liệu mới nhất</option>
            <option value="nettruyen">NetTruyen</option>
            <option value="truyenqq">TruyenQQ</option>
          </select>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              class="setting-checkbox"
              formControlName="enableNotifications"
            />
            <div class="checkbox-custom">
              <svg class="checkbox-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="setting-content">
              <span class="setting-title">Bật thông báo</span>
              <span class="setting-description">Nhận thông báo khi đồng bộ hoàn tất</span>
            </div>
          </label>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              class="setting-checkbox"
              formControlName="backupBeforeSync"
            />
            <div class="checkbox-custom">
              <svg class="checkbox-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="setting-content">
              <span class="setting-title">Sao lưu trước khi đồng bộ</span>
              <span class="setting-description">Tạo bản sao lưu dữ liệu hiện tại</span>
            </div>
          </label>
        </div>
      </div>

      <!-- Step Actions -->
      <div class="step-actions">
        <button
          type="button"
          class="action-button secondary"
          (click)="prevStep()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <polyline points="15 18 9 12 15 6"/>
          </svg>
          <span class="button-text">Quay lại</span>
        </button>

        <button
          type="button"
          class="action-button tertiary"
          (click)="resetForm()"
          [disabled]="isLoading()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          <span class="button-text">Đặt lại</span>
        </button>

        <button
          type="submit"
          class="action-button primary"
          [disabled]="!canStartSync()"
        >
          <svg class="button-icon" viewBox="0 0 24 24">
            <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
            <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
          </svg>
          <span class="button-text">Bắt đầu đồng bộ</span>
        </button>
      </div>
    </div>

  </form>
</div>