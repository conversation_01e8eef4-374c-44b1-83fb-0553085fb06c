<!-- Sync Form Component -->
<div class="sync-form-container">
  <!-- Header -->
  <div class="sync-form-header">
    <div class="header-content">
      <div class="header-icon">
        <svg class="icon-sync" viewBox="0 0 24 24">
          <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
          <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
        </svg>
      </div>
      <div class="header-text">
        <h1 class="header-title"><PERSON><PERSON><PERSON> bộ theo dõ<PERSON> t<PERSON></h1>
        <p class="header-subtitle"><PERSON><PERSON><PERSON> bộ danh sách theo dõi giữa NetTruyen và TruyenQQ</p>
      </div>
    </div>
  </div>

  <!-- Main Form -->
  <form [formGroup]="myForm" class="sync-form" (ngSubmit)="onSubmit()">
    <!-- Credentials Section -->
    <div class="form-section">
      <div class="section-header">
        <h2 class="section-title">Thông tin đăng nhập</h2>
        <p class="section-subtitle">Nhập thông tin tài khoản của bạn trên cả hai website</p>
      </div>

      <div class="credentials-grid">
        <!-- NetTruyen Credentials -->
        <div class="credential-card">
          <div class="card-header">
            <div class="site-logo nettruyen-logo">
              <svg class="logo-icon" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                <path d="M8 12l2 2 4-4"/>
              </svg>
            </div>
            <div class="site-info">
              <h3 class="site-name">NetTruyen</h3>
              <span class="site-url">nettruyen.com</span>
            </div>
            <div class="validation-status" [class.valid]="credentialsValid().nettruyen">
              <svg *ngIf="credentialsValid().nettruyen" class="status-icon valid" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>

          <div class="form-fields" formGroupName="nettruyen">
            <div class="field-group">
              <label class="field-label">Tên đăng nhập</label>
              <div class="input-wrapper">
                <input
                  type="text"
                  class="form-input"
                  formControlName="username"
                  placeholder="Nhập tên đăng nhập"
                  [class.error]="isFieldInvalid(myForm, 'nettruyen.username')"
                />
                <svg class="input-icon" viewBox="0 0 24 24">
                  <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
              <div class="field-error" *ngIf="getFieldError(myForm, 'nettruyen.username')">
                {{ getFieldError(myForm, 'nettruyen.username') }}
              </div>
            </div>

            <div class="field-group">
              <label class="field-label">Mật khẩu</label>
              <div class="input-wrapper">
                <input
                  [type]="showPasswords().nettruyen ? 'text' : 'password'"
                  class="form-input"
                  formControlName="password"
                  placeholder="Nhập mật khẩu"
                  [class.error]="isFieldInvalid(myForm, 'nettruyen.password')"
                />
                <button
                  type="button"
                  class="input-action"
                  (click)="togglePasswordVisibility('nettruyen')"
                >
                  <svg class="action-icon" viewBox="0 0 24 24">
                    <path *ngIf="!showPasswords().nettruyen" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle *ngIf="!showPasswords().nettruyen" cx="12" cy="12" r="3"/>
                    <path *ngIf="showPasswords().nettruyen" d="M17.94 17.94A10.07 10.07 0 0112 20c-7 0-11-8-11-8a18.45 18.45 0 015.06-5.94M9.9 4.24A9.12 9.12 0 0112 4c7 0 11 8 11 8a18.5 18.5 0 01-2.16 3.19m-6.72-1.07a3 3 0 11-4.24-4.24"/>
                    <line *ngIf="showPasswords().nettruyen" x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                </button>
              </div>
              <div class="field-error" *ngIf="getFieldError(myForm, 'nettruyen.password')">
                {{ getFieldError(myForm, 'nettruyen.password') }}
              </div>
            </div>

            <button
              type="button"
              class="test-button"
              (click)="testCredentials('nettruyen')"
              [disabled]="isLoading() || !myForm.get('nettruyen')?.valid"
            >
              <svg class="button-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span class="button-text">Kiểm tra kết nối</span>
            </button>
          </div>
        </div>

        <!-- TruyenQQ Credentials -->
        <div class="credential-card">
          <div class="card-header">
            <div class="site-logo truyenqq-logo">
              <svg class="logo-icon" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                <path d="M8 12l2 2 4-4"/>
              </svg>
            </div>
            <div class="site-info">
              <h3 class="site-name">TruyenQQ</h3>
              <span class="site-url">truyenqq.com</span>
            </div>
            <div class="validation-status" [class.valid]="credentialsValid().truyenqq">
              <svg *ngIf="credentialsValid().truyenqq" class="status-icon valid" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
          </div>

          <div class="form-fields" formGroupName="truyenqq">
            <div class="field-group">
              <label class="field-label">Tên đăng nhập</label>
              <div class="input-wrapper">
                <input
                  type="text"
                  class="form-input"
                  formControlName="username"
                  placeholder="Nhập tên đăng nhập"
                  [class.error]="isFieldInvalid(myForm, 'truyenqq.username')"
                />
                <svg class="input-icon" viewBox="0 0 24 24">
                  <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                </svg>
              </div>
              <div class="field-error" *ngIf="getFieldError(myForm, 'truyenqq.username')">
                {{ getFieldError(myForm, 'truyenqq.username') }}
              </div>
            </div>

            <div class="field-group">
              <label class="field-label">Mật khẩu</label>
              <div class="input-wrapper">
                <input
                  [type]="showPasswords().truyenqq ? 'text' : 'password'"
                  class="form-input"
                  formControlName="password"
                  placeholder="Nhập mật khẩu"
                  [class.error]="isFieldInvalid(myForm, 'truyenqq.password')"
                />
                <button
                  type="button"
                  class="input-action"
                  (click)="togglePasswordVisibility('truyenqq')"
                >
                  <svg class="action-icon" viewBox="0 0 24 24">
                    <path *ngIf="!showPasswords().truyenqq" d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle *ngIf="!showPasswords().truyenqq" cx="12" cy="12" r="3"/>
                    <path *ngIf="showPasswords().truyenqq" d="M17.94 17.94A10.07 10.07 0 0112 20c-7 0-11-8-11-8a18.45 18.45 0 015.06-5.94M9.9 4.24A9.12 9.12 0 0112 4c7 0 11 8 11 8a18.5 18.5 0 01-2.16 3.19m-6.72-1.07a3 3 0 11-4.24-4.24"/>
                    <line *ngIf="showPasswords().truyenqq" x1="1" y1="1" x2="23" y2="23"/>
                  </svg>
                </button>
              </div>
              <div class="field-error" *ngIf="getFieldError(myForm, 'truyenqq.password')">
                {{ getFieldError(myForm, 'truyenqq.password') }}
              </div>
            </div>

            <button
              type="button"
              class="test-button"
              (click)="testCredentials('truyenqq')"
              [disabled]="isLoading() || !myForm.get('truyenqq')?.valid"
            >
              <svg class="button-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              <span class="button-text">Kiểm tra kết nối</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings Section -->
    <div class="form-section">
      <div class="section-header">
        <h2 class="section-title">Cài đặt đồng bộ</h2>
        <p class="section-subtitle">Tùy chỉnh cách thức đồng bộ dữ liệu</p>
      </div>

      <div class="settings-grid">
        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              class="setting-checkbox"
              formControlName="autoResolveConflicts"
            />
            <div class="checkbox-custom">
              <svg class="checkbox-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="setting-content">
              <span class="setting-title">Tự động giải quyết xung đột</span>
              <span class="setting-description">Tự động chọn dữ liệu mới nhất khi có xung đột</span>
            </div>
          </label>
        </div>

        <div class="setting-item">
          <label class="setting-label">Nguồn ưu tiên</label>
          <select class="setting-select" formControlName="preferredSource">
            <option value="latest">Dữ liệu mới nhất</option>
            <option value="nettruyen">NetTruyen</option>
            <option value="truyenqq">TruyenQQ</option>
          </select>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              class="setting-checkbox"
              formControlName="enableNotifications"
            />
            <div class="checkbox-custom">
              <svg class="checkbox-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="setting-content">
              <span class="setting-title">Bật thông báo</span>
              <span class="setting-description">Nhận thông báo khi đồng bộ hoàn tất</span>
            </div>
          </label>
        </div>

        <div class="setting-item">
          <label class="setting-label">
            <input
              type="checkbox"
              class="setting-checkbox"
              formControlName="backupBeforeSync"
            />
            <div class="checkbox-custom">
              <svg class="checkbox-icon" viewBox="0 0 24 24">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="setting-content">
              <span class="setting-title">Sao lưu trước khi đồng bộ</span>
              <span class="setting-description">Tạo bản sao lưu dữ liệu hiện tại</span>
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="form-actions">
      <button
        type="button"
        class="action-button secondary"
        (click)="resetForm()"
        [disabled]="isLoading()"
      >
        <svg class="button-icon" viewBox="0 0 24 24">
          <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
        </svg>
        <span class="button-text">Đặt lại</span>
      </button>

      <button
        type="button"
        class="action-button tertiary"
        (click)="fillSampleCredentials()"
        [disabled]="isLoading()"
      >
        <svg class="button-icon" viewBox="0 0 24 24">
          <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
        </svg>
        <span class="button-text">Dữ liệu mẫu</span>
      </button>

      <button
        type="submit"
        class="action-button primary"
        [disabled]="!canStartSync()"
      >
        <svg class="button-icon" viewBox="0 0 24 24">
          <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8z"/>
          <path d="M12 18c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
        </svg>
        <span class="button-text">Bắt đầu đồng bộ</span>
      </button>
    </div>
  </form>
</div>
