import { InputType, SettingType } from '@schema';
import { IOption } from 'src/app/dataSource/schema/IOption';

// Enhanced Setting Option Interface
export interface EnhancedSettingOption {
  id: string;
  type: SettingType;
  inputType: InputType;
  name: string;
  description: string;
  value: any;
  defaultValue: any;
  category: SettingCategory;
  order: number;
  icon?: string;
  
  // Input-specific properties
  options?: IOption[];
  min?: number;
  max?: number;
  step?: number;
  unit?: string;
  
  // Validation
  required?: boolean;
  validator?: (value: any) => boolean;
  
  // UI properties
  disabled?: boolean;
  hidden?: boolean;
  tooltip?: string;
  preview?: boolean;
  
  // Dependencies
  dependsOn?: string[];
  enabledWhen?: (settings: Map<string, any>) => boolean;
}

// Setting Categories
export enum SettingCategory {
  APPEARANCE = 'appearance',
  READING = 'reading',
  BEHAVIOR = 'behavior',
  ACCESSIBILITY = 'accessibility',
  // ADVANCED = 'advanced'
}

// Setting Group Interface
export interface SettingGroup {
  id: string;
  category: SettingCategory;
  name: string;
  description: string;
  icon: string;
  order: number;
  settings: EnhancedSettingOption[];
}

// Setting Change Event
export interface SettingChangeEvent {
  settingId: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
}

// Setting Validation Result
export interface SettingValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Setting Export/Import
export interface SettingExport {
  version: string;
  timestamp: Date;
  settings: Record<string, any>;
  metadata: {
    userAgent: string;
    platform: string;
  };
}

// Setting Search/Filter
export interface SettingFilter {
  category?: SettingCategory;
  searchTerm?: string;
  showAdvanced?: boolean;
  showHidden?: boolean;
}

// Setting Theme
export interface SettingTheme {
  id: string;
  name: string;
  description: string;
  settings: Record<string, any>;
  preview?: string;
}

// Setting Preset
export interface SettingPreset {
  id: string;
  name: string;
  description: string;
  category: SettingCategory;
  settings: Record<string, any>;
  isDefault?: boolean;
}

// Setting Migration
export interface SettingMigration {
  fromVersion: string;
  toVersion: string;
  migrate: (oldSettings: Record<string, any>) => Record<string, any>;
}

// Setting Context
export interface SettingContext {
  userId?: string;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  platform: 'web' | 'android' | 'ios';
  version: string;
}

// Setting Analytics
export interface SettingAnalytics {
  settingId: string;
  action: 'view' | 'change' | 'reset' | 'export' | 'import';
  value?: any;
  timestamp: Date;
  context: SettingContext;
}

// Setting Component Props
export interface SettingComponentProps {
  setting: EnhancedSettingOption;
  value: any;
  disabled?: boolean;
  readonly?: boolean;
  showDescription?: boolean;
  compact?: boolean;
}

// Setting Tab Interface
export interface SettingTab {
  id: string;
  name: string;
  icon: string;
  category: SettingCategory;
  badge?: number;
  disabled?: boolean;
}

// Setting Search Result
export interface SettingSearchResult {
  setting: EnhancedSettingOption;
  group: SettingGroup;
  relevanceScore: number;
  matchedFields: string[];
}

// Setting Backup
export interface SettingBackup {
  id: string;
  name: string;
  description?: string;
  settings: Record<string, any>;
  createdAt: Date;
  size: number;
}

// Setting Notification
export interface SettingNotification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  settingId?: string;
  actions?: Array<{
    label: string;
    action: () => void;
  }>;
  autoClose?: boolean;
  duration?: number;
}

// Setting Performance Metrics
export interface SettingPerformanceMetrics {
  loadTime: number;
  renderTime: number;
  settingsCount: number;
  memoryUsage?: number;
  lastUpdated: Date;
}
