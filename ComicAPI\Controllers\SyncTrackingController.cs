using System.Text;
using System.Text.Json;
using ComicApp.Models;
using ComicApp.Models.SyncTracking;
using ComicAPI.DTOs.SyncTracking;
using ComicAPI.Services.SyncTracking;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ComicAPI.Models;
using ComicAPI.Services;

namespace ComicAPI.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/sync-tracking")]
    public class SyncTrackingController : ControllerBase
    {
        private readonly ISyncTrackingService _syncService;
        private readonly IUserService _userService;
        private readonly ILogger<SyncTrackingController> _logger;

        public SyncTrackingController(
            ISyncTrackingService syncService,
            IUserService userService,
            ILogger<SyncTrackingController> logger)
        {
            _syncService = syncService;
            _logger = logger;
            _userService = userService;
        }


        /// <summary>
        /// Start sync process
        /// </summary>
        [HttpPost("start")]
        public async Task<ActionResult<ServiceResponse<object>>> StartSync([FromBody] StartSyncRequestDTO request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.Credentials))
                {
                    return BadRequest(new ServiceResponse<object>
                    {
                        Status = 400,
                        Message = "Missing credentials or settings",
                        Data = null
                    });
                }

                // Decode base64 credentials
                var credentialsJson = Encoding.UTF8.GetString(Convert.FromBase64String(request.Credentials));
                var credentials = JsonSerializer.Deserialize<SyncCredentialsDTO>(credentialsJson);

                if (credentials == null)
                {
                    return BadRequest(new ServiceResponse<object>
                    {
                        Status = 400,
                        Message = "Invalid credentials format",
                        Data = null
                    });
                }
                credentials.UserId = _userService.CurrentUser!.ID;

                // Start sync process
                var sessionId = await _syncService.StartSyncAsync(credentials);

                return Ok(new ServiceResponse<object>
                {
                    Status = 200,
                    Message = "Sync process started",
                    Data = new { sessionId, message = "Sync process started" }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Start sync error");
                return StatusCode(500, new ServiceResponse<object>
                {
                    Status = 500,
                    Message = "Failed to start sync process",
                    Data = null
                });
            }
        }



        /// <summary>
        /// Get sync progress for real-time updates
        /// </summary>
        [HttpGet("progress/{sessionId}")]
        public async Task<ActionResult<ServiceResponse<SyncProgressResponseDTO>>> GetSyncProgress(string sessionId)
        {
            try
            {
                var progress = await _syncService.GetSyncProgressAsync(sessionId);

                if (progress == null)
                {
                    return NotFound(new ServiceResponse<SyncProgressResponseDTO>
                    {
                        Status = 404,
                        Message = "Session not found",
                        Data = null
                    });
                }

                return Ok(new ServiceResponse<SyncProgressResponseDTO>
                {
                    Status = 200,
                    Message = "Progress retrieved successfully",
                    Data = new SyncProgressResponseDTO(progress)
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Get sync progress error");
                return StatusCode(500, new ServiceResponse<SyncProgressResponseDTO>
                {
                    Status = 500,
                    Message = "Failed to get sync progress",
                    Data = null
                });
            }
        }

        /// <summary>
        /// Cancel sync process
        /// </summary>
        [HttpPost("cancel/{sessionId}")]
        public async Task<ActionResult<ServiceResponse<object>>> CancelSync(string sessionId)
        {
            try
            {
                var success = await _syncService.CancelSyncAsync(sessionId);

                if (!success)
                {
                    return NotFound(new ServiceResponse<object>
                    {
                        Status = 404,
                        Message = "Session not found or already completed",
                        Data = null
                    });
                }

                return Ok(new ServiceResponse<object>
                {
                    Status = 200,
                    Message = "Sync cancelled successfully",
                    Data = null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Cancel sync error");
                return StatusCode(500, new ServiceResponse<object>
                {
                    Status = 500,
                    Message = "Failed to cancel sync",
                    Data = null
                });
            }
        }

        /// <summary>
        /// Health check endpoint
        /// </summary>
        [HttpGet("health")]
        public ActionResult<ServiceResponse<object>> HealthCheck()
        {
            return Ok(new ServiceResponse<object>
            {
                Status = 200,
                Message = "Sync tracking service is healthy",
                Data = new { status = "healthy", timestamp = DateTime.UtcNow }
            });
        }
    }
}
