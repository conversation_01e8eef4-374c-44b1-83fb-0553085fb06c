import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LayoutComponent } from '@components/layout/layout.component';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: '',
        loadChildren: () => import('./modules/home/<USER>').then((m) => m.HomeModule),
      },
      {
        path: 'truyen-tranh/:id',
        loadChildren: () => import('./modules/comic-detail/comic-detail.module').then((m) => m.DetailModule,),
      },
      {
        path: 'tim-truyen',
        loadChildren: () => import('./modules/search-page/search.module').then((m) => m.SearchModule,),
      },
      {
        path: 'xep-hang',
        loadChildren: () => import('./modules/rank/rank.module').then((m) => m.RankModule),
      },
      {
        path: 'lich-su',
        loadChildren: () => import('./modules/history-page/history-page.module').then((m) => m.HistoryPageModule),
      },
      {
        path: 'theo-doi',
        loadChildren: () => import('./modules/followed-page/followed-page.module').then((m) => m.FollowedPageModule,),
      },
      {
        path: 'truyen-hot',
        loadChildren: () => import('./modules/comic-hot/comic-hot.module').then((m) => m.ComicHotModule,),
      },
      {
        path: 'truyen-tranh/:comicid/:chapterid',
        loadChildren: () => import('./modules/chapter-page/chapter.module').then((m) => m.ChapterModule,),
      },
      {
        path: 'tai-khoan',
        loadChildren: () => import('./modules/user/user.module').then((m) => m.UserModule),
      },
      {
        path: 'auth',
        loadChildren: () => import('./modules/authentication/auth.module').then((m) => m.AuthModule,),
      },
      {
        path: 'chinh-sach-bao-mat',
        loadChildren: () => import('./modules/privacy-policy/privacy-policy.module').then((m) => m.PrivacyPolicyModule),
      },
      {
        path: 'dieu-khoan',
        loadChildren: () => import('./modules/clause/clause.module').then((m) => m.ClauseModule),
      },
      {
        path: 'lien-he',
        loadChildren: () => import('./modules/contact/contact.module').then((m) => m.ContactModule),
      },
    ],
  },

  { path: '**', loadChildren: () => import('./modules/not-found/not-found.module').then((m) => m.NotFoundModule) },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule { }
