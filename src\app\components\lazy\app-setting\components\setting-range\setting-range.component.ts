import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, computed, signal } from '@angular/core';
import { SettingComponentProps } from '../../interfaces/setting-interfaces';

@Component({
  selector: 'app-setting-range',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="setting-range-container">
      <div class="setting-range-header">
        <label class="setting-range-label" [for]="setting.id">
          {{ setting.name }}
        </label>
        <div class="setting-range-value">
          {{ value }}{{ setting.unit || '' }}
        </div>
      </div>
      
      <div class="setting-range-wrapper">
        <input
          type="range"
          [id]="setting.id"
          [value]="value"
          [min]="setting.min || 0"
          [max]="setting.max || 100"
          [step]="setting.step || 1"
          [disabled]="disabled || readonly"
          (input)="onRangeChange($event)"
          class="setting-range-input"
        />
        <div class="setting-range-track">
          <div 
            class="setting-range-progress" 
            [style.width.%]="progressPercentage"
          ></div>
        </div>
      </div>

      <div class="setting-range-labels">
        <span class="setting-range-min">{{ setting.min || 0 }}{{ setting.unit || '' }}</span>
        <span class="setting-range-max">{{ setting.max || 100 }}{{ setting.unit || '' }}</span>
      </div>

    </div>
  `,
  styleUrls: ['./setting-range.component.scss']
})
export class SettingRangeComponent implements SettingComponentProps {
  @Input() setting!: any;
  @Input() value: number = 0;
  @Input() disabled: boolean = false;
  @Input() readonly: boolean = false;
  @Input() showDescription: boolean = true;
  @Input() compact: boolean = false;
  
  @Output() onChange = new EventEmitter<number>();
  @Output() onValidate = new EventEmitter<any>();

  get progressPercentage(): number {
    const min = this.setting.min || 0;
    const max = this.setting.max || 100;
    return ((this.value - min) / (max - min)) * 100;
  }

  onRangeChange(event: Event): void {
    const target = event.target as HTMLInputElement;
    const numValue = parseFloat(target.value);
    this.onChange.emit(numValue);
  }
}
