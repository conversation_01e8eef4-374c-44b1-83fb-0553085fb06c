import { Injectable } from '@angular/core';
import { Observable, of, delay, map, switchMap, timer } from 'rxjs';
import {
  SyncCredentials,
  SyncResult,
  SyncSettings,
  TrackedComic,
  SyncStage,
  ComicStatus,
  SyncError
} from '../models/sync-tracking.models';

@Injectable({
  providedIn: 'root'
})
export class SyncTrackingMockService {
  
  // Mock data
  private mockNettruyenComics: TrackedComic[] = [
    {
      id: 'nt-001',
      title: 'One Piece',
      url: 'one-piece',
      lastReadChapter: 'Chapter 1095',
      lastReadDate: new Date('2024-01-15'),
      totalChapters: 1095,
      status: ComicStatus.READING,
      thumbnail: '/assets/images/one-piece.jpg',
      source: 'nettruyen'
    },
    {
      id: 'nt-002',
      title: 'Naruto',
      url: 'naruto',
      lastReadChapter: 'Chapter 700',
      lastReadDate: new Date('2024-01-10'),
      totalChapters: 700,
      status: ComicStatus.COMPLETED,
      thumbnail: '/assets/images/naruto.jpg',
      source: 'nettruyen'
    },
    {
      id: 'nt-003',
      title: 'Attack on Titan',
      url: 'attack-on-titan',
      lastReadChapter: 'Chapter 139',
      lastReadDate: new Date('2024-01-05'),
      totalChapters: 139,
      status: ComicStatus.COMPLETED,
      thumbnail: '/assets/images/aot.jpg',
      source: 'nettruyen'
    }
  ];

  private mockTruyenqqComics: TrackedComic[] = [
    {
      id: 'tqq-001',
      title: 'One Piece',
      url: 'one-piece',
      lastReadChapter: 'Chapter 1090',
      lastReadDate: new Date('2024-01-12'),
      totalChapters: 1095,
      status: ComicStatus.READING,
      thumbnail: '/assets/images/one-piece.jpg',
      source: 'truyenqq'
    },
    {
      id: 'tqq-002',
      title: 'Dragon Ball',
      url: 'dragon-ball',
      lastReadChapter: 'Chapter 519',
      lastReadDate: new Date('2024-01-08'),
      totalChapters: 519,
      status: ComicStatus.COMPLETED,
      thumbnail: '/assets/images/dragon-ball.jpg',
      source: 'truyenqq'
    },
    {
      id: 'tqq-003',
      title: 'My Hero Academia',
      url: 'my-hero-academia',
      lastReadChapter: 'Chapter 405',
      lastReadDate: new Date('2024-01-14'),
      totalChapters: 405,
      status: ComicStatus.READING,
      thumbnail: '/assets/images/mha.jpg',
      source: 'truyenqq'
    }
  ];

  /**
   * Mock API: Test credentials
   */
  testCredentials(site: 'nettruyen' | 'truyenqq', credentials: any): Observable<boolean> {
    return timer(2000).pipe(
      map(() => {
        // Mock validation logic
        const { username, password } = credentials;
        return username.length >= 3 && password.length >= 6;
      })
    );
  }

  /**
   * Mock API: Fetch tracked comics from a source
   */
  fetchTrackedComics(source: 'nettruyen' | 'truyenqq'): Observable<TrackedComic[]> {
    return timer(3000).pipe(
      map(() => {
        return source === 'nettruyen' ? this.mockNettruyenComics : this.mockTruyenqqComics;
      })
    );
  }

  /**
   * Mock API: Start sync process
   */
  startSync(credentials: SyncCredentials, settings: SyncSettings): Observable<SyncResult> {
    return this.simulateSyncProcess().pipe(
      map(() => this.generateMockSyncResult())
    );
  }

  /**
   * Simulate sync process with progress updates
   */
  private simulateSyncProcess(): Observable<void> {
    const stages = [
      { stage: SyncStage.CONNECTING, duration: 2000, message: 'Connecting to websites...' },
      { stage: SyncStage.FETCHING_NETTRUYEN, duration: 3000, message: 'Fetching NetTruyen data...' },
      { stage: SyncStage.FETCHING_TRUYENQQ, duration: 3000, message: 'Fetching TruyenQQ data...' },
      { stage: SyncStage.COMPARING, duration: 2000, message: 'Comparing data...' },
      { stage: SyncStage.SYNCING, duration: 4000, message: 'Syncing changes...' },
      { stage: SyncStage.COMPLETED, duration: 500, message: 'Sync completed successfully!' }
    ];

    return new Observable(observer => {
      let currentStageIndex = 0;
      
      const processNextStage = () => {
        if (currentStageIndex >= stages.length) {
          observer.complete();
          return;
        }

        const currentStage = stages[currentStageIndex];
        const startTime = Date.now();
        
        // Simulate progress within each stage
        const progressInterval = setInterval(() => {
          const elapsed = Date.now() - startTime;
          const progress = Math.min(100, (elapsed / currentStage.duration) * 100);
          
          // This would typically update the progress subject in the real service
          // For demo purposes, we'll just log it
          console.log(`Stage: ${currentStage.stage}, Progress: ${progress}%`);
          
          if (elapsed >= currentStage.duration) {
            clearInterval(progressInterval);
            currentStageIndex++;
            setTimeout(processNextStage, 100);
          }
        }, 100);
      };

      processNextStage();
    });
  }

  /**
   * Generate mock sync result
   */
  private generateMockSyncResult(): SyncResult {
    const errors: SyncError[] = [
      {
        comicId: 'nt-004',
        comicTitle: 'Bleach',
        error: 'Chapter data not found',
        source: 'nettruyen'
      }
    ];

    return {
      success: true,
      totalComics: 5,
      syncedComics: 4,
      errors: errors,
      summary: {
        nettruyenComics: 3,
        truyenqqComics: 3,
        newComics: 2,
        updatedComics: 2,
        conflictComics: 1
      },
      duration: 14 // seconds
    };
  }

  /**
   * Mock API: Get sync settings
   */
  getSettings(): Observable<SyncSettings> {
    return of({
      autoResolveConflicts: false,
      preferredSource: 'latest' as 'nettruyen' | 'truyenqq' | 'latest',
      syncInterval: 24,
      enableNotifications: true,
      backupBeforeSync: true
    }).pipe(delay(500));
  }

  /**
   * Mock API: Save sync settings
   */
  saveSettings(settings: SyncSettings): Observable<void> {
    return of(void 0).pipe(delay(1000));
  }

  /**
   * Mock API: Get sync history
   */
  getSyncHistory(limit: number = 10): Observable<any[]> {
    const mockHistory = [
      {
        id: 'sync-001',
        timestamp: new Date('2024-01-15T10:30:00'),
        result: this.generateMockSyncResult(),
        settings: {
          autoResolveConflicts: false,
          preferredSource: 'latest',
          syncInterval: 24,
          enableNotifications: true,
          backupBeforeSync: true
        }
      }
    ];

    return of(mockHistory.slice(0, limit)).pipe(delay(800));
  }
}
