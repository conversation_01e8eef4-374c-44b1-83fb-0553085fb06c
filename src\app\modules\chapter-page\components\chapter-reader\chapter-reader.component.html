<!-- Reading Container -->
<section class="reading-container">
  <div #imageContainer id="image-container" class="reading-content">
    <!-- Scroll Navigation -->
    <div class="scroll-navigation" [ngClass]="containerClasses()">
      <button
        (click)="onScrollHorizontal(-1)"
        class="scroll-btn scroll-btn-prev"
        title="Trang trước"
      >
        <svg class="scroll-btn-icon" viewBox="0 0 24 24">
          <polyline points="15 18 9 12 15 6" />
        </svg>
        <span class="scroll-btn-text">Trước</span>
      </button>

      <button 
        (click)="onScrollHorizontal(1)" 
        class="scroll-btn scroll-btn-next" 
        title="Trang tiếp"
      >
        <span class="scroll-btn-text">Tiếp</span>
        <svg class="scroll-btn-icon" viewBox="0 0 24 24">
          <polyline points="9 18 15 12 9 6" />
        </svg>
      </button>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner">
          <svg class="loading-icon" viewBox="0 0 24 24">
            <circle class="loading-circle-bg" cx="12" cy="12" r="10" />
            <circle class="loading-circle-progress" cx="12" cy="12" r="10" />
          </svg>
        </div>
        <div class="loading-text">
          <h3 class="loading-title">Đang tải chương...</h3>
          <p class="loading-subtitle">Vui lòng đợi trong giây lát</p>
        </div>
      </div>

      <!-- Loading Skeleton -->
      <div class="loading-skeleton">
        <div 
          class="skeleton-page" 
          *ngFor="let page of skeletonPages(); trackBy: trackBySkeletonIndex"
        ></div>
      </div>
    </div>

    <!-- Chapter Images -->
    <ng-container *ngIf="isBrowser && !isLoading">
      <div
        *ngFor="let page of visibleImages(); let i = index; trackBy: trackByPageUrl"
        class="chapter-page-container"
        (dblclick)="onDoubleClick()"
        [style.width.px]="imageWidth()"
      >
        <img
          [loading]="getImageLoadingStrategy(i)"
          class="chapter-page-image"
          [alt]="getImageAlt(i)"
          [ngClass]="getImageClasses(i)"
          [src]="page.url"
          (error)="onImageError($event)"
          (load)="onImageLoad($event)"
        />
      </div>
    </ng-container>
  </div>
</section>
