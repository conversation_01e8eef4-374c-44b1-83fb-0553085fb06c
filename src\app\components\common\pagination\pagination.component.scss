// Modern Pagination Component - Comic Website Design
.pagination-container {
  @apply bg-white dark:bg-neutral-800 rounded-lg sm:rounded-lg border border-gray-200 dark:border-neutral-700 shadow-sm overflow-hidden mt-6 mb-2;
}

// Main Wrapper
.pagination-wrapper {
  @apply flex flex-col gap-3 px-3 py-2 sm:flex-row sm:items-center sm:justify-between sm:gap-4;
}

// Page Info Section
.pagination-info {
  @apply  items-center order-2 sm:order-1 hidden sm:flex;
}

.page-info-content {
  @apply flex items-center gap-2 px-2 py-1 sm:px-3 sm:py-2 bg-gray-50 dark:bg-neutral-700 rounded-lg;
}

.page-info-icon {
  @apply w-4 h-4 text-primary-100;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.page-info-text {
  @apply text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300;
}

.current-page-highlight {
  @apply font-bold text-primary-100;
}

.total-pages {
  @apply font-semibold text-gray-900 dark:text-white;
}

// Navigation Controls
.pagination-nav {
  @apply flex items-center gap-2 order-1 flex-wrap justify-center sm:order-2 sm:flex-nowrap sm:justify-start;
}

// Previous/Next Buttons
.pagination-prev,
.pagination-next {
  @apply flex items-center gap-2 px-2 py-2 sm:px-3 sm:py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:text-gray-600 disabled:hover:bg-transparent;
}

.pagination-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.pagination-text {
  @apply hidden sm:inline font-medium;
}

// Page Numbers
.pagination-pages {
  @apply flex items-center gap-0.5 sm:gap-1 flex-wrap justify-center sm:flex-nowrap sm:justify-start;
}

.pagination-page {
  @apply flex items-center justify-center px-1 text-xs min-w-8  h-8 sm:px-2 sm:text-xs md:min-w-10 md:h-10 md:px-3 md:py-2 md:text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-100 hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border-none cursor-pointer;

  &.pagination-page-active {
    @apply bg-primary-100 text-white hover:bg-primary-200 hover:text-white shadow-sm;
  }

  &.pagination-ellipsis {
    @apply cursor-pointer hover:bg-primary-100/10 dark:hover:bg-primary-100/20;
  }

  &:disabled {
    @apply cursor-not-allowed opacity-50;
  }
}

.page-number {
  @apply font-semibold;
}

.ellipsis-icon {
  @apply w-4 h-4 text-gray-400 dark:text-gray-500;
  fill: currentColor;
  stroke: none;
}

// Enhanced Search Section
.pagination-search {
  @apply fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm;
}

.search-container {
  @apply bg-white dark:bg-neutral-800 rounded-xl shadow-2xl border border-gray-200 dark:border-neutral-700 p-4 m-2 sm:p-6 sm:m-4 w-full max-w-md;
}

.search-header {
  @apply flex items-center gap-3 mb-4 pb-4 border-b border-gray-200 dark:border-neutral-700;
}

.search-icon {
  @apply w-5 h-5 text-primary-100;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.search-title {
  @apply text-lg font-bold text-gray-900 dark:text-white;
}

.search-form {
  @apply space-y-4;
}

.search-input-group {
  @apply space-y-2;
}

.search-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
}

.search-input {
  @apply w-full px-4 py-3 text-center text-lg font-semibold text-gray-900 dark:text-white bg-gray-50 dark:bg-neutral-700 border border-gray-300 dark:border-neutral-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-100 focus:border-transparent transition-all duration-200;
}

.search-range {
  @apply text-xs text-gray-500 dark:text-gray-400 text-center block;
}

.search-actions {
  @apply flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3;
}

.search-go-button {
  @apply w-full justify-center sm:flex-1 flex items-center gap-2 px-4 py-3 bg-primary-100 hover:bg-primary-200 text-white font-medium rounded-lg transition-all duration-200 border-none cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed;
}

.search-go-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.search-cancel-button {
  @apply w-full justify-center sm:w-auto flex items-center gap-2 px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-neutral-700 rounded-lg transition-all duration-200 border-none cursor-pointer;
}

.search-cancel-icon {
  @apply w-4 h-4;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Responsive Design - Now handled by Tailwind responsive utilities in the classes above

