import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef, inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformServer } from '@angular/common';
import { FormControl, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { ImageService } from '@services/image.service';
import { LevelService } from '@services/level.service';
import { ToastService, ToastType } from '@services/toast.service';
import { EyeIconComponent } from '@components/common/eye-icon/eye-icon.component';
import { first } from 'rxjs';

@Component({
  selector: 'app-user-header',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, FormsModule, EyeIconComponent],
  templateUrl: './user-header.component.html',
  styleUrl: './user-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class UserHeaderComponent implements OnInit {
  private accountService = inject(AccountService);
  private toast = inject(ToastService);
  private imageService = inject(ImageService);
  private levelService = inject(LevelService);
  private cd = inject(ChangeDetectorRef);
  private platformId = inject(PLATFORM_ID);

  user!: IUser;
  levelUser!: { percent: number; level: string; nextLevel: string };
  avatar = 'https://static.vecteezy.com/system/resources/previews/002/002/257/non_2x/beautiful-woman-avatar-character-icon-free-vector.jpg';
  maxim: string | null = '';
  
  // Form states
  isLoading = true;
  isEditingProfile = false;
  isEditingPassword = false;
  showPassword = false;
  showNewPassword = false;
  showRePassword = false;

  // Forms
  infoForm: FormGroup = new FormGroup({
    firstName: new FormControl('', Validators.required),
    lastName: new FormControl('', Validators.required),
    email: new FormControl('', [Validators.required, Validators.email]),
    dob: new FormControl('', Validators.required),
  });

  passwordForm: FormGroup = new FormGroup({
    oldPassword: new FormControl('', Validators.required),
    newPassword: new FormControl('', Validators.required),
    rePassword: new FormControl('', Validators.required),
  });

  ngOnInit() {
    if (isPlatformServer(this.platformId)) return;
    this.loadUserData();
  }

  private loadUserData() {
    this.accountService.GetRemoteUser().subscribe({
      next: (res: any) => {
        if (res.status) {
          this.user = res.data;
          this.avatar = this.user?.avatar || this.avatar;
          this.maxim = this.user.maxim || '';
          this.user.dob = this.transform(this.user.dob, 'YYYY-MM-DD');
          this.levelUser = this.levelService.getLevelUser(
            this.user.experience!,
            this.user.typeLevel!,
          );
          this.updateInfoForm();
        }
        this.isLoading = false;
        this.cd.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.cd.detectChanges();
      }
    });
  }

  private transform(dateString: string | undefined, FORMAT_DATE = 'DD/MM/YYYY'): string {
    const date = new Date(dateString!);
    if (isNaN(date.getTime())) {
      throw new Error("Invalid date string");
    }
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${year}-${month}-${day}`;
  }

  private updateInfoForm() {
    this.infoForm.setValue({
      firstName: this.user.firstName,
      lastName: this.user.lastName,
      email: this.user.email,
      dob: this.user.dob,
    });
  }

  onFileChange(event: any) {
    if (event.target.files && event.target.files.length) {
      const file = event.target.files[0];
      const avatar: FormData = new FormData();
      avatar.append('image', file, file.name);
      
      this.accountService.UpdateAvatar(avatar).pipe(first()).subscribe({
        next: (res: any) => {
          if (res.status == 200) {
            this.avatar = res.data;
            this.user.avatar = this.avatar;
            this.user.token = this.accountService.getAuthorizationToken();
            this.accountService.SaveUser(this.user);
            this.imageService.updateImageUrl(this.avatar);
            this.toast.show(ToastType.Success, res.message);
            this.cd.detectChanges();
          } else {
            this.toast.show(ToastType.Error, res.message);
          }
        }
      });
    }
  }

  updateTypeLevel(type: number) {
    this.accountService.UpdateTypeLevel(type).subscribe({
      next: (res: any) => {
        if (res.status == 200) {
          this.user.typeLevel = type;
          this.levelUser = this.levelService.getLevelUser(this.user.experience!, type);
          this.toast.show(ToastType.Success, res.message);
          this.cd.detectChanges();
        } else {
          this.toast.show(ToastType.Error, res.message);
        }
      }
    });
  }

  onUpdateInfo() {
    if (this.infoForm.valid && this.infoForm.dirty) {
      const userInfoToUpdate = { ...this.infoForm.value };
      userInfoToUpdate.dob = new Date(userInfoToUpdate.dob).toISOString();
      
      this.accountService.UpdateInfo(userInfoToUpdate).pipe(first()).subscribe({
        next: (res: any) => {
          if (res.status == 200) {
            this.user = res.data;
            this.user.dob = this.transform(this.user.dob, 'YYYY-MM-DD');
            this.infoForm.markAsPristine();
            this.isEditingProfile = false;
            this.toast.show(ToastType.Success, res.message);
            this.cd.detectChanges();
          } else {
            this.toast.show(ToastType.Error, res.message);
          }
        }
      });
    }
  }

  onUpdatePassword() {
    if (this.passwordForm.valid) {
      this.accountService.UpdatePassword(this.passwordForm.value).pipe(first()).subscribe({
        next: (res: any) => {
          if (res.status == 200) {
            this.passwordForm.reset();
            this.isEditingPassword = false;
            this.toast.show(ToastType.Success, res.message);
            this.cd.detectChanges();
          } else {
            this.toast.show(ToastType.Error, res.message);
          }
        }
      });
    }
  }

  onUpdateMaxim(maxim: string | null) {
    if (maxim === this.user.maxim) return;
    
    this.accountService.UpdateMaxim(maxim).pipe(first()).subscribe({
      next: (res: any) => {
        if (res.status == 200) {
          this.user.maxim = maxim!;
          this.toast.show(ToastType.Success, res.message);
        } else {
          this.toast.show(ToastType.Error, res.message);
        }
      }
    });
  }

  isControlInvalid(control: string, form: FormGroup): boolean {
    const ctrl = form.get(control);
    return ctrl ? (ctrl.invalid && (ctrl.dirty || ctrl.touched)) : false;
  }

  toggleEditProfile() {
    this.isEditingProfile = !this.isEditingProfile;
    if (!this.isEditingProfile) {
      this.infoForm.reset();
      this.updateInfoForm();
    }
  }

  toggleEditPassword() {
    this.isEditingPassword = !this.isEditingPassword;
    if (!this.isEditingPassword) {
      this.passwordForm.reset();
    }
  }
}
