using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicAPI.Models
{
    public class QuestTemplate
    {
        [Key, Column("id")]
        public int ID { get; set; }

        [Required, <PERSON><PERSON>eng<PERSON>(255), Column("title")]
        public string Title { get; set; } = string.Empty;

        [Column("description")]
        public string? Description { get; set; }

        [Required, <PERSON><PERSON>ength(50), Column("questtype")]
        public string QuestType { get; set; } = string.Empty;

        [Required, Column("target")]
        public int Target { get; set; }

        [Required, <PERSON><PERSON><PERSON>th(20), Column("difficulty")]
        public string Difficulty { get; set; } = string.Empty;

        [MaxLength(50), Column("icon")]
        public string? Icon { get; set; }

        [Column("isdaily")]
        public bool IsDaily { get; set; } = true;

        [Column("isweekly")]
        public bool IsWeekly { get; set; } = false;

        [Column("isactive")]
        public bool IsActive { get; set; } = true;

        [Column("createat")]
        public DateTime CreateAt { get; set; } = DateTime.UtcNow;

        [Column("updateat")]
        public DateTime UpdateAt { get; set; } = DateTime.UtcNow;

        // Embedded Reward Properties
        [Required, MaxLength(50), Column("rewardtype")]
        public string RewardType { get; set; } = "coins";

        [Required, Column("rewardamount")]
        public int RewardAmount { get; set; } = 0;

        [Column("rewarditem")]
        public int? RewardItem { get; set; }

        [MaxLength(255), Column("rewarddescription")]
        public string? RewardDescription { get; set; }

        // Navigation properties
        public virtual ICollection<UserQuest> UserQuests { get; set; } = new List<UserQuest>();
    }

    public enum QuestType
    {
        READ_chapters,
        read_comics,
        add_favorites,
        write_comments,
        rate_comics,
        share_comics,
        login_streak,
        discover_new,
        complete_series,
        weekly_reading
    }

    public enum QuestDifficulty
    {
        easy,
        medium,
        hard,
        legendary
    }
}
