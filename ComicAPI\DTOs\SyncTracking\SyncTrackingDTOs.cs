using System.ComponentModel.DataAnnotations;
using ComicApp.Models.SyncTracking;

namespace ComicAPI.DTOs.SyncTracking
{
    public class SyncCredentialsDTO
    {
        public int UserId { get; set; }
        
        [Required]
        public string username { get; set; } = string.Empty;

        [Required]
        public string password { get; set; } = string.Empty;

        [Required]
        public string sourceFrom { get; set; } = string.Empty; // "nettruyen" or "truyenqq"

        public string sourceTo { get; set; } = "local"; // Always sync to local database
    }


    public class StartSyncRequestDTO
    {
        [Required]
        public string Credentials { get; set; } = string.Empty; // Base64 encoded SyncCredentialsDTO

    }

    public class TestCredentialsRequestDTO
    {
        [Required]
        public string Site { get; set; } = string.Empty; // "nettruyen" or "truyenqq"

        [Required]
        public string Credentials { get; set; } = string.Empty; // Base64 encoded credentials
    }

    public class SyncProgressResponseDTO
    {
        public string SessionId { get; set; } = string.Empty;
        public string Stage { get; set; } = string.Empty;
        public int Progress { get; set; }
        public string Message { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int? TotalComics { get; set; }
        public int? ProcessedComics { get; set; }
        public List<string> Errors { get; set; } = new List<string>();

        public SyncProgressResponseDTO() { }

        public SyncProgressResponseDTO(SyncProgress progress)
        {
            SessionId = progress.SessionId;
            Stage = progress.Stage.ToString().ToLower();
            Progress = progress.Progress;
            Message = progress.Message;
            StartTime = progress.StartTime;
            EndTime = progress.EndTime;
            TotalComics = progress.TotalComics;
            ProcessedComics = progress.ProcessedComics;
            Errors = progress.Errors;
        }
    }

}
