@use '../../user.share.scss' as *;

.favorite-comics-container {
  @apply space-y-6;
}


// Loading State
.loading-container {
  @apply flex flex-col items-center justify-center py-16 space-y-4;
}

.loading-spinner {
  @apply w-8 h-8 border-2 border-neutral-300 border-t-sky-500 rounded-full animate-spin;
}

.loading-text {
  @apply text-neutral-600 dark:text-neutral-400;
}

// Content Card (for empty states)
.content-card {
  @apply bg-white dark:bg-neutral-800 rounded-2xl border border-neutral-200 dark:border-neutral-700 shadow-sm p-12;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  
  .dark & {
    background: rgba(31, 41, 55, 0.95);
  }
}


// Error State
.error-state {
  @apply text-center space-y-6;
}

.error-illustration {
  @apply flex justify-center;
}

.error-icon {
  @apply w-24 h-24 text-red-300 dark:text-red-600;
  fill: none;
  stroke: currentColor;
  stroke-width: 1;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.error-title {
  @apply text-xl font-semibold text-neutral-900 dark:text-white;
}

.error-description {
  @apply text-neutral-600 dark:text-neutral-400 max-w-md mx-auto leading-relaxed;
}

.retry-btn {
  @apply flex items-center gap-2 mx-auto px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &:focus {
    @apply outline-none ring-2 ring-red-500/50;
  }
}

// Empty State
.empty-state {
  @apply text-center space-y-6;
}

.empty-illustration {
  @apply flex justify-center;
}

.empty-icon {
  @apply w-24 h-24 text-neutral-300 dark:text-neutral-600;
  fill: none;
  stroke: currentColor;
  stroke-width: 1;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.empty-title {
  @apply text-xl font-semibold text-neutral-900 dark:text-white;
}

.empty-description {
  @apply text-neutral-600 dark:text-neutral-400 max-w-md mx-auto leading-relaxed;
}

.explore-btn {
  @apply flex items-center gap-2 mx-auto px-6 py-3 bg-lime-600 hover:bg-lime-700 text-white font-medium rounded-lg transition-colors duration-200 border-none cursor-pointer;

  &:focus {
    @apply outline-none ring-2 ring-lime-500/50;
  }
}

// Comics Content
.comics-content {
  @apply space-y-6;
}


// Comics Grid
.comics-grid {
  @apply grid gap-4 grid-cols-2 xs:grid-cols-3 sm:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6;
}

.comic-item {
  @apply transition-transform duration-200 hover:scale-105;
}


