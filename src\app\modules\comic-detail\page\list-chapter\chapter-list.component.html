<div class="chapter-panel">
  <span class="chapter-title">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="20"
      width="20"
      fill="currentColor"
      viewBox="0 0 512 512"
    >
      <path
        d="M64 144a48 48 0 1 0 0-96 48 48 0 1 0 0 96zM192 64c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zm0 160c-17.7 0-32 14.3-32 32s14.3 32 32 32H480c17.7 0 32-14.3 32-32s-14.3-32-32-32H192zM64 464a48 48 0 1 0 0-96 48 48 0 1 0 0 96zm48-208a48 48 0 1 0 -96 0 48 48 0 1 0 96 0z"
      />
    </svg>
    <p class="chapter-title-text"><PERSON><PERSON> s<PERSON>ch ch<PERSON></p>
  </span>
  <div class="chapter-controls">
    <div class="chapter-search-container">
      <div class="chapter-search-icon">
        <svg
          class="chapter-search-svg"
          aria-hidden="true"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 20 20"
        >
          <path
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
          />
        </svg>
      </div>
      <input
        type="search"
        maxlength="255"
        id="chapter-search"
        (input)="onSearchChapter($event)"
        class="chapter-search-input"
        placeholder="Tìm chương..."
        required
      />
    </div>
    <app-selection
      [options]="options"
      [value]="curOptioneValue"
      (onChange)="LoopScroll.GoToItem(($event * this.distance))"
      class="chapter-selection"
    >
    </app-selection>
  </div>
</div>
<div
  *ngIf="preLoadChapters.length === 0 && !isChapterLoading"
  class="chapter-not-found"
>
  Không tìm thấy chương ...
</div>

<div class="chapter-list-container overflow-hidden min-h-32"
  [ngClass]="{
    'h-80':isChapterLoading,
  }"
>
  <div class="chapter-loading" *ngIf="isChapterLoading">
    <app-spinner [sizeSpinner]="'40'"></app-spinner>
  </div>
  <app-loop-scroll
    #LoopScroll
    class="flex w-full"
    [allitems]="isChapterLoading ? [] : preLoadChapters"
    [gridSize]="gridSize"
    [nPreloadItem]="40"
    [itemHeight]="68"
    (onChange)="onScrollChange($event)"
  >
    <ng-template #ItemTemplate let-item="item;">
      <a routerLink="/truyen-tranh/{{ comic?.url }}/{{ item.id }}">
        <div class="chapter-item mr-3 mb-3">
          <div class="chapter-item-content">
            <p class="chapter-item-title">Chapter {{ item.slug }}</p>
          </div>
          <div class="chapter-item-date">
            <div class="chapter-item-date-text">
              {{ item.updateAt | dateAgo }}
            </div>
          </div>
        </div>
      </a>
    </ng-template>
  </app-loop-scroll>
</div>

