using ComicAPI.Services;

namespace ComicAPI.Services
{
    /// <summary>
    /// Background service for processing user activities every 10 minutes
    /// </summary>
    public class UserActivityBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<UserActivityBackgroundService> _logger;
        private readonly TimeSpan _processingInterval = TimeSpan.FromMinutes(10); // Process every 10 minutes
        private readonly TimeSpan _cleanupInterval = TimeSpan.FromHours(24); // Cleanup daily
        private DateTime _lastCleanup = DateTime.UtcNow;

        public UserActivityBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<UserActivityBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("User Activity Background Service started");

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ProcessUserActivities();
                    
                    // Check if cleanup is needed (daily)
                    if (DateTime.UtcNow - _lastCleanup > _cleanupInterval)
                    {
                        await CleanupOldActivities();
                        _lastCleanup = DateTime.UtcNow;
                    }

                    await Task.Delay(_processingInterval, stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred in User Activity Background Service");
                    // Continue running even if processing fails
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }

            // Final processing before shutdown
            try
            {
                await ProcessUserActivities();
                _logger.LogInformation("Final user activity processing completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during final user activity processing");
            }

            _logger.LogInformation("User Activity Background Service stopped");
        }

        private async Task ProcessUserActivities()
        {
            using var scope = _serviceProvider.CreateScope();
            var userActivityService = scope.ServiceProvider.GetRequiredService<UserActivityService>();

            try
            {
                await userActivityService.ProcessPendingActivitiesAsync();
                _logger.LogDebug("User activities processed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing user activities");
            }
        }

        private async Task CleanupOldActivities()
        {
            using var scope = _serviceProvider.CreateScope();
            var userActivityService = scope.ServiceProvider.GetRequiredService<IUserActivityService>();

            try
            {
                await userActivityService.CleanupOldActivitiesAsync();
                _logger.LogInformation("Old user activities cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user activities cleanup");
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("User Activity Background Service is stopping");
            await base.StopAsync(cancellationToken);
        }
    }
}
