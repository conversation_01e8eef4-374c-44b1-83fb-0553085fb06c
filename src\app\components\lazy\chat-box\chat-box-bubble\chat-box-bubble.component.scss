// Modern Chat Bubble Component - Comic Website Design

// Chat Bubble Container
.chat-bubble-container {
  @apply fixed bottom-6 right-6 z-50;
}

.chat-bubble-btn {
  @apply relative flex items-center justify-center w-14 h-14 bg-primary-100 hover:bg-primary-200 text-white rounded-full shadow-2xl transition-all duration-300 hover:scale-110 border-none cursor-pointer;
}

.chat-bubble-icon-wrapper {
  @apply relative;
}

.chat-bubble-icon {
  @apply w-7 h-7;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

// Notification Badge
.chat-bubble-badge {
  @apply absolute -top-2 -right-2 flex items-center justify-center min-w-6 h-6 bg-red-500 text-white rounded-full border-2 border-white;
}

.chat-bubble-badge-text {
  @apply text-xs font-bold px-1;
}

// Pulse Animation
// .chat-bubble-pulse {
//   @apply absolute inset-0 rounded-full bg-primary-100 animate-ping opacity-30;
// }

// Tooltip
.chat-bubble-tooltip {
  @apply absolute bottom-full right-0 mb-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none;
}

.chat-bubble-tooltip-text {
  @apply bg-neutral-900 text-white text-sm px-3 py-2 rounded-lg whitespace-nowrap;
}

.chat-bubble-tooltip-arrow {
  @apply absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-neutral-900;
}

// Responsive Design
@media (max-width: 640px) {
  .chat-bubble-container {
    @apply bottom-4 right-4;
  }


}

// Accessibility
.chat-bubble-btn:focus {
  @apply outline-none ring-4 ring-primary-100/50;
}

