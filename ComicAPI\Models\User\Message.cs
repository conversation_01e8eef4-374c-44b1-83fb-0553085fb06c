using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ComicAPI.Models
{
    public class Message
    {
        [Key, Column("id")]
        public Guid ID { get; set; } = Guid.NewGuid();

        [Required, <PERSON>umn("user_id")]
        public int UserId { get; set; }

        [Required, Column("content")]
        public string Content { get; set; } = string.Empty;

        [Required, <PERSON>umn("conversationid")]
        public Guid ConversationId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public User? User { get; set; }
        public Conversation? Conversation { get; set; }
    }
}
