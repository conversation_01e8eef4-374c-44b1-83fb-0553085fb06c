using ComicAPI.BackroundTask;
using ComicAPI.Data;
using ComicAPI.Models;
using ComicAPI.Services;
using Microsoft.EntityFrameworkCore;
using System.Collections.Concurrent;
using System.Text.Json;

namespace ComicAPI.Services
{
    /// <summary>
    /// Service for tracking user activities with high performance and background processing
    /// </summary>
    public class UserActivityService : IUserActivityService
    {
        private readonly ComicDbContext _dbContext;
        private readonly ILogger<UserActivityService> _logger;
        private readonly IBackgroundTaskQueue _taskQueue;
        
        // In-memory cache for recent activities (before batch insert)
        private static readonly ConcurrentQueue<UserActivity> _pendingActivities = new();
        private static readonly ConcurrentDictionary<int, DateTime> _userLastActivity = new();
        private static readonly ConcurrentDictionary<int, string> _userLastIp = new();

        public UserActivityService(
            ComicDbContext dbContext,
            ILogger<UserActivityService> logger,
            IBackgroundTaskQueue taskQueue)
        {
            _dbContext = dbContext;
            _logger = logger;
            _taskQueue = taskQueue;
        }

        public async Task TrackUserActivityAsync(int userId, string? ipAddress, string? userAgent, string? endpoint)
        {
            try
            {
                var now = DateTime.UtcNow;
                
                // Update in-memory cache immediately
                _userLastActivity.AddOrUpdate(userId, now, (key, oldValue) => now);
                if (!string.IsNullOrEmpty(ipAddress))
                {
                    _userLastIp.AddOrUpdate(userId, ipAddress, (key, oldValue) => ipAddress);
                }

                // Queue activity for batch processing
                var activity = new UserActivity
                {
                    UserId = userId,
                    ActivityType = "page_view",
                    Endpoint = endpoint,
                    IpAddress = ipAddress,
                    UserAgent = userAgent,
                    Timestamp = now,
                    SessionId = GenerateSessionId(userId, ipAddress)
                };

                _pendingActivities.Enqueue(activity);

                // Queue background task to update user's LastActivity in database
                _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
                {
                    await UpdateUserLastActivityInDatabase(userId, now, ipAddress);
                });

                _logger.LogDebug("User activity tracked for UserId: {UserId}, Endpoint: {Endpoint}", userId, endpoint);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking user activity for UserId: {UserId}", userId);
            }
        }

        public async Task TrackUserLoginAsync(int userId, string? ipAddress)
        {
            try
            {
                var now = DateTime.UtcNow;
                
                // Update in-memory cache
                _userLastActivity.AddOrUpdate(userId, now, (key, oldValue) => now);
                if (!string.IsNullOrEmpty(ipAddress))
                {
                    _userLastIp.AddOrUpdate(userId, ipAddress, (key, oldValue) => ipAddress);
                }

                // Queue login activity
                var loginActivity = new UserActivity
                {
                    UserId = userId,
                    ActivityType = "login",
                    IpAddress = ipAddress,
                    Timestamp = now,
                    SessionId = GenerateSessionId(userId, ipAddress)
                };

                _pendingActivities.Enqueue(loginActivity);

                // Update user login info in database immediately
                _taskQueue.QueueBackgroundWorkItem(async cancellationToken =>
                {
                    await UpdateUserLoginInDatabase(userId, now, ipAddress);
                });

                _logger.LogInformation("User login tracked for UserId: {UserId}, IP: {IpAddress}", userId, ipAddress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error tracking user login for UserId: {UserId}", userId);
            }
        }

        public async Task<DateTime?> GetLastActivityAsync(int userId)
        {
            // Check in-memory cache first
            if (_userLastActivity.TryGetValue(userId, out var cachedActivity))
            {
                return cachedActivity;
            }

            // Fallback to database
            try
            {
                var user = await _dbContext.Users
                    .AsNoTracking()
                    .Where(u => u.ID == userId)
                    .Select(u => u.LastActivity)
                    .FirstOrDefaultAsync();

                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last activity for UserId: {UserId}", userId);
                return null;
            }
        }

        public async Task<bool> IsUserOnlineAsync(int userId)
        {
            var lastActivity = await GetLastActivityAsync(userId);
            if (lastActivity == null) return false;

            var onlineThreshold = DateTime.UtcNow.AddMinutes(-5); // 5 minutes threshold
            return lastActivity > onlineThreshold;
        }

        public async Task<int> GetOnlineUsersCountAsync()
        {
            try
            {
                var onlineThreshold = DateTime.UtcNow.AddMinutes(-5);
                
                // Count from in-memory cache first
                var onlineFromCache = _userLastActivity.Count(kvp => kvp.Value > onlineThreshold);
                
                // If cache is empty or small, query database
                if (onlineFromCache < 10)
                {
                    var onlineFromDb = await _dbContext.Users
                        .AsNoTracking()
                        .Where(u => u.LastActivity != null && u.LastActivity > onlineThreshold)
                        .CountAsync();
                    
                    return Math.Max(onlineFromCache, onlineFromDb);
                }

                return onlineFromCache;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting online users count");
                return 0;
            }
        }

        public async Task<UserActivityStats> GetUserActivityStatsAsync(int userId)
        {
            try
            {
                var user = await _dbContext.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.ID == userId);

                if (user == null)
                {
                    return new UserActivityStats();
                }

                var last24Hours = DateTime.UtcNow.AddHours(-24);
                var last7Days = DateTime.UtcNow.AddDays(-7);

                var activities = await _dbContext.Set<UserActivity>()
                    .AsNoTracking()
                    .Where(a => a.UserId == userId && a.Timestamp > last7Days)
                    .OrderByDescending(a => a.Timestamp)
                    .Take(100)
                    .ToListAsync();

                var recentEndpoints = activities
                    .Where(a => !string.IsNullOrEmpty(a.Endpoint))
                    .Select(a => a.Endpoint!)
                    .Distinct()
                    .Take(10)
                    .ToList();

                return new UserActivityStats
                {
                    LastActivity = user.LastActivity,
                    LastLogin = user.LastLogin,
                    LastLoginIp = user.LastLoginIp,
                    ActivitiesLast24Hours = activities.Count(a => a.Timestamp > last24Hours),
                    ActivitiesLast7Days = activities.Count,
                    RecentEndpoints = recentEndpoints
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user activity stats for UserId: {UserId}", userId);
                return new UserActivityStats();
            }
        }

        public async Task CleanupOldActivitiesAsync()
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-30); // Keep 30 days of activities
                
                var deletedCount = await _dbContext.Set<UserActivity>()
                    .Where(a => a.Timestamp < cutoffDate)
                    .ExecuteDeleteAsync();

                if (deletedCount > 0)
                {
                    _logger.LogInformation("Cleaned up {Count} old user activities", deletedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up old activities");
            }
        }

        /// <summary>
        /// Batch insert pending activities (called by background service)
        /// </summary>
        public async Task ProcessPendingActivitiesAsync()
        {
            if (_pendingActivities.IsEmpty) return;

            var activitiesToProcess = new List<UserActivity>();
            
            // Dequeue all pending activities
            while (_pendingActivities.TryDequeue(out var activity))
            {
                activitiesToProcess.Add(activity);
                
                // Process in batches of 100
                if (activitiesToProcess.Count >= 100) break;
            }

            if (activitiesToProcess.Count == 0) return;

            try
            {
                await _dbContext.Set<UserActivity>().AddRangeAsync(activitiesToProcess);
                await _dbContext.SaveChangesAsync();
                
                _logger.LogDebug("Processed {Count} user activities", activitiesToProcess.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing pending activities");
                
                // Re-queue failed activities
                foreach (var activity in activitiesToProcess)
                {
                    _pendingActivities.Enqueue(activity);
                }
            }
        }

        private async Task UpdateUserLastActivityInDatabase(int userId, DateTime timestamp, string? ipAddress)
        {
            try
            {
                var user = await _dbContext.Users.FindAsync(userId);
                if (user != null)
                {
                    user.LastActivity = timestamp;
                    user.UpdateAt = timestamp;
                    
                    if (!string.IsNullOrEmpty(ipAddress))
                    {
                        user.LastLoginIp = ipAddress;
                    }

                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user last activity in database for UserId: {UserId}", userId);
            }
        }

        private async Task UpdateUserLoginInDatabase(int userId, DateTime timestamp, string? ipAddress)
        {
            try
            {
                var user = await _dbContext.Users.FindAsync(userId);
                if (user != null)
                {
                    user.LastLogin = timestamp;
                    user.LastActivity = timestamp;
                    user.UpdateAt = timestamp;
                    
                    if (!string.IsNullOrEmpty(ipAddress))
                    {
                        user.LastLoginIp = ipAddress;
                    }

                    await _dbContext.SaveChangesAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user login in database for UserId: {UserId}", userId);
            }
        }

        private static string GenerateSessionId(int userId, string? ipAddress)
        {
            var sessionData = $"{userId}_{ipAddress}_{DateTime.UtcNow:yyyyMMddHH}";
            return Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(sessionData))[..16];
        }
    }
}
