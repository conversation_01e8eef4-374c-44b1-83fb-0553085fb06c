<div *ngIf="comic; else empty" class="comic-card-v2" [attr.aria-label]="'Truyện tranh: ' + comic.title">
  <div class="comic-image-container">
    <a [routerLink]="comicRouterLink()" [attr.aria-label]="'Xem truyện ' + comic.title">
      <img
        loading="lazy"
        class="comic-image-v2"
        [src]="comic.coverImage"
        [alt]="'Ảnh bìa truyện ' + comic.title"
        onerror="this.src='/option2.png'"
      />
    </a>
  </div>

  <div class="comic-content">
    <div class="comic-header">
      <a [routerLink]="comicRouterLink()" class="comic-title-link">
        <h4 class="comic-title-v2">{{ comic.title }}</h4>
      </a>
      <p class="comic-update-time">{{ comic.updateAt | dateAgo }}</p>
    </div>

    <div class="genre-tags" role="list" aria-label="Thể loại truyện">
      <a
        *ngFor="let tag of displayGenres(); index as i; trackBy: trackByGenreId"
        class="genre-tag"
        [class.genre-tag-primary]="i === 0"
        [class.genre-tag-secondary]="i !== 0"
        [attr.aria-label]="'Thể loại: ' + tag.title"
        [attr.title]="tag.title"
        role="listitem"
      >
        <span class="genre-tag-text">{{ tag.title }}</span>
      </a>
    </div>

    <div class="comic-stats">
      <div class="stats-row">
        <div class="rating-stat" title="Đánh giá: {{ comic.rating }}/5">
          <svg class="star-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" viewBox="0 0 24 24">
            <path d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01z"></path>
          </svg>
          {{ comic.rating }}
        </div>

        <div class="bookmark-stat" title="Lượt bookmark: {{ comic.rating }}">
          <svg class="bookmark-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
          </svg>
          {{ comic.rating }}
        </div>

        <div class="view-stat" title="Lượt xem: {{ comic.viewCount | numeral }}">
          <svg class="eye-icon" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"></path>
            <circle cx="12" cy="12" r="3"></circle>
          </svg>
          {{ comic.viewCount | numeral }}
        </div>
      </div>

      <div class="status-container">
        <div *ngIf="isOngoing()" class="status-ongoing">
          <div class="status-indicator ongoing"></div>
          <div class="status-text">{{ statusText() }}</div>
        </div>

        <div *ngIf="isCompleted()" class="status-completed">
          <svg class="status-icon completed" xmlns="http://www.w3.org/2000/svg" height="6" width="6" viewBox="0 0 512 512">
            <path fill="#2debb2" d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/>
          </svg>
          <div class="status-text">{{ statusText() }}</div>
        </div>
      </div>
    </div>

    <div class="comic-description">
      <p class="description-text" [innerHTML]="comic.description | fillDescription : comic.id : comic.title : comic.url"></p>
    </div>
  </div>
</div>

<ng-template #empty>
  <div class="placeholder-container">
    <div class="placeholder-image-section">
      <svg class="placeholder-icon" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 18">
        <path d="M18 0H2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2Zm-5.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3Zm4.376 10.481A1 1 0 0 1 16 15H4a1 1 0 0 1-.895-1.447l3.5-7A1 1 0 0 1 7.468 6a.965.965 0 0 1 .9.5l2.775 4.757 1.546-1.887a1 1 0 0 1 1.618.1l2.541 4a1 1 0 0 1 .028 1.011Z"/>
      </svg>
    </div>
    <div class="placeholder-content-section">
      <div class="placeholder-image-area">
        <div class="placeholder-image-block"></div>
      </div>
      <div class="placeholder-text-area">
        <div class="placeholder-title"></div>
        <div class="placeholder-tags">
          <div class="placeholder-tag"></div>
          <div class="placeholder-tag"></div>
          <div class="placeholder-tag"></div>
        </div>
        <div class="placeholder-description"></div>
      </div>
    </div>
  </div>
</ng-template>