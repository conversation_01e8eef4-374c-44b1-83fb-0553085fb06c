<!-- Desktop Carousel -->
<div class="desktop-carousel">
  <!-- Navigation Controls -->
  <button
    *ngIf="hasCarouselItems"
    class="nav-left"
    (click)="next()"
    aria-label="Previous carousel item"
    (keydown.enter)="next()"
    (keydown.space)="next()"
  >
    <svg class="nav-icon" viewBox="0 0 512 512" fill="currentColor">
      <polygon points="352,128.4 319.7,96 160,256 160,256 160,256 319.7,416 352,383.6 224.7,256"/>
    </svg>
  </button>

  <!-- Comic Details Overlay -->
  <div
    *ngIf="shouldShowHoverDetails"
    [@formAnimation]
    class="details-overlay"
  >
    <div class="details-content">
      <header class="comic-header">
        <h4 class="comic-title">{{ hoverComic?.title }}</h4>
        <div class="status-container">
          @if (hoverComic?.status === 0) {
            <div class="status-ongoing"></div>
            <span><PERSON><PERSON> tiến hành</span>
          } @else {
            <svg class="status-completed" viewBox="0 0 512 512">
              <path fill="#2debb2" d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM369 209L241 337c-9.4 9.4-24.6 9.4-33.9 0l-64-64c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l47 47L335 175c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9z"/>
            </svg>
            <span>Đã hoàn thành</span>
          }
        </div>
      </header>

      <div class="genre-tags">
        <a
          *ngFor="let tag of hoverComic?.genres; index as i"
          class="genre-tag"
        >
          @if (i === 0) {
            <span class="tag-primary">{{ tag.title }}</span>
          } @else {
            <span class="tag-secondary">{{ tag.title }}</span>
          }
        </a>
      </div>

      <div class="comic-stats">
        <div class="stat-item">
          <svg class="stat-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
            <path d="m12 2 3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01z"/>
          </svg>
          {{ hoverComic?.rating }}
        </div>
        <div class="stat-item">
          <svg class="stat-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
            <path d="m19 21-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"/>
          </svg>
          {{ hoverComic?.rating }}
        </div>
        <div class="stat-item">
          <svg class="stat-icon" viewBox="0 0 24 24" stroke="currentColor" fill="none">
            <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
          {{ hoverComic?.viewCount | numeral }}
        </div>
      </div>

      <div class="comic-description">
        <p
          class="description-text"
          [innerHTML]="
            hoverComic?.description
              | fillDescription
                : hoverComic!.id
                : hoverComic!.title
                : hoverComic!.url
          "
        ></p>
      </div>
    </div>
  </div>

  <button
    *ngIf="hasCarouselItems"
    class="nav-right"
    (click)="prev()"
    aria-label="Next carousel item"
    (keydown.enter)="prev()"
    (keydown.space)="prev()"
  >
    <svg class="nav-icon" viewBox="0 0 512 512" fill="currentColor">
      <polygon points="160,128.4 192.3,96 352,256 352,256 352,256 192.3,416 160,383.6 287.3,256"/>
    </svg>
  </button>

  <!-- Carousel Items -->
  <div
    *ngFor="let comics of carouselItems; let i = index; trackBy: trackByCarouselIndex"
    #carouselItemPc
    class="carousel-item"
    [style.left]="getLeftPositon(i)"
  >
    <app-carousel-layout
      [comics]="comics"
      (comicHover)="OnComicHover($event)"
    />
  </div>

  <!-- Loading State -->
  <div
    *ngIf="!hasCarouselItems"
    class="loading-container"
    role="status"
    aria-label="Loading carousel content"
  >
    <app-spinner [sizeSpinner]="'40'" />
  </div>
</div>

<!-- Mobile Carousel -->
<div class="mobile-carousel">
  <button
    class="mobile-nav-left"
    (click)="next()"
    aria-label="Previous mobile carousel item"
    (keydown.enter)="next()"
    (keydown.space)="next()"
  >
    <svg class="mobile-nav-icon" viewBox="0 0 512 512" fill="currentColor">
      <polygon points="352,128.4 319.7,96 160,256 160,256 160,256 319.7,416 352,383.6 224.7,256"/>
    </svg>
  </button>

  <div
    #carouselItemMobile
    *ngFor="let comic of comicList; let i = index; trackBy: trackByComicId"
    [style.left]="getLeftPositon(i)"
    [style.width]="100 / grid + '%'"
    class="mobile-item"
  >
    <a
      [routerLink]="['/truyen-tranh', comic.url + '-' + comic.id]"
      [attr.aria-label]="'Read comic: ' + comic.title"
    >
      <img
        loading="lazy"
        class="mobile-image"
        [src]="comic.coverImage"
        [alt]="comic.title"
        onerror="this.src='/option2.png'"
      />
    </a>
  </div>

  <button
    class="mobile-nav-right"
    (click)="prev()"
    aria-label="Next mobile carousel item"
    (keydown.enter)="prev()"
    (keydown.space)="prev()"
  >
    <svg class="mobile-nav-icon" viewBox="0 0 512 512" fill="currentColor">
      <polygon points="160,128.4 192.3,96 352,256 352,256 352,256 192.3,416 160,383.6 287.3,256"/>
    </svg>
  </button>
</div>

<!-- Swiper Component -->
<app-swiper
  *ngIf="isBrowser"
  class="swiper-container"
  (nextChange)="SlideChange($event)"
/>
