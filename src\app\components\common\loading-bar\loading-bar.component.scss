// Optimized Loading Bar Component - Tailwind @apply with best practices

// Loading Bar Container
.loading-bar {
  @apply fixed top-0 left-0 right-0 z-50 w-full h-1;
  @apply bg-gray-200 dark:bg-gray-700;
}

// Loading Progress Bar
.loading-progress {
  @apply h-full bg-primary-100 transition-all duration-300 ease-out;
  @apply relative overflow-hidden;

  // Animated shimmer effect
  &::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
    @apply animate-shimmer;
  }
}

// Indeterminate loading animation
.loading-progress--indeterminate {
  @apply animate-loading-bar;
  transform-origin: 0% 50%;
}

// Animations
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes loading-bar {
  0% {
    transform: translateX(0) scaleX(0);
  }
  50% {
    transform: translateX(0) scaleX(0.4);
  }
  100% {
    transform: translateX(100%) scaleX(0.5);
  }
}

// Tailwind custom animations
.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

.animate-loading-bar {
  animation: loading-bar 0.75s infinite linear;
}