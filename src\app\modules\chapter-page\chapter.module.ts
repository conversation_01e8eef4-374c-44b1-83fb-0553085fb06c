import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ChapterSelectorComponent } from '@components/common/chapter-selector/chapter-selector.component';
import { ChapterImgsResolver } from 'src/app/resolvers/comic.resolver';
import { AdsModule } from 'src/app/shared/ads.module';
import { ChapterPageComponent } from './chapter-page.component';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { AnouncementComponent } from '@components/common/anouncement/anouncement.component';
import { FadeInDirective } from '@directives/fade-in.directive';
import { PopoverDirective } from '@directives/popover.directive';
import { CommentComponent } from '@components/common/comment/comment.component';
import { OptimizedImageComponent } from '@components/common/optimized-image/optimized-image.component';
import { SelectionComponent } from "../../components/common/selection/selection.component";
import { ClickOutsideDirective } from '@directives/click-outside.directive';

// Optimized sub-components
import { ChapterHeaderComponent } from './components/chapter-header/chapter-header.component';
import { ChapterControlsComponent } from './components/chapter-controls/chapter-controls.component';
import { ChapterReaderComponent } from './components/chapter-reader/chapter-reader.component';
import { ChapterNavigationComponent } from './components/chapter-navigation/chapter-navigation.component';

@NgModule({
  declarations: [
    ChapterPageComponent,
    ChapterHeaderComponent,
    ChapterControlsComponent,
    ChapterReaderComponent,
    ChapterNavigationComponent
  ],

  imports: [
    CommonModule,
    RouterModule.forChild([{
            path: '', component: ChapterPageComponent, resolve: { ChapterImgRes: ChapterImgsResolver },
        }]),
    RouterModule,
    ReactiveFormsModule,
    AdsModule,
    BreadcrumbComponent,
    AnouncementComponent,
    FadeInDirective,
    PopoverDirective,
    CommentComponent,
    ChapterSelectorComponent,
    OptimizedImageComponent,
    SelectionComponent,
    ClickOutsideDirective
],
})
export class ChapterModule { }
