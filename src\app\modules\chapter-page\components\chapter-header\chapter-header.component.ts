import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Chapter, ChapterPage, Comic } from '@schema';

@Component({
  selector: 'app-chapter-header',
  templateUrl: './chapter-header.component.html',
  styleUrl: './chapter-header.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class ChapterHeaderComponent extends OptimizedBaseComponent implements OnInit {
  // Input properties
  @Input() comic!: Comic;
  @Input() mainChapter!: ChapterPage;
  @Input() listChapterServerIds: number[] = [];
  @Input() selectedServerId = 0;
  @Input() showAllServers = false;

  // Output events
  @Output() reportError = new EventEmitter<void>();
  @Output() serverChange = new EventEmitter<{ serverId: number; index: number }>();
  @Output() toggleServers = new EventEmitter<void>();

  // Computed properties for performance
  private readonly _visibleServers = signal<number[]>([]);
  private readonly _hasMoreServers = signal(false);

  readonly visibleServers = computed(() => {
    const servers = this.listChapterServerIds || [];
    return this.showAllServers ? servers : servers.slice(0, 3);
  });

  readonly hasMoreServers = computed(() => {
    return (this.listChapterServerIds?.length || 0) > 3;
  });

  readonly comicTitleLink = computed(() => {
    return `/truyen-tranh/${this.comic?.url}-${this.comic?.id}`;
  });

  readonly breadcrumbLinks = computed(() => [
    { label: 'Trang chủ', url: '/' },
    {
      label: this.comic?.title || '',
      url: this.comicTitleLink()
    },
    { label: this.mainChapter?.title || '', url: '' }
  ]);

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.updateVisibleServers();
  }

  // Performance optimized methods
  onReportError(): void {
    this.reportError.emit();
  }

  onServerChange(serverId: number, index: number): void {
    this.serverChange.emit({ serverId, index });
  }

  onToggleServers(): void {
    this.toggleServers.emit();
  }

  // TrackBy functions for performance
  trackByServerId = (index: number, serverId: number): number => serverId;

  private updateVisibleServers(): void {
    this._visibleServers.set(this.visibleServers());
    this._hasMoreServers.set(this.hasMoreServers());
  }
}
