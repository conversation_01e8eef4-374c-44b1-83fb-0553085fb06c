import { ComicStatus, SortType } from "@schema";
import { IOption } from "src/app/dataSource/schema/IOption";


export interface IFilters {
  status: IOption[];
  sorts: IOption[];
}

export const advancedFiltersOptions: IFilters = {
  status: [
    {
      label: 'Tất Cả',
      value: ComicStatus.ALL,
    },
    {
      label: 'Đang tiến hành',
      value: ComicStatus.ONGOING,
    },
    {
      label: 'Hoàn thành',
      value: ComicStatus.COMPLETED,
    },
  ],

  sorts: [
    {
      label: 'M<PERSON>i cập nhật',
      value: SortType.LastUpdate,
    },
    {
      label: 'Top All',
      value: SortType.TopAll,
    },
    {
      label: 'Top ngày',
      value: SortType.TopDay,
    },
    {
      label: 'Top tuần',
      value: SortType.TopWeek,
    },
    {
      label: 'Top tháng',
      value: SortType.TopMonth,
    },
    {
      label: 'Chapter',
      value: SortType.Chapter,
    },
    {
      label: '<PERSON>',
      value: SortType.TopFollow,
    },
    {
      label: 'Bình luận',
      value: SortType.TopComment,
    },
    {
      label: 'Truyện mới',
      value: SortType.NewComic,
    },
  ],
};

export const rankFiltersOptions: IFilters = {
  status: [
    {
      label: 'Tất Cả',
      value: ComicStatus.ALL,
    },
    {
      label: 'Đang Ra',
      value: ComicStatus.ONGOING,
    },
    {
      label: 'Hoàn Thành',
      value: ComicStatus.COMPLETED,
    },
  ],

  sorts: [
    {
      label: 'Top All',
      value: SortType.TopAll,
    },
    {
      label: 'Top ngày',
      value: SortType.TopDay,
    },
    {
      label: 'Top tuần',
      value: SortType.TopWeek,
    },
    {
      label: 'Top tháng',
      value: SortType.TopMonth,
    },

    {
      label: 'Chapter',
      value: SortType.Chapter,
    },

    {
      label: 'Truyện mới',
      value: SortType.NewComic,
    },
    {
      label: 'Ngày cập nhật',
      value: SortType.LastUpdate,
    },
  ],
};
