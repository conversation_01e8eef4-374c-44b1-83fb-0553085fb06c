import {
  AfterViewChecked,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  HostListener,
  Inject,
  On<PERSON><PERSON>roy,
  OnInit,
  PLATFORM_ID,
  Renderer2,
  ViewChild,
} from '@angular/core';

import { ActivatedRoute, Router } from '@angular/router';

import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SettingCategory } from '@components/lazy/app-setting/interfaces/setting-interfaces';
import { ChatBoxBubbleComponent } from '@components/lazy/chat-box/chat-box-bubble/chat-box-bubble.component';
import { Chapter, ChapterPage, Comic, IServiceResponse, Page, UserExpType } from '@schema';
import { ComicService } from '@services/comic.service';
import { HistoryService } from '@services/history.service';
import { PopupService } from '@services/popup.service';
import { SeoService } from '@services/seo.service';
import { SettingService } from '@services/setting.service';
import { UrlService } from '@services/url.service';
import { ServiceUtility } from '@services/utils.service';
import { ChapterServer } from 'src/app/dataSource/schema/ChapterServer';
import { throttle } from 'src/app/utils';
export enum StickyState {
  NoneSticky = 0,
  Sticky = 1,
  StickyInvisible = 3,
}
@Component({
  selector: 'app-chapter',
  templateUrl: './chapter-page.component.html',
  styleUrl: './chapter-page.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChapterPageComponent extends OptimizedBaseComponent
  implements AfterViewInit, AfterViewChecked, OnDestroy, OnInit {


  readonly bannerImg: Page = { url: '/banner-manga.png', pageNumber: 0 };
  listImgs?: Page[];
  listChapterServerIds: number[] = [];
  comic!: Comic;
  mainChapter!: ChapterPage;
  @ViewChild('screenContainer') screenContainer!: ElementRef;
  @ViewChild('imageContainer') imageContainer!: ElementRef;
  @ViewChild('controlBar') controlBar!: ElementRef;
  @ViewChild('controlBarContainer') controlBarContainer!: ElementRef;
  @ViewChild('EndChapter')
  endChapterElement!: ElementRef;


  // Control Bar State Management
  stickyState: StickyState = StickyState.NoneSticky;
  private controlBarVisible = true;
  private animationTimeout?: number;

  // Scroll Management
  lastScrollTop = 0;
  isTracking = false;
  toolbarOriginalPosition = 0;
  showScrollToTop = false;
  statePosition = 0;
  // Loading & Server Management
  isImageLoading = true;
  selectedServerId: number = 0;
  showAllServers = false;

  scrollState = "down"

  chapterSetting = {
    isFullScreen: false,
    isNightMode: false,
    isAutoNextChapter: false,
    isVertical: true,
    preloadPages: 5,
    fixedToolbar: false,
    toolbarStyle: 'classic',
  };

  zoomData = {
    minZoomLevel: 50,
    maxZoomLevel: 150,
    zoomValue: 100,
    defaultZoomLevel: 100,
    isZoomIn: false,
  };

  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private historyService: HistoryService,
    private seoService: SeoService,
    private renderer: Renderer2,
    protected override cd: ChangeDetectorRef,
    private popupService: PopupService,
    private urlService: UrlService,
    private settingService: SettingService,
    @Inject(PLATFORM_ID) protected override platformId: object,
  ) {
    super(cd, platformId);
  }
  ngOnInit(): void {
    this.runInBrowser(() => {
      ChatBoxBubbleComponent.Instance?.SetVisible(false);
      this.loadSetting();
    })
    this.loadChapterPage();
  }


  ngAfterViewInit(): void {
    this.runInBrowser(() => {
      const rect = this.controlBarContainer.nativeElement.getBoundingClientRect();
      this.toolbarOriginalPosition = rect.top + window.scrollY;
    })
  }
  ngAfterViewChecked(): void {

  }
  override ngOnDestroy(): void {
    // Clear animation timeout to prevent memory leaks
    if (this.animationTimeout) {
      clearTimeout(this.animationTimeout);
      this.animationTimeout = undefined;
    }

    ChatBoxBubbleComponent.Instance?.SetVisible(true);
  }

  getImageWidth() {
    return this.zoomData.zoomValue + '%'
  }
  getImageLeft() {
    return (100 - this.zoomData.zoomValue) * 0.5 + '%';
  }
  verifyZoom(value: number) {
    if (value <= 0) return 1;
    value = Math.min(value, this.zoomData.maxZoomLevel);
    value = Math.max(value, this.zoomData.minZoomLevel);
    return value;
  }

  loadSetting() {
    this.chapterSetting.isFullScreen = this.settingService.getSettingValue('doubleClickToFullscreen') ?? false;
    this.chapterSetting.isNightMode = this.settingService.getSettingValue('nightMode') ?? false;
    this.chapterSetting.isAutoNextChapter = this.settingService.getSettingValue('autoNextChapter') ?? false;
    this.chapterSetting.isVertical = this.settingService.getSettingValue('verticalReading') ?? true;
    this.chapterSetting.preloadPages = this.settingService.getSettingValue('preloadPages') ?? 3;
    this.zoomData.zoomValue = this.verifyZoom(this.settingService.getSettingValue('zoom-reading') ?? 100);
    this.chapterSetting.fixedToolbar = this.settingService.getSettingValue('fixedToolbar') ?? false;
    this.chapterSetting.toolbarStyle = this.settingService.getSettingValue('styleToolbar') ?? 'classic';


    this.settingService.settingChanges$.subscribe((event) => {
      switch (event.settingId) {
        case 'doubleClickToFullscreen':
          this.chapterSetting.isFullScreen = event.newValue;
          break;
        case 'nightMode':
          this.chapterSetting.isNightMode = event.newValue;
          break;
        case 'autoNextChapter':
          this.chapterSetting.isAutoNextChapter = event.newValue;
          break;
        case 'verticalReading':
          this.chapterSetting.isVertical = event.newValue;
          this.changeDirectionReading(event.newValue);
          break;
        case 'zoom-reading':
          this.zoomData.zoomValue = event.newValue;
          break;
        case 'preloadPages':
          this.chapterSetting.preloadPages = event.newValue;
          break;
        case 'fixedToolbar':
          this.chapterSetting.fixedToolbar = event.newValue;
          break;
        case 'styleToolbar':
          this.chapterSetting.toolbarStyle = event.newValue;
          break;
      }

      // console.log(event);
    });
  }



  loadChapterPage() {
    this.isImageLoading = true;
    this.route.data.subscribe(({ ChapterImgRes }) => {
      // Initialize toolbar position if not set
      const res = ChapterImgRes as IServiceResponse<ChapterPage>;
      if (res === null || res.data === null) {
        this.router.navigate(['/']);
        return;
      }
      const data = structuredClone(res.data)!;
      this.listChapterServerIds = [0, ...data.chapterServerIds];
      this.isImageLoading = false;
      this.comic = data.comic;
      this.mainChapter = data;
      this.listImgs = [this.bannerImg, ...this.mainChapter.pages];
      this.selectedServerId = 0;
      this.runInBrowser(() => {
        this.historyService.SaveHistory(this.comic, {
          id: this.mainChapter.id,
          title: this.mainChapter.title,
          slug: this.mainChapter.slug,
          updateAt: this.mainChapter.updateAt,
          viewCount: this.mainChapter.viewCount,
        });

        this.comicService
          .getChapters(this.comic.id)
          .subscribe((res: IServiceResponse<Chapter[]>) => {
            this.comic.chapters = res.data || [];
            this.safeMarkForCheck();
          });
      })

      this.SetupSeo();
      this.isTracking = false;
      this.safeMarkForCheck();
    });
  }



  changeServer(serverId: number, index: number) {
    this.selectedServerId = index;
    if (this.selectedServerId == 0) {
      this.listImgs = [this.bannerImg, ...this.mainChapter.pages];
      this.safeMarkForCheck();

      return
    }

    if (!serverId) {
      return;
    }

    this.comicService
      .getChapterServer(serverId)
      .subscribe((res: IServiceResponse<ChapterServer>) => {
        const pagesImages = res.data?.images ?? [];
        this.listImgs = [this.bannerImg, ...pagesImages.map((img) => {
          return {
            url: img,
            pageNumber: 0,
          };
        })];
        this.safeMarkForCheck();

      });
  }
  showMoreServer() {
    this.showAllServers = !this.showAllServers;
  }
  openSetting() {
    this.popupService.showSetting(SettingCategory.READING);
  }


  onChangeChapter(chapterId: number) {
    this.isImageLoading = true;
    if (this.chapterSetting.isFullScreen) {
      this.screenContainer.nativeElement.scrollTo({ top: 0 });

    }
    this.router.navigate(['truyen-tranh', this.comic.url, chapterId]);
  }


  zoomImage(zoomIn: boolean): void {

    if (zoomIn) {
      this.zoomData.zoomValue = Math.min(
        this.zoomData.zoomValue + 10,
        this.zoomData.maxZoomLevel
      );
    } else {
      this.zoomData.zoomValue = Math.max(
        this.zoomData.zoomValue - 10,
        this.zoomData.minZoomLevel
      );
    }
    if (this.zoomData.zoomValue <= this.zoomData.minZoomLevel) {
      this.zoomData.isZoomIn = false;
      return;
    }
    if (this.zoomData.zoomValue >= this.zoomData.maxZoomLevel) {
      this.zoomData.isZoomIn = true;
      return;
    }
    this.settingService.setSettingValue('zoom-reading', this.zoomData.zoomValue);
  }

  resetView(): void {
    this.zoomData.isZoomIn = false;
    this.zoomData.zoomValue = this.zoomData.defaultZoomLevel;
    this.settingService.setSettingValue('zoom-reading', this.zoomData.zoomValue);
  }

  getZoomPercentage(): number {
    return Math.round((this.zoomData.zoomValue));
  }

  @HostListener('document:keydown.arrowleft', ['$event'])
  onNextChapter(): void {
    this.navigateChapter(true);
  }
  @HostListener('document:keydown.arrowright', ['$event'])
  onPreviousChapter(): void {
    this.navigateChapter(false);
  }

  navigateChapter = throttle((isNext: boolean): void => {
    if (this.isImageLoading) {
      return;
    }

    const currentChapterIndex = this.comic.chapters!.findIndex(
      (chapter) => chapter.id === this.mainChapter.id
    );
    const targetChapterIndex = isNext
      ? currentChapterIndex + 1
      : currentChapterIndex - 1;
    // console.log(targetChapterIndex, this.comic.chapters!.length);

    if (
      targetChapterIndex >= 0 &&
      targetChapterIndex < this.comic.chapters!.length
    ) {
      const targetChapter = this.comic.chapters![targetChapterIndex];
      this.onChangeChapter(targetChapter.id);
    }
  }, 1000);

  @HostListener('document:fullscreenchange', ['$event'])
  onFullscreenChange(event: Event) {
    this.chapterSetting.isFullScreen = document.fullscreenElement != null
    this.safeMarkForCheck();
    this.stickyState = StickyState.NoneSticky;
    this.applyStickyState(StickyState.NoneSticky);

    const rect = this.controlBarContainer.nativeElement.getBoundingClientRect();
    console.log(rect);
    
    if (this.chapterSetting.isFullScreen) {
      this.toolbarOriginalPosition = rect.top + this.screenContainer.nativeElement.scrollTop;
    }
    else {
      this.toolbarOriginalPosition = rect.top + window.scrollY;
    }
    
  }

  toggleFullscreen(): void {
    const elem = this.screenContainer.nativeElement;
    if (!this.chapterSetting.isFullScreen) {
      if (elem.requestFullscreen) {
        elem.requestFullscreen();
      } else if (elem.mozRequestFullScreen) {
        // Firefox
        elem.mozRequestFullScreen();
      } else if (elem.webkitRequestFullscreen) {
        // Chrome, Safari, and Opera
        elem.webkitRequestFullscreen();
      } else if (elem.msRequestFullscreen) {
        // IE/Edge
        elem.msRequestFullscreen();
      }

    }
    else {
      document.exitFullscreen();
    }
  }

  scrollToTop(event: Event): void {
    event.preventDefault();
    if (this.chapterSetting.isFullScreen) {
      this.screenContainer.nativeElement.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  /**
   * Handle scroll events from fullscreen container
   */
  handleFullScreenScroll($event: Event) {
    const target = $event.target as HTMLElement;
    this.handleScroll(target);
  }

  /**
   * Handle scroll events from window
   */
  @HostListener('window:scroll', ['$event'])
  handleWindowScroll(_event: any) {
    this.handleScroll();
  }

  /**
   * Unified scroll handler that works for both window and fullscreen modes
   * @param scrollContainer - Optional scroll container element (for fullscreen mode)
   */
  handleScroll(scrollContainer?: HTMLElement) {
    const scrollData = this.getScrollData(scrollContainer);

    // Auto next chapter functionality
    if (this.shouldAutoNavigateToNextChapter(scrollData)) {
      this.navigateChapter(false);
      return;
    }

    // Update control bar sticky state
    this.updateStickerState(scrollData);

    // Track user engagement (view count and experience)
    this.trackUserEngagement(scrollData.scrollTop);

    // Update last scroll position for direction detection
    this.lastScrollTop = scrollData.scrollTop <= 0 ? 0 : scrollData.scrollTop;
  }

  /**
   * Get scroll data from appropriate container (window or fullscreen element)
   */
  private getScrollData(scrollContainer?: HTMLElement) {
    // Check if we're in fullscreen mode and have a scroll container
    const isFullScreenMode = this.chapterSetting.isFullScreen && scrollContainer;

    if (isFullScreenMode) {
      // Fullscreen mode - use container scroll data
      return {
        scrollTop: scrollContainer!.scrollTop,
        scrollHeight: scrollContainer!.scrollHeight,
        clientHeight: scrollContainer!.clientHeight,
        isFullScreen: true
      };
    } else {
      // Normal mode - use window scroll data
      return {
        scrollTop: window.scrollY || document.documentElement.scrollTop || document.body.scrollTop || 0,
        scrollHeight: document.documentElement.scrollHeight,
        clientHeight: window.innerHeight,
        isFullScreen: false
      };
    }
  }

  /**
   * Check if should auto navigate to next chapter
   */
  private shouldAutoNavigateToNextChapter(scrollData: any): boolean {
    const isAtBottom = scrollData.scrollTop + scrollData.clientHeight >= scrollData.scrollHeight - 1;
    return this.chapterSetting.isAutoNextChapter &&
      this.chapterSetting.isVertical &&
      isAtBottom;
  }

  /**
   * Track user engagement for view count and experience points
   */
  private trackUserEngagement(scrollOffset: number) {
    if (this.isTracking) return;

    if (!this.isTracking && scrollOffset > 2000) {
      this.isTracking = true;
      this.comicService
        .updateViewAndExp(
          this.comic.id,
          this.mainChapter.id,
          UserExpType.Chapter
        )
        .subscribe(() => { });
    }
  }

  // Performance optimization methods
  trackByPageId = (index: number, page: Page): any => {
    return page?.url ?? index;
  };

  /**
   * Enhanced sticky state management with cleaner logic
   * @param scrollData - Scroll data from either window or fullscreen container
   */
  updateStickerState(scrollData: any) {
    const previousState = this.stickyState;

    // Determine new sticky state based on scroll position
    const newState = this.calculateStickyState(scrollData);

    // Only apply changes if state has changed
    if (previousState !== newState) {
      this.stickyState = newState;
      this.applyStickyState(newState);
    }
  }

  /**
   * Calculate the appropriate sticky state based on scroll position
   * @param scrollData - Scroll data containing position and dimensions
   */
  private calculateStickyState(scrollData: any): StickyState {
    const { scrollTop, clientHeight, isFullScreen } = scrollData;

    // Calculate end chapter position based on scroll mode
    const endChapterTop = isFullScreen
      ? this.getFullscreenEndChapterPosition()
      : this.endChapterElement.nativeElement.offsetTop;

    const isEndChapter = scrollTop + clientHeight > endChapterTop;
    const isScrolledPastToolbar = scrollTop > this.toolbarOriginalPosition;
    ;
    const preState = this.scrollState;
    this.scrollState = scrollTop < this.lastScrollTop ? "up" : "down";

    if (preState !== this.scrollState) {
      this.statePosition = scrollTop;
    }

    if (!isScrolledPastToolbar) {
      return StickyState.NoneSticky;
    }

    // End of chapter - stick to bottom
    if (this.chapterSetting.fixedToolbar || isEndChapter) {
      return StickyState.Sticky;
    }

    if (this.scrollState === "up" && this.statePosition - scrollTop > 50) {
      return StickyState.Sticky;
    }

    // Scrolling down for significant distance - hide toolbar
    if (this.scrollState === "down" && scrollTop - this.statePosition > 200) {
      return StickyState.StickyInvisible;
    }

    // Default to current state if no change needed
    return this.stickyState;
  }

  /**
   * Get end chapter position for fullscreen mode
   */
  private getFullscreenEndChapterPosition(): number {
    if (!this.endChapterElement?.nativeElement) return 0;

    // In fullscreen mode, calculate relative position within the scroll container
    const endChapterRect = this.endChapterElement.nativeElement.getBoundingClientRect();
    const containerRect = this.screenContainer.nativeElement.getBoundingClientRect();

    return endChapterRect.top - containerRect.top + this.screenContainer.nativeElement.scrollTop;
  }

  /**
   * Get toolbar position based on scroll mode (window vs fullscreen)
   */


  /**
   * Apply the sticky state to the control bar
   */
  private applyStickyState(state: StickyState) {
    switch (state) {
      case StickyState.Sticky:
        this.stickToTop();
        break;
      case StickyState.StickyInvisible:
        this.stickToInvisible();
        break;
      case StickyState.NoneSticky:
        this.cancelSticky();
        break;
    }
  }
  /**
   * Cancel sticky positioning and return to normal state
   */
  cancelSticky() {
    const element = this.controlBar.nativeElement;
    this.showScrollToTop = false;
    element.classList.remove('sticky-top', 'sticky-invisible');
  }

  /**
   * Stick control bar to top with enhanced animation
   */
  stickToTop() {
    const element = this.controlBar.nativeElement;
    // Apply sticky top positioning
    element.classList.remove('sticky-invisible');
    element.classList.add('sticky-top');
    this.showScrollToTop = true;

  }

  stickToInvisible() {
    const element = this.controlBar.nativeElement;
    // Apply invisible state
    element.classList.remove('sticky-top');
    element.classList.add('sticky-invisible');
    this.showScrollToTop = false;
  }


  changeDirectionReading(stage: boolean) {
    this.chapterSetting.isVertical = stage;
    this.addMouseWheelEvent();
    const styles = this.chapterSetting.isVertical
      ? {
        'scroll-snap-align': 'start',
        flex: '0 0 auto',
        display: 'flex',
        'flex-direction': 'column',
        'overflow-y': 'auto',
        'overflow-x': 'hidden',
      }
      : {
        'margin-top': '30px',
        'min-width': '30rem',
        'scroll-snap-align': 'start',
        display: 'flex',
        'flex-direction': 'row',
        overflow: 'hidden',
        'overflow-x': 'auto',
        'overflow-y': 'hidden',
      };

    for (const [key, value] of Object.entries(styles)) {
      this.renderer.setStyle(this.imageContainer.nativeElement, key, value);
    }
  }

  enableNightLight(stage: boolean) {
    this.chapterSetting.isNightMode = stage;
  }

  addMouseWheelEvent() {
    const container = this.imageContainer.nativeElement;
    container.removeEventListener('wheel', this.handleWheelEvent);
    container.addEventListener('wheel', this.handleWheelEvent);
  }

  scrollHorizontal(direction: number) {
    const container = this.imageContainer.nativeElement;
    const scrollAmount = direction * 500;
    container.scrollBy({
      left: scrollAmount,
      behavior: 'smooth',
    });
    if (this.chapterSetting.isAutoNextChapter) {
      this.checkScrollEnd();
    }
  }

  handleWheelEvent = (event: WheelEvent) => {
    const container = this.imageContainer.nativeElement;
    const scrollGap = 2.5;
    if (!this.chapterSetting.isVertical) {
      container.scrollBy({
        left: event.deltaY * scrollGap,
        behavior: 'smooth',
      });
      if (this.chapterSetting.isAutoNextChapter) {
        this.checkScrollEnd();
      }
      event.preventDefault();
    }
  };

  checkScrollEnd() {
    const container = this.imageContainer.nativeElement;

    if (this.chapterSetting.isVertical) {
      if (
        container.scrollTop + container.clientHeight >=
        container.scrollHeight
      ) {
        this.navigateChapter(true);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    } else {
      if (
        container.scrollLeft + container.clientWidth >=
        container.scrollWidth
      ) {
        this.navigateChapter(false);
        container.scrollTop = 0;
        container.scrollLeft = 0;
      }
    }
  }
  SetupSeo() {
    const chapterName = this.mainChapter.title || `Chương ${this.mainChapter.slug}`;
    const title = `${this.comic.title} ${chapterName} - Đọc Online Miễn Phí`;
    const description = this.generateChapterSEODescription();
    const url = `${this.urlService.BASE_URL}/truyen-tranh/${this.comic.url}/${this.mainChapter.id}`;
    const keywords = this.generateChapterKeywords();

    const seoData = {
      title,
      description,
      keywords,
      type: 'article' as const,
      url,
      image: this.comic.coverImage || `${this.urlService.BASE_URL}/assets/images/og-default.jpg`,
      author: this.comic.author || 'Đang cập nhật',
      publishedTime: this.mainChapter.updateAt,
      modifiedTime: this.mainChapter.updateAt,
      section: 'Chương truyện',
      tags: this.comic.genres?.map((g) => g.title) || [],
      siteName: 'Mê Truyện Mới',
      canonical: url,
      twitterCard: 'summary_large_image' as const
    };

    this.seoService.setSEOData(seoData);

    // Add chapter structured data
    const chapterSchema = this.seoService.generateChapterSchema(this.comic, this.mainChapter);

    // Add breadcrumb structured data
    const breadcrumbSchema = this.seoService.generateBreadcrumbSchema([
      { name: 'Trang chủ', url: '/' },
      { name: 'Truyện tranh', url: '/truyen-tranh' },
      { name: this.comic.title, url: `/truyen-tranh/${this.comic.url}-${this.comic.id}` },
      { name: chapterName, url: `/truyen-tranh/${this.comic.url}/${this.mainChapter.id}` }
    ]);

    // Add reading progress structured data
    const readingProgressSchema = this.generateReadingProgressSchema();

    // Combine all schemas
    const combinedSchema = [chapterSchema, breadcrumbSchema, readingProgressSchema];
    this.seoService.addStructuredData(combinedSchema);
  }

  private generateChapterSEODescription(): string {
    const chapterName = this.mainChapter.title || `Chương ${this.mainChapter.slug}`;
    const baseDescription = `Đọc ${this.comic.title} ${chapterName} online miễn phí tại Mê Truyện Mới.`;

    const genres = this.comic.genres?.map((g) => g.title).join(', ') || '';
    const totalChapters = this.comic.chapters?.length || 0;

    return `${baseDescription} Thể loại: ${genres}. Tổng ${totalChapters} chương. Cập nhật nhanh, chất lượng HD.`;
  }

  private generateChapterKeywords(): string {
    const chapterName = this.mainChapter.title || `chương ${this.mainChapter.slug}`;
    const baseKeywords = [
      `${this.comic.title} ${chapterName}`,
      `đọc ${this.comic.title} ${chapterName}`,
      `${this.comic.title} ${chapterName} online`,
      `${this.comic.title} ${chapterName} miễn phí`,
      this.comic.title,
      `truyện tranh ${this.comic.title}`
    ];

    const additionalKeywords = [
      'đọc truyện online',
      'truyện tranh miễn phí',
      'manga việt nam',
      'manhwa hay',
      'chương mới nhất'
    ];

    return [...baseKeywords, ...additionalKeywords].join(', ');
  }

  private generateReadingProgressSchema(): any {
    const currentChapterIndex = this.comic.chapters?.findIndex(
      chapter => chapter.id === this.mainChapter.id
    ) || 0;
    const totalChapters = this.comic.chapters?.length || 1;
    const progressPercentage = Math.round(((currentChapterIndex + 1) / totalChapters) * 100);

    return {
      '@context': 'https://schema.org',
      '@type': 'ReadAction',
      'object': {
        '@type': 'Book',
        'name': this.comic.title,
        'url': `${this.urlService.BASE_URL}/truyen-tranh/${this.comic.url}-${this.comic.id}`
      },
      'actionStatus': 'ActiveActionStatus',
      'startTime': new Date().toISOString(),
      'location': {
        '@type': 'Place',
        'name': 'Mê Truyện Mới',
        'url': this.urlService.BASE_URL
      },
      'result': {
        '@type': 'Chapter',
        'name': this.mainChapter.title || `Chương ${this.mainChapter.slug}`,
        'position': currentChapterIndex + 1,
        'isPartOf': {
          '@type': 'Book',
          'name': this.comic.title
        }
      },
      'additionalProperty': [
        {
          '@type': 'PropertyValue',
          'name': 'readingProgress',
          'value': `${progressPercentage}%`
        },
        {
          '@type': 'PropertyValue',
          'name': 'totalChapters',
          'value': totalChapters
        }
      ]
    };
  }

  reportError() {
    this.popupService.showReportComic({
      comicID: this.comic.id,
      chapterID: this.mainChapter.id,
    });
  }




  onLoad($event: Event) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onload = null;
  }
  onError($event: ErrorEvent) {
    const imageEle = $event.target as HTMLImageElement;
    imageEle.onerror = null;
    imageEle.classList.add('hidden');
  }
}
