// Item System Interfaces
export interface ItemTemplate {
  id: number;
  name: string;
  description?: string;
  icon?: string;
  category: ItemCategory;
  rarity: ItemRarity;
  isActive: boolean;
  createAt?: Date;
  updateAt?: Date;
}

export interface UserInventoryItem {
  id: number;
  userId: number;
  itemTemplateId: number;
  quantity: number;
  obtainedAt: Date;
  expiresAt?: Date;
  metadata?: string;
  itemTemplate: ItemTemplate;
}

export interface UserEquippedItem {
  userId: number;
  itemTemplateId: number;
  slotType: EquipmentSlotType;
  equippedAt: Date;
  itemTemplate: ItemTemplate;
}

export interface InventoryPage {
  items: UserInventoryItem[];
  totalItems: number;
  currentPage: number;
  totalPages: number;
}

export interface ItemOperationResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Item Operation Requests
export interface UseItemRequest {
  itemTemplateId: number;
  quantity: number;
}

export interface EquipItemRequest {
  itemTemplateId: number;
  slotType: EquipmentSlotType;
}

export interface UnequipItemRequest {
  slotType: EquipmentSlotType;
}

export interface GiveItemRequest {
  userId: number;
  itemTemplateId: number;
  quantity: number;
  source?: string;
  sourceId?: number;
}

// Item Display Interfaces
export interface ItemDisplayInfo {
  template: ItemTemplate;
  quantity?: number;
  isEquipped?: boolean;
  canUse?: boolean;
  canEquip?: boolean;
  expiresAt?: Date;
}

export interface InventoryFilter {
  category?: ItemCategory;
  rarity?: ItemRarity;
  searchTerm?: string;
  sortBy?: InventorySortType;
  sortOrder?: 'asc' | 'desc';
}

// Enums
export enum ItemCategory {
  CURRENCY = 'currency',
  EXPERIENCE = 'experience', 
  BADGE = 'badge',
  AVATAR_FRAME = 'avatar_frame',
  TITLE = 'title',
  PREMIUM = 'premium',
  CONSUMABLE = 'consumable',
  EQUIPMENT = 'equipment',
  COLLECTIBLE = 'collectible'
}

export enum ItemRarity {
  COMMON = 'common',
  UNCOMMON = 'uncommon',
  RARE = 'rare',
  EPIC = 'epic',
  LEGENDARY = 'legendary'
}

export enum EquipmentSlotType {
  AVATAR_FRAME = 'avatar_frame',
  TITLE = 'title',
  BADGE = 'badge',
  BACKGROUND = 'background'
}

export enum InventorySortType {
  NAME = 'name',
  RARITY = 'rarity',
  QUANTITY = 'quantity',
  OBTAINED_DATE = 'obtainedAt',
  CATEGORY = 'category'
}

// Item Action Types
export enum ItemActionType {
  USE = 'use',
  EQUIP = 'equip',
  UNEQUIP = 'unequip',
  VIEW_DETAILS = 'view_details',
  DELETE = 'delete'
}

// Item Categories Display Info
export interface ItemCategoryInfo {
  category: ItemCategory;
  displayName: string;
  icon: string;
  description: string;
  color: string;
}

// Item Rarity Display Info
export interface ItemRarityInfo {
  rarity: ItemRarity;
  displayName: string;
  color: string;
  bgColor: string;
  borderColor: string;
}

// Constants for UI
export const ITEM_CATEGORY_INFO: Record<ItemCategory, ItemCategoryInfo> = {
  [ItemCategory.CURRENCY]: {
    category: ItemCategory.CURRENCY,
    displayName: 'Tiền tệ',
    icon: 'coins',
    description: 'Các loại tiền tệ trong game',
    color: '#F59E0B'
  },
  [ItemCategory.EXPERIENCE]: {
    category: ItemCategory.EXPERIENCE,
    displayName: 'Kinh nghiệm',
    icon: 'star',
    description: 'Vật phẩm tăng kinh nghiệm',
    color: '#8B5CF6'
  },
  [ItemCategory.BADGE]: {
    category: ItemCategory.BADGE,
    displayName: 'Huy hiệu',
    icon: 'shield',
    description: 'Huy hiệu thành tích',
    color: '#EF4444'
  },
  [ItemCategory.AVATAR_FRAME]: {
    category: ItemCategory.AVATAR_FRAME,
    displayName: 'Khung avatar',
    icon: 'frame',
    description: 'Khung trang trí avatar',
    color: '#06B6D4'
  },
  [ItemCategory.TITLE]: {
    category: ItemCategory.TITLE,
    displayName: 'Danh hiệu',
    icon: 'crown',
    description: 'Danh hiệu đặc biệt',
    color: '#F97316'
  },
  [ItemCategory.PREMIUM]: {
    category: ItemCategory.PREMIUM,
    displayName: 'Premium',
    icon: 'gem',
    description: 'Vật phẩm cao cấp',
    color: '#EC4899'
  },
  [ItemCategory.CONSUMABLE]: {
    category: ItemCategory.CONSUMABLE,
    displayName: 'Tiêu hao',
    icon: 'potion',
    description: 'Vật phẩm sử dụng một lần',
    color: '#10B981'
  },
  [ItemCategory.EQUIPMENT]: {
    category: ItemCategory.EQUIPMENT,
    displayName: 'Trang bị',
    icon: 'sword',
    description: 'Trang bị có thể mặc',
    color: '#6366F1'
  },
  [ItemCategory.COLLECTIBLE]: {
    category: ItemCategory.COLLECTIBLE,
    displayName: 'Sưu tập',
    icon: 'collection',
    description: 'Vật phẩm sưu tập',
    color: '#84CC16'
  }
};

export const ITEM_RARITY_INFO: Record<ItemRarity, ItemRarityInfo> = {
  [ItemRarity.COMMON]: {
    rarity: ItemRarity.COMMON,
    displayName: 'Thường',
    color: '#6B7280',
    bgColor: '#F9FAFB',
    borderColor: '#D1D5DB'
  },
  [ItemRarity.UNCOMMON]: {
    rarity: ItemRarity.UNCOMMON,
    displayName: 'Không thường',
    color: '#059669',
    bgColor: '#ECFDF5',
    borderColor: '#A7F3D0'
  },
  [ItemRarity.RARE]: {
    rarity: ItemRarity.RARE,
    displayName: 'Hiếm',
    color: '#2563EB',
    bgColor: '#EFF6FF',
    borderColor: '#93C5FD'
  },
  [ItemRarity.EPIC]: {
    rarity: ItemRarity.EPIC,
    displayName: 'Sử thi',
    color: '#7C3AED',
    bgColor: '#F5F3FF',
    borderColor: '#C4B5FD'
  },
  [ItemRarity.LEGENDARY]: {
    rarity: ItemRarity.LEGENDARY,
    displayName: 'Huyền thoại',
    color: '#DC2626',
    bgColor: '#FEF2F2',
    borderColor: '#FECACA'
  }
};
