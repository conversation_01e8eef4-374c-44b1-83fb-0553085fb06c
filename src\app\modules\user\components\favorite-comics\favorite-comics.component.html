<div class="favorite-comics-container">
  <!-- <PERSON> Header -->
  <div class="page-header">
    <h2 class="page-title">
      <svg class="title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path
          d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
        />
      </svg>
      Truyện yêu thích
    </h2>
    <p class="page-description">Danh sách các truyện bạn đã lưu vào mục yêu thích</p>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading()">
    <div class="loading-spinner"></div>
    <p class="loading-text"><PERSON><PERSON> tả<PERSON> truyện yêu thích...</p>
  </div>

  <!-- Error State -->
  <div class="content-card" *ngIf="!isLoading() && hasError">
    <div class="error-state">
      <div class="error-illustration">
        <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10" />
          <line x1="12" y1="8" x2="12" y2="12" />
          <line x1="12" y1="16" x2="12.01" y2="16" />
        </svg>
      </div>
      <h3 class="error-title">Có lỗi xảy ra</h3>
      <p class="error-description">{{ errorMessage() }}</p>
      <button class="retry-btn" (click)="onRetryClick()">
        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <polyline points="23,4 23,10 17,10" />
          <path d="M20.49 15a9 9 0 1 1-2.12-9.36L23 10" />
        </svg>
        Thử lại
      </button>
    </div>
  </div>

  <!-- Empty State (No Comics) -->
  <div class="content-card" *ngIf="!isLoading() && !hasComics && !hasError">
    <div class="empty-state">
      <div class="empty-illustration">
        <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path
            d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
          />
        </svg>
      </div>
      <h3 class="empty-title">Chưa có truyện yêu thích</h3>
      <p class="empty-description">
        Bạn chưa lưu truyện nào vào danh sách yêu thích. Hãy khám phá và lưu những truyện bạn thích
        nhé!
      </p>
      <button class="explore-btn" (click)="onExploreClick()">
        <svg class="btn-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8" />
          <path d="M21 21l-4.35-4.35" />
        </svg>
        Khám phá truyện
      </button>
    </div>
  </div>

  <!-- Comics Grid -->
  <div class="comics-content" *ngIf="!isLoading() && hasComics && !hasError">
    <!-- Comics Grid -->
    <div class="comics-grid">
      <div *ngFor="let comic of favoriteComics(); trackBy: trackByComicId" class="comic-item">
        <app-comic-card [comic]="comic"></app-comic-card>
      </div>
    </div>

    <!-- Pagination -->
    <app-pagination
      [totalpage]="totalPages()"
      [currentPage]="currentPage()"
      (OnChange)="onPageChange($event)"
    ></app-pagination>
  </div>

</div>
