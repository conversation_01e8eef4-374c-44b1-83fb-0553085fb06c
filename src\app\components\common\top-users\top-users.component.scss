
// Simple Top Users Component - Comic Website Design
.top-users-container {
  @apply w-full bg-white dark:bg-neutral-800 rounded-lg border border-gray-200 dark:border-neutral-700 shadow-sm overflow-hidden;
}

// Header
.top-users-header {
  @apply w-full flex items-center justify-center py-3 bg-gray-50 dark:bg-neutral-800 border-b border-gray-200 dark:border-neutral-700;
}

.header-title-section {
  @apply flex items-center gap-2;
}

.header-icon {
  @apply w-4 h-4 text-primary-100;
  fill: currentColor;
  stroke: none;
}

.header-title {
  @apply text-base font-bold text-gray-900 dark:text-white;
}

// Content Area
.top-users-content {
  @apply bg-white dark:bg-dark-bg;
}


// Top Users List
.top-users-list {
  @apply grid grid-cols-1 gap-2 p-2;
}

.top-user-item {
  @apply flex-shrink h-full;
}

.user-item-content {
  @apply relative flex items-center gap-3 p-2 bg-gray-50 dark:bg-neutral-800/50 hover:bg-gray-100 dark:hover:bg-neutral-700/50 rounded-lg border border-gray-200 dark:border-neutral-700 h-full cursor-pointer;
}

// Rank Badge
.rank-badge {
  @apply absolute top-0 left-[22px] z-10 flex items-center justify-center w-5 h-5 rounded-full font-bold text-white text-xs shadow-md;

  &.rank-gold {
    @apply bg-gradient-to-br from-yellow-400 to-yellow-600;
  }

  &.rank-silver {
    @apply bg-gradient-to-br from-gray-300 to-gray-500;
  }

  &.rank-bronze {
    @apply bg-gradient-to-br from-orange-400 to-orange-600;
  }

  &.rank-default {
    @apply bg-gradient-to-br from-gray-400 to-gray-600;
  }
}

.rank-number {
  @apply font-bold;
}

// User Avatar
.user-avatar-container {
  @apply relative flex-shrink-0;
}

.user-avatar-link {
  @apply relative block overflow-hidden rounded-lg;
}

.user-avatar {
  @apply w-12 h-12 object-cover rounded-lg;
}

.avatar-overlay {
  @apply absolute inset-0 bg-black/0 hover:bg-black/10;
}

// User Info
.user-info {
  @apply flex-1 min-w-0 space-y-1;
}

.user-main-info {
  @apply space-y-1;
}

.user-name {
  @apply text-sm font-bold text-gray-900 dark:text-white line-clamp-1;
}

.user-name-link {
  @apply hover:text-primary-100;
}

.user-level {
  @apply flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400;
}

.level-icon {
  @apply w-3 h-3 flex-shrink-0;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.level-text {
  @apply hover:text-primary-100 hover:underline line-clamp-1;
}

// User Stats
.user-stats {
  @apply flex items-center justify-start;
}

.exp-count {
  @apply flex items-center gap-1 text-xs text-primary-100 font-semibold;
}

.exp-icon {
  @apply w-3 h-3;
  fill: none;
  stroke: currentColor;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.exp-count-text {
  @apply uppercase;
}
