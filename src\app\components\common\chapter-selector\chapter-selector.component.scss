// Modern Chapter Selector Styles

// Main Container
.chapter-selector {
  @apply relative inline-block;
}

// Trigger But<PERSON>
.selector-trigger {
  @apply flex items-center justify-between gap-3 px-2 py-2;
  @apply bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700;
  @apply rounded-md shadow-sm hover:shadow-md;
  @apply cursor-pointer w-full md:w-64;
  @apply hover:border-sky-300 dark:hover:border-sky-600;
  @apply focus-within:ring-2 focus-within:ring-sky-500/20;
}

.trigger-content {
  @apply flex items-center gap-3 flex-1 min-w-0;
}

// .trigger-icon {
//   @apply flex-shrink-0 w-8 h-8 bg-sky-50 dark:bg-sky-900/30;
//   @apply rounded-lg flex items-center justify-center;
// }

.icon-book {
  @apply w-5 h-5 text-sky-600 dark:text-sky-400;
}

.trigger-text {
  
}
.trigger-title {
  @apply text-sm font-semibold text-neutral-900 dark:text-white;
}

.trigger-arrow {
  @apply flex-shrink-0 w-6 h-6 text-neutral-400 dark:text-neutral-500 hidden sm:block;
  &.rotated {
    @apply rotate-180;
  }
}

.arrow-icon {
  @apply w-full h-full;
}

// Dropdown Panel
.selector-dropdown {
  @apply absolute left-1/2 transform -translate-x-1/2 z-50 mt-2;
  @apply bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700;
  @apply rounded-md shadow-xl backdrop-blur-sm;
  @apply min-w-[320px] max-w-[480px] overflow-hidden;

  &.dropdown-top {
    @apply bottom-full mb-2 mt-0;
  }

  &.dropdown-bottom {
    @apply top-full;
  }
}

// Header
.dropdown-header {
  @apply flex items-center justify-between px-4 py-3;
  @apply border-b border-neutral-100 dark:border-neutral-700;
}

.header-title {
  @apply flex items-center gap-2;
}

.header-icon {
  @apply w-5 h-5 text-sky-600 dark:text-sky-400;
}

.header-title span {
  @apply text-sm font-semibold text-neutral-900 dark:text-white;
}

.chapter-count {
  @apply text-xs text-neutral-500 dark:text-neutral-400;
  @apply bg-neutral-100 dark:bg-neutral-700 px-2 py-1 rounded-full;
}

// Search Container
.search-container {
  @apply p-4 border-b border-neutral-100 dark:border-neutral-700;
}

.search-input-wrapper {
  @apply relative flex items-center;
}

.search-icon {
  @apply absolute left-3 w-4 h-4 text-neutral-400 dark:text-neutral-500;
}

.search-input {
  @apply w-full pl-10 pr-10 py-2.5 text-sm;
  @apply bg-neutral-50 dark:bg-neutral-700 border border-neutral-200 dark:border-neutral-600;
  @apply rounded-lg focus:outline-none focus:ring-2 focus:ring-sky-500/20;
  @apply focus:border-sky-500 dark:focus:border-sky-400;
  @apply text-neutral-900 dark:text-white placeholder-neutral-500 dark:placeholder-neutral-400;
}

.search-clear {
  @apply absolute right-3 w-4 h-4 text-neutral-400 hover:text-neutral-600;
  @apply dark:text-neutral-500 dark:hover:text-neutral-300;
}

// Chapter List
.chapter-list-wrapper {
  @apply py-2 flex w-full overflow-hidden items-center justify-center;
}

.empty-state {
  @apply flex flex-col items-center justify-center py-8 px-4;
  @apply text-neutral-500 dark:text-neutral-400;
}

.empty-icon {
  @apply w-8 h-8 mb-2;
}

.empty-text {
  @apply text-sm;
}

.chapter-scroll {
  @apply w-full max-h-80 min-h-0 flex;
}

// Chapter Item
.chapter-item {
  @apply flex items-center justify-between px-4 py-2;
  @apply hover:bg-neutral-50 dark:hover:bg-neutral-700/50;
  @apply cursor-pointer;
  @apply border-b border-neutral-100 dark:border-neutral-700/50 last:border-b-0;

  &.selected {
    @apply bg-sky-50 dark:bg-sky-900/20 border-sky-100 dark:border-sky-800;
    @apply hover:bg-sky-100 dark:hover:bg-sky-900/30;
  }
}

.chapter-info {
  @apply flex gap-3 flex-1 min-w-0;
}

.chapter-number {
  @apply text-sm font-semibold text-neutral-900 dark:text-white;
}

.chapter-meta {
  @apply flex items-center gap-2;
}

.chapter-date {
  @apply text-xs text-neutral-500 dark:text-neutral-400;
}

.chapter-status {
  @apply flex items-center gap-2;
}

.current-badge {
  @apply flex items-center gap-1 px-2 py-1;
  @apply bg-sky-100 dark:bg-sky-900/30 text-sky-700 dark:text-sky-300;
  @apply rounded-full text-xs font-medium;
}

.badge-icon {
  @apply w-3 h-3;
}

.arrow-right {
  @apply w-4 h-4 text-neutral-400 dark:text-neutral-500;
}

