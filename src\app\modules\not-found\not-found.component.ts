import { Component, Inject, OnInit, Optional, PLATFORM_ID, RESPONSE_INIT } from '@angular/core';
import { SeoService } from '@services/seo.service';
@Component({
    selector: 'app-not-found',
    templateUrl: './not-found.component.html',
    styleUrl: './not-found.component.scss',
    standalone: false
})
export class NotFoundComponent implements OnInit {
  constructor(
    @Inject(PLATFORM_ID) private platformId: object, 
    @Optional() @Inject(RESPONSE_INIT) private response: ResponseInit,
    private seoService: SeoService,
  ) {
    this.seoService.setTitle('404 - Not Found');
  }
  ngOnInit() {
    if(this.response)
    {
      this.response.status = 404;
    }
  }
}
