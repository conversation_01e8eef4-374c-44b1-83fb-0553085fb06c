import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { RouterLink } from '@angular/router';
import { DateAgoPipe } from '@pines/date-ago.pine';
import { Comic } from '@schema';

import { HistoryService } from '@services/history.service';
import { BaseComponent } from '../base/component-base';

@Component({
  selector: 'app-recent-read',
  templateUrl: './recent-read.component.html',
  styleUrl: './recent-read.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [RouterLink, CommonModule, DateAgoPipe],
})
export class RecentReadComponent extends BaseComponent implements OnInit {
  listComics: Comic[] = [];
  nComics = 2;
  constructor(
    private hisService: HistoryService,
    @Inject(PLATFORM_ID) platformId: object,
  ) { super(platformId); }

  ngOnInit(): void {
    this.runInBrowser(() => {
    this.listComics = this.hisService.GetHistorys().slice(0, 2) ?? [];
    this.nComics = this.hisService.GetHistorys().length;
    });
  }

  // TrackBy function for performance optimization
  trackByComicId = (index: number, comic: Comic): number => {
    return comic.id;
  };
}
