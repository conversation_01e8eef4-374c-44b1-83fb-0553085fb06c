<div class="inventory-container">
  <!-- Header -->
  <div class="inventory-header">
    <div class="inventory-header__title">
      <h2 class="inventory-title"><PERSON><PERSON></h2>
      <p class="inventory-subtitle">Qu<PERSON>n lý vật phẩm của bạn</p>
    </div>

  </div>

  <!-- Tabs -->
  <div class="inventory-tabs">
    <button
      class="tab-btn"
      [class.tab-btn--active]="activeTab() === 'owned'"
      (click)="onTabChange('owned')"
    >
      <i class="icon-backpack"></i>
      <span><PERSON>ang sở hữu</span>
      <span class="tab-count" *ngIf="inventoryItems().length > 0">{{ inventoryItems().length }}</span>
    </button>

    <button
      class="tab-btn"
      [class.tab-btn--active]="activeTab() === 'equipped'"
      (click)="onTabChange('equipped')"
    >
      <i class="icon-equipped"></i>
      <span><PERSON><PERSON> trang bị</span>
      <span class="tab-count" *ngIf="equippedItems().length > 0">{{ equippedItems().length }}</span>
    </button>

    <button
      class="tab-btn"
      [class.tab-btn--active]="activeTab() === 'all'"
      (click)="onTabChange('all')"
    >
      <i class="icon-catalog"></i>
      <span>Toàn bộ items</span>
      <span class="tab-count" *ngIf="allItemTemplates().length > 0">{{ allItemTemplates().length }}</span>
    </button>
  </div>

  <!-- Filters -->
  <div class="inventory-filters">
    <!-- Search -->
    <div class="filter-group">
      <label class="filter-label">Tìm kiếm</label>
      <input 
        type="text"
        class="filter-input"
        placeholder="Tìm theo tên hoặc mô tả..."
        [value]="filter().searchTerm || ''"
        (input)="onFilterChange({ searchTerm: $any($event.target).value })"
      >
    </div>

    <!-- Category Filter -->
    <div class="filter-group">
      <label class="filter-label">Loại vật phẩm</label>
      <select 
        class="filter-select"
        [value]="filter().category || ''"
        (change)="onFilterChange({ category: $any($event.target).value || undefined })"
      >
        <option value="">Tất cả</option>
        <option *ngFor="let category of categoryOptions" [value]="category">
          {{ ITEM_CATEGORY_INFO[category].displayName }}
        </option>
      </select>
    </div>

    <!-- Rarity Filter -->
    <div class="filter-group">
      <label class="filter-label">Độ hiếm</label>
      <select 
        class="filter-select"
        [value]="filter().rarity || ''"
        (change)="onFilterChange({ rarity: $any($event.target).value || undefined })"
      >
        <option value="">Tất cả</option>
        <option *ngFor="let rarity of rarityOptions" [value]="rarity">
          {{ ITEM_RARITY_INFO[rarity].displayName }}
        </option>
      </select>
    </div>

    <!-- Sort -->
    <div class="filter-group">
      <label class="filter-label">Sắp xếp</label>
      <select 
        class="filter-select"
        [value]="filter().sortBy || ''"
        (change)="onFilterChange({ sortBy: $any($event.target).value || undefined })"
      >
        <option value="">Mặc định</option>
        <option *ngFor="let option of sortOptions" [value]="option.value">
          {{ option.label }}
        </option>
      </select>
    </div>

    <!-- Sort Order -->
    <div class="filter-group" *ngIf="filter().sortBy">
      <label class="filter-label">Thứ tự</label>
      <select 
        class="filter-select"
        [value]="filter().sortOrder || 'asc'"
        (change)="onFilterChange({ sortOrder: $any($event.target).value })"
      >
        <option value="asc">Tăng dần</option>
        <option value="desc">Giảm dần</option>
      </select>
    </div>
  </div>

  <!-- Selected Items Actions -->
  <div class="selected-actions" *ngIf="hasSelectedItems">
    <div class="selected-info">
      <span>Đã chọn {{ selectedItemsCount }} vật phẩm</span>
    </div>
    <div class="selected-buttons">
      <button class="action-btn action-btn--secondary" (click)="clearSelection()">
        Bỏ chọn tất cả
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading()">
    <div class="loading-spinner"></div>
    <p>
      <span *ngIf="activeTab() === 'owned'">Đang tải kho đồ...</span>
      <span *ngIf="activeTab() === 'equipped'">Đang tải vật phẩm đang trang bị...</span>
      <span *ngIf="activeTab() === 'all'">Đang tải toàn bộ vật phẩm...</span>
    </p>
  </div>

  <!-- Empty State -->
  <div class="empty-state" *ngIf="!isLoading() && filteredItems.length === 0">
    <div class="empty-state__icon">
      <i class="icon-empty-box"></i>
    </div>
    <h3 class="empty-state__title">
      <span *ngIf="activeTab() === 'owned'">Không có vật phẩm sở hữu</span>
      <span *ngIf="activeTab() === 'equipped'">Không có vật phẩm đang trang bị</span>
      <span *ngIf="activeTab() === 'all'">Không có vật phẩm</span>
    </h3>
    <p class="empty-state__description">
      <span *ngIf="filter().searchTerm || filter().category || filter().rarity">
        Không tìm thấy vật phẩm phù hợp với bộ lọc.
      </span>
      <span *ngIf="!filter().searchTerm && !filter().category && !filter().rarity && activeTab() === 'owned'">
        Kho đồ của bạn đang trống. Hãy hoàn thành nhiệm vụ để nhận vật phẩm!
      </span>
      <span *ngIf="!filter().searchTerm && !filter().category && !filter().rarity && activeTab() === 'equipped'">
        Bạn chưa trang bị vật phẩm nào. Hãy trang bị vật phẩm từ kho đồ!
      </span>
      <span *ngIf="!filter().searchTerm && !filter().category && !filter().rarity && activeTab() === 'all'">
        Không có vật phẩm nào trong hệ thống.
      </span>
    </p>
  </div>

  <!-- Items Grid/List -->
  <div class="inventory-content" *ngIf="!isLoading() && filteredItems.length > 0">
    <!-- Grid View -->
    <div 
      class="items-grid"
      [class.items-grid--small]="cardSize() === 'small'"
      [class.items-grid--medium]="cardSize() === 'medium'"
      [class.items-grid--large]="cardSize() === 'large'"
    >
      <app-item-card
        *ngFor="let item of filteredItems; trackBy: trackByItemId"
        [item]="item"
        [size]="cardSize()"
        [selectable]="true"
        [selected]="selectedItems().has(item.template.id)"
        (actionClicked)="onItemAction($event)"
        (itemClicked)="onItemClick($event)"
        (selectionChanged)="onSelectionChanged($event)"
      ></app-item-card>
    </div>


  </div>

  <!-- Pagination -->
  <div class="inventory-pagination" *ngIf="!isLoading() && totalPages() > 1">
    <button 
      class="pagination-btn"
      [disabled]="currentPage() <= 1"
      (click)="onPageChange(currentPage() - 1)"
    >
      Trước
    </button>
    
    <span class="pagination-info">
      Trang {{ currentPage() }} / {{ totalPages() }}
    </span>
    
    <button 
      class="pagination-btn"
      [disabled]="currentPage() >= totalPages()"
      (click)="onPageChange(currentPage() + 1)"
    >
      Sau
    </button>
  </div>
</div>
