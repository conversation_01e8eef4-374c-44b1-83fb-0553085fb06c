import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  OnInit,
  Output,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SyncCredentials, SyncSettings } from '../../models/sync-tracking.models';
import { SyncTrackingService } from '../../services/sync-tracking.service';

@Component({
  selector: 'app-sync-form',
  templateUrl: './sync-form.component.html',
  styleUrl: './sync-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class SyncFormComponent extends OptimizedBaseComponent implements OnInit {
  @Output() syncStart = new EventEmitter<{ credentials: SyncCredentials; settings: SyncSettings }>();

  // Form groups
  myForm!: FormGroup;

  // Component state
  private readonly _isLoading = signal(false);
  private readonly _showPasswords = signal({ nettruyen: false, truyenqq: false });
  private readonly _credentialsValid = signal({ nettruyen: false, truyenqq: false });

  // Computed properties
  readonly isLoading = computed(() => this._isLoading());
  readonly showPasswords = computed(() => this._showPasswords());
  readonly credentialsValid = computed(() => this._credentialsValid());
  readonly canStartSync = computed(() => {
    return this.myForm?.valid && 
           !this.isLoading() &&
           !this.syncService.isSyncInProgress();
  });

  constructor(
    private fb: FormBuilder,
    private syncService: SyncTrackingService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.initializeForms();
    this.loadSavedSettings();
  }

  // ===== FORM INITIALIZATION =====

  private initializeForms(): void {
    this.myForm = this.fb.group({
      nettruyen: this.fb.group({
        username: ['', [Validators.required, Validators.minLength(3)]],
        password: ['', [Validators.required, Validators.minLength(6)]]
      }),
      truyenqq: this.fb.group({
        username: ['', [Validators.required, Validators.minLength(3)]],
        password: ['', [Validators.required, Validators.minLength(6)]]
      }),
      autoResolveConflicts: [false],
      preferredSource: ['latest', Validators.required],
      syncInterval: [24, [Validators.required, Validators.min(1), Validators.max(168)]],
      enableNotifications: [true],
      backupBeforeSync: [true]
    });

  }

  private loadSavedSettings(): void {
    this.runInBrowser(() => {
      this.syncService.getSettings().subscribe({
        next: (settings) => {
          this.myForm.patchValue(settings);
          this.safeMarkForCheck();
        },
        error: (error) => {
          console.warn('Could not load saved settings:', error);
        }
      });
    });
  }

  // ===== CREDENTIAL TESTING =====

  async testCredentials(site: 'nettruyen' | 'truyenqq'): Promise<void> {
    const credentials = this.myForm.get(site)?.value;
    if (!credentials?.username || !credentials?.password) return;

    this._isLoading.set(true);
    this.safeMarkForCheck();

    try {
      const isValid = await this.syncService.testCredentials(site, credentials).toPromise();
      this._credentialsValid.update(current => ({ ...current, [site]: isValid || false }));
      
      if (isValid) {
        this.showSuccessMessage(`${site} credentials are valid!`);
      } else {
        this.showErrorMessage(`Invalid ${site} credentials. Please check and try again.`);
      }
    } catch (error) {
      this.showErrorMessage(`Failed to test ${site} credentials: ${error}`);
      this._credentialsValid.update(current => ({ ...current, [site]: false }));
    } finally {
      this._isLoading.set(false);
      this.safeMarkForCheck();
    }
  }

  // ===== PASSWORD VISIBILITY =====

  togglePasswordVisibility(site: 'nettruyen' | 'truyenqq'): void {
    this._showPasswords.update(current => ({
      ...current,
      [site]: !current[site]
    }));
  }

  // ===== FORM SUBMISSION =====

  onSubmit(): void {
    if (!this.canStartSync()) return;

    const credentials: SyncCredentials = this.myForm.value;
    // const settings: SyncSettings = this.settingsForm.value;

    // Save settings for future use
    // this.syncService.saveSettings(settings).subscribe();

    // Emit sync start event
    // this.syncStart.emit({ credentials, settings });
  }

  // ===== FORM VALIDATION =====

  getFieldError(formGroup: FormGroup, fieldPath: string): string | null {
    const field = formGroup.get(fieldPath);
    if (!field?.errors || !field.touched) return null;

    if (field.errors['required']) return 'This field is required';
    if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} characters`;
    if (field.errors['min']) return `Minimum value is ${field.errors['min'].min}`;
    if (field.errors['max']) return `Maximum value is ${field.errors['max'].max}`;

    return 'Invalid input';
  }

  isFieldInvalid(formGroup: FormGroup, fieldPath: string): boolean {
    const field = formGroup.get(fieldPath);
    return !!(field?.invalid && field.touched);
  }

  // ===== UTILITY METHODS =====

  private showSuccessMessage(message: string): void {
    // Implement toast notification or similar
    console.log('Success:', message);
  }

  private showErrorMessage(message: string): void {
    // Implement toast notification or similar
    console.error('Error:', message);
  }

  // ===== FORM RESET =====

  resetForm(): void {
    this.myForm.reset();
    this._credentialsValid.set({ nettruyen: false, truyenqq: false });
    this._showPasswords.set({ nettruyen: false, truyenqq: false });
    this.initializeForms();
  }

  // ===== PRESET ACTIONS =====

  fillSampleCredentials(): void {
    this.myForm.patchValue({
      nettruyen: {
        username: 'sample_user',
        password: 'sample_pass'
      },
      truyenqq: {
        username: 'sample_user',
        password: 'sample_pass'
      }
    });
  }

  // ===== TRACKBY FUNCTIONS =====

  override trackByIndex = (index: number): number => index;
}
