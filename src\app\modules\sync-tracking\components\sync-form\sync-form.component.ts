import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  OnInit,
  Output,
  PLATFORM_ID,
  computed,
  signal,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { firstValueFrom } from 'rxjs';
import { SyncCredentials } from '../../models/sync-tracking.models';
import { ScriptTemplatesService } from '../../services/script-templates.service';
import { SyncTrackingService } from '../../services/sync-tracking.service';

@Component({
  selector: 'app-sync-form',
  templateUrl: './sync-form.component.html',
  styleUrl: './sync-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false,
})
export class SyncFormComponent extends OptimizedBaseComponent implements OnInit {
  @Output() syncStart = new EventEmitter<{ credentials: SyncCredentials }>();

  // Form groups
  syncForm!: FormGroup;

  // Component state
  private readonly _isLoading = signal(false);
  private readonly _showPassword = signal(false);
  private readonly _credentialsValid = signal(false);
  private readonly _currentStep = signal<'source' | 'method' | 'credentials'>('source');

  // Computed properties
  readonly isLoading = computed(() => this._isLoading());
  readonly showPassword = computed(() => this._showPassword());
  readonly credentialsValid = computed(() => this._credentialsValid());
  readonly currentStep = computed(() => this._currentStep());

  readonly canStartSync = () => {
    return this.syncForm?.valid && !this.isLoading() && !this.syncService.isSyncInProgress();
  };

  readonly sourceOptions = computed(() => [
    { value: 'nettruyen', label: 'NetTruyen', shortName: 'N', icon: 'nettruyen', color: 'yellow' },
    { value: 'truyenqq', label: 'TruyenQQ', shortName: 'QQ', icon: 'truyenqq', color: 'cyan' },
  ]);

  readonly methodOptions = computed(() => [
    {
      value: 'account',
      label: 'Sử dụng tài khoản',
      icon: 'user',
      description: 'Đăng nhập bằng tên đăng nhập và mật khẩu (recommend)',
    },
    {
      value: 'script',
      label: 'Sử dụng script',
      icon: 'code',
      description:
        'Chạy script trong console để lấy dữ liệu, không cần đăng nhập (Chỉ chạy được trên máy tính)',
    },
  ]);

  readonly selectedMethod = () => this.syncForm.get('method')?.value;

  readonly showAccountForm = () => this.syncForm.get('method')?.value === 'account';

  readonly showScriptForm = () => this.syncForm.get('method')?.value === 'script';

  readonly scriptInstructions = computed(() => {
    const sourceFrom = this.syncForm?.get('sourceFrom')?.value;
    return sourceFrom ? this.scriptTemplatesService.getInstructions(sourceFrom) : [];
  });

  readonly scriptTemplate = computed(() => {
    const sourceFrom = this.syncForm?.get('sourceFrom')?.value;
    return sourceFrom ? this.scriptTemplatesService.getScriptTemplate(sourceFrom) : '';
  });

  constructor(
    private fb: FormBuilder,
    private syncService: SyncTrackingService,
    private scriptTemplatesService: ScriptTemplatesService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  // ===== FORM INITIALIZATION =====

  private initializeForm(): void {
    this.syncForm = this.fb.group({
      sourceFrom: ['', Validators.required],
      method: ['', Validators.required],
      username: [''],
      password: [''],
      scriptData: [''],
    });

    // Watch for method changes to update validation
    this.syncForm.get('method')?.valueChanges.subscribe(method => {
      this.updateValidationBasedOnMethod(method);
    });
  }

  private updateValidationBasedOnMethod(method: 'account' | 'script'): void {
    const usernameControl = this.syncForm.get('username');
    const passwordControl = this.syncForm.get('password');
    const scriptDataControl = this.syncForm.get('scriptData');

    if (method === 'account') {
      usernameControl?.setValidators([Validators.required, Validators.minLength(3)]);
      passwordControl?.setValidators([Validators.required, Validators.minLength(3)]);
      scriptDataControl?.clearValidators();
    } else if (method === 'script') {
      usernameControl?.clearValidators();
      passwordControl?.clearValidators();
      scriptDataControl?.setValidators([Validators.required, Validators.minLength(10)]);
    }

    usernameControl?.updateValueAndValidity();
    passwordControl?.updateValueAndValidity();
    scriptDataControl?.updateValueAndValidity();
  }

  // ===== STEP NAVIGATION =====

  goToStep(step: 'source' | 'method' | 'credentials'): void {
    this._currentStep.set(step);
  }

  nextStep(): void {
    const current = this.currentStep();
    if (current === 'source' && this.isSourceStepValid()) {
      this._currentStep.set('method');
    } else if (current === 'method' && this.isMethodStepValid()) {
      this._currentStep.set('credentials');
    }
  }

  prevStep(): void {
    const current = this.currentStep();
    if (current === 'credentials') {
      this._currentStep.set('method');
    } else if (current === 'method') {
      this._currentStep.set('source');
    }
  }

  // ===== STEP VALIDATION =====

  isSourceStepValid(): boolean {
    const sourceFrom = this.syncForm.get('sourceFrom')?.value;
    return !!sourceFrom;
  }

  isMethodStepValid(): boolean {
    const method = this.syncForm.get('method')?.value;
    return !!method;
  }

  isCredentialsStepValid(): boolean {
    const method = this.syncForm.get('method')?.value;

    if (method === 'account') {
      const username = this.syncForm.get('username')?.value;
      const password = this.syncForm.get('password')?.value;
      return !!(username && password && username.length >= 3 && password.length >= 3);
    } else if (method === 'script') {
      const scriptData = this.syncForm.get('scriptData')?.value;
      return !!(scriptData && scriptData.length >= 10);
    }

    return false;
  }

  // ===== CREDENTIAL TESTING =====

  async testCredentials(): Promise<void> {
    const sourceFrom = this.syncForm.get('sourceFrom')?.value;
    const method = this.syncForm.get('method')?.value;

    let credentials: any = {};

    if (method === 'account') {
      credentials = {
        username: this.syncForm.get('username')?.value,
        password: this.syncForm.get('password')?.value,
      };

      if (!sourceFrom || !credentials.username || !credentials.password) return;
    } else if (method === 'script') {
      const scriptData = this.syncForm.get('scriptData')?.value;

      if (!sourceFrom || !scriptData) return;

      // Validate script data format
      const validation = this.scriptTemplatesService.validateScriptData(scriptData);
      if (!validation.valid) {
        this.showErrorMessage(validation.error || 'Invalid script data format');
        return;
      }

      credentials = {
        scriptData: scriptData,
        parsedData: validation.parsedData,
      };
    }

    this._isLoading.set(true);
    this.safeMarkForCheck();

    try {
      const result = await firstValueFrom(
        this.syncService.testCredentials(sourceFrom, credentials)
      );
      this._credentialsValid.set(result || false);

      if (result) {
        this.showSuccessMessage(`Connection test successful for ${sourceFrom}!`);
      } else {
        this.showErrorMessage(
          `Connection test failed for ${sourceFrom}. Please check and try again.`
        );
      }
    } catch (error) {
      this.showErrorMessage(`Failed to test connection: ${error}`);
      this._credentialsValid.set(false);
    } finally {
      this._isLoading.set(false);
      this.safeMarkForCheck();
    }
  }

  // ===== PASSWORD VISIBILITY =====

  togglePasswordVisibility(): void {
    this._showPassword.update(current => !current);
  }

  // ===== FORM SUBMISSION =====

  onSubmit(): void {
    if (!this.canStartSync()) return;

    const formValue = this.syncForm.value;
    const credentials: SyncCredentials = {
      sourceFrom: formValue.sourceFrom,
      sourceTo: 'local',
      method: formValue.method,
      username: formValue.username,
      password: formValue.password,
      scriptData: formValue.scriptData,
    };

    // Emit sync start event
    this.syncStart.emit({ credentials });
  }

  // ===== FORM VALIDATION =====

  getFieldError(formGroup: FormGroup, fieldPath: string): string | null {
    const field = formGroup.get(fieldPath);
    if (!field?.errors || !field.touched) return null;

    if (field.errors['required']) return 'This field is required';
    if (field.errors['minlength'])
      return `Minimum ${field.errors['minlength'].requiredLength} characters`;
    if (field.errors['min']) return `Minimum value is ${field.errors['min'].min}`;
    if (field.errors['max']) return `Maximum value is ${field.errors['max'].max}`;

    return 'Invalid input';
  }

  isFieldInvalid(formGroup: FormGroup, fieldPath: string): boolean {
    const field = formGroup.get(fieldPath);
    return !!(field?.invalid && field.touched);
  }

  // ===== UTILITY METHODS =====

  private showSuccessMessage(message: string): void {
    // Implement toast notification or similar
    console.log('Success:', message);
  }

  private showErrorMessage(message: string): void {
    // Implement toast notification or similar
    console.error('Error:', message);
  }

  // ===== FORM RESET =====

  resetForm(): void {
    this.syncForm.reset();
    this._credentialsValid.set(false);
    this._showPassword.set(false);
    this._currentStep.set('source');
    this.initializeForm();
  }

  // ===== PRESET ACTIONS =====

  fillSampleCredentials(): void {
    this.syncForm.patchValue({
      sourceFrom: 'nettruyen',
      username: 'sample_user',
      password: 'sample_pass',
    });
  }

  // ===== UTILITY METHODS =====

  copyScript(): void {
    const script = this.scriptTemplate();
    if (script) {
      navigator.clipboard
        .writeText(script)
        .then(() => {
          this.showSuccessMessage('Script đã được copy vào clipboard!');
        })
        .catch(() => {
          this.showErrorMessage('Không thể copy script. Vui lòng copy thủ công.');
        });
    }
  }

  // ===== TRACKBY FUNCTIONS =====

  override trackByIndex = (index: number): number => index;
  trackByValue = (_index: number, item: any): string => item.value;
}
