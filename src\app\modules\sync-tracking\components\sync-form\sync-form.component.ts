import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Inject,
  OnInit,
  Output,
  PLATFORM_ID,
  computed,
  signal
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { SyncCredentials, SyncSettings } from '../../models/sync-tracking.models';
import { SyncTrackingService } from '../../services/sync-tracking.service';

@Component({
  selector: 'app-sync-form',
  templateUrl: './sync-form.component.html',
  styleUrl: './sync-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class SyncFormComponent extends OptimizedBaseComponent implements OnInit {
  @Output() syncStart = new EventEmitter<{ credentials: SyncCredentials; settings: SyncSettings }>();

  // Form groups
  syncForm!: FormGroup;

  // Component state
  private readonly _isLoading = signal(false);
  private readonly _showPassword = signal(false);
  private readonly _credentialsValid = signal(false);
  private readonly _currentStep = signal<'source' | 'credentials' | 'settings'>('source');

  // Computed properties
  readonly isLoading = computed(() => this._isLoading());
  readonly showPassword = computed(() => this._showPassword());
  readonly credentialsValid = computed(() => this._credentialsValid());
  readonly currentStep = computed(() => this._currentStep());

  readonly canStartSync = computed(() => {
    return this.syncForm?.valid &&
           !this.isLoading() &&
           !this.syncService.isSyncInProgress();
  });

  readonly sourceOptions = computed(() => [
    { value: 'nettruyen', label: 'NetTruyen.com', icon: 'nettruyen', color: 'green' },
    { value: 'truyenqq', label: 'TruyenQQ.com', icon: 'truyenqq', color: 'orange' }
  ]);

  readonly syncDirections = computed(() => {
    const sourceFrom = this.syncForm?.get('sourceFrom')?.value;
    const sourceTo = this.syncForm?.get('sourceTo')?.value;

    if (!sourceFrom || !sourceTo) return '';

    const fromLabel = this.sourceOptions().find(s => s.value === sourceFrom)?.label;
    const toLabel = this.sourceOptions().find(s => s.value === sourceTo)?.label;

    return `${fromLabel} → ${toLabel}`;
  });

  constructor(
    private fb: FormBuilder,
    private syncService: SyncTrackingService,
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadSavedSettings();
  }

  // ===== FORM INITIALIZATION =====

  private initializeForm(): void {
    this.syncForm = this.fb.group({
      sourceFrom: ['', Validators.required],
      sourceTo: ['', Validators.required],
      username: ['', [Validators.required, Validators.minLength(3)]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      autoResolveConflicts: [false],
      preferredSource: ['latest', Validators.required],
      syncInterval: [24, [Validators.required, Validators.min(1), Validators.max(168)]],
      enableNotifications: [true],
      backupBeforeSync: [true]
    });

    // Watch for source changes to validate sync direction
    this.syncForm.get('sourceFrom')?.valueChanges.subscribe(sourceFrom => {
      const sourceTo = this.syncForm.get('sourceTo')?.value;
      if (sourceFrom && sourceTo && sourceFrom === sourceTo) {
        this.syncForm.get('sourceTo')?.setValue('');
      }
    });

    this.syncForm.get('sourceTo')?.valueChanges.subscribe(sourceTo => {
      const sourceFrom = this.syncForm.get('sourceFrom')?.value;
      if (sourceFrom && sourceTo && sourceFrom === sourceTo) {
        this.syncForm.get('sourceFrom')?.setValue('');
      }
    });
  }

  private loadSavedSettings(): void {
    this.runInBrowser(() => {
      this.syncService.getSettings().subscribe({
        next: (settings) => {
          this.syncForm.patchValue(settings);
          this.safeMarkForCheck();
        },
        error: (error) => {
          console.warn('Could not load saved settings:', error);
        }
      });
    });
  }

  // ===== STEP NAVIGATION =====

  goToStep(step: 'source' | 'credentials' | 'settings'): void {
    this._currentStep.set(step);
  }

  nextStep(): void {
    const current = this.currentStep();
    if (current === 'source' && this.isSourceStepValid()) {
      this._currentStep.set('credentials');
    } else if (current === 'credentials' && this.isCredentialsStepValid()) {
      this._currentStep.set('settings');
    }
  }

  prevStep(): void {
    const current = this.currentStep();
    if (current === 'settings') {
      this._currentStep.set('credentials');
    } else if (current === 'credentials') {
      this._currentStep.set('source');
    }
  }

  // ===== STEP VALIDATION =====

  isSourceStepValid(): boolean {
    const sourceFrom = this.syncForm.get('sourceFrom')?.value;
    const sourceTo = this.syncForm.get('sourceTo')?.value;
    return !!(sourceFrom && sourceTo && sourceFrom !== sourceTo);
  }

  isCredentialsStepValid(): boolean {
    const username = this.syncForm.get('username')?.value;
    const password = this.syncForm.get('password')?.value;
    return !!(username && password && username.length >= 3 && password.length >= 6);
  }

  // ===== CREDENTIAL TESTING =====

  async testCredentials(): Promise<void> {
    const sourceFrom = this.syncForm.get('sourceFrom')?.value;
    const credentials = {
      username: this.syncForm.get('username')?.value,
      password: this.syncForm.get('password')?.value
    };

    if (!sourceFrom || !credentials.username || !credentials.password) return;

    this._isLoading.set(true);
    this.safeMarkForCheck();

    try {
      const isValid = await this.syncService.testCredentials(sourceFrom, credentials).toPromise();
      this._credentialsValid.set(isValid || false);

      if (isValid) {
        this.showSuccessMessage(`Credentials are valid for ${sourceFrom}!`);
      } else {
        this.showErrorMessage(`Invalid credentials for ${sourceFrom}. Please check and try again.`);
      }
    } catch (error) {
      this.showErrorMessage(`Failed to test credentials: ${error}`);
      this._credentialsValid.set(false);
    } finally {
      this._isLoading.set(false);
      this.safeMarkForCheck();
    }
  }

  // ===== PASSWORD VISIBILITY =====

  togglePasswordVisibility(): void {
    this._showPassword.update(current => !current);
  }

  // ===== FORM SUBMISSION =====

  onSubmit(): void {
    if (!this.canStartSync()) return;

    const formValue = this.syncForm.value;
    const credentials: SyncCredentials = {
      username: formValue.username,
      password: formValue.password,
      sourceFrom: formValue.sourceFrom,
      sourceTo: formValue.sourceTo
    };

    const settings: SyncSettings = {
      autoResolveConflicts: formValue.autoResolveConflicts,
      preferredSource: formValue.preferredSource,
      syncInterval: formValue.syncInterval,
      enableNotifications: formValue.enableNotifications,
      backupBeforeSync: formValue.backupBeforeSync
    };

    // Save settings for future use
    this.syncService.saveSettings(settings).subscribe();

    // Emit sync start event
    this.syncStart.emit({ credentials, settings });
  }

  // ===== FORM VALIDATION =====

  getFieldError(formGroup: FormGroup, fieldPath: string): string | null {
    const field = formGroup.get(fieldPath);
    if (!field?.errors || !field.touched) return null;

    if (field.errors['required']) return 'This field is required';
    if (field.errors['minlength']) return `Minimum ${field.errors['minlength'].requiredLength} characters`;
    if (field.errors['min']) return `Minimum value is ${field.errors['min'].min}`;
    if (field.errors['max']) return `Maximum value is ${field.errors['max'].max}`;

    return 'Invalid input';
  }

  isFieldInvalid(formGroup: FormGroup, fieldPath: string): boolean {
    const field = formGroup.get(fieldPath);
    return !!(field?.invalid && field.touched);
  }

  // ===== UTILITY METHODS =====

  private showSuccessMessage(message: string): void {
    // Implement toast notification or similar
    console.log('Success:', message);
  }

  private showErrorMessage(message: string): void {
    // Implement toast notification or similar
    console.error('Error:', message);
  }

  // ===== FORM RESET =====

  resetForm(): void {
    this.syncForm.reset();
    this._credentialsValid.set(false);
    this._showPassword.set(false);
    this._currentStep.set('source');
    this.initializeForm();
  }

  // ===== PRESET ACTIONS =====

  fillSampleCredentials(): void {
    this.syncForm.patchValue({
      sourceFrom: 'nettruyen',
      sourceTo: 'truyenqq',
      username: 'sample_user',
      password: 'sample_pass'
    });
  }

  // ===== UTILITY METHODS =====

  getAvailableTargets(): Array<{value: string, label: string, icon: string, color: string}> {
    const sourceFrom = this.syncForm.get('sourceFrom')?.value;
    return this.sourceOptions().filter(option => option.value !== sourceFrom);
  }

  // ===== TRACKBY FUNCTIONS =====

  override trackByIndex = (index: number): number => index;
  trackByValue = (index: number, item: any): string => item.value;
}
