@use '../../user.share.scss' as *;


// Daily Quests Component Styles
.daily-quests-container {
  @apply p-6 bg-white dark:bg-neutral-900 min-h-screen;
}


// Tab Navigation
.quest-tabs {
  @apply flex gap-2 mb-6 bg-gray-100 dark:bg-neutral-800 rounded-lg p-1;
}

.tab-button {
  @apply flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md;
  @apply text-gray-600 dark:text-gray-400 transition-all duration-200;
  @apply hover:bg-white dark:hover:bg-neutral-700;

  &.active {
    @apply bg-white dark:bg-neutral-700 text-primary-100 shadow-sm;
  }
}

.tab-icon {
  @apply text-lg;
}

.tab-label {
  @apply font-medium;
}

.tab-count {
  @apply bg-gray-200 dark:bg-neutral-600 text-xs px-2 py-1 rounded-full;

  .tab-button.active & {
    @apply bg-primary-100 text-white;
  }
}

.refresh-button {
  @apply flex items-center justify-center w-12 h-12 rounded-lg;
  @apply bg-white dark:bg-neutral-700 text-gray-600 dark:text-gray-400;
  @apply hover:bg-gray-50 dark:hover:bg-neutral-600 hover:text-green-600 dark:hover:text-green-400;
  @apply transition-all duration-200 border-0;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  @apply focus:outline-none focus:ring-2 focus:ring-green-500/20;
}

.refresh-icon {
  @apply text-lg transition-transform duration-500;

  &.spinning {
    @apply animate-spin;
  }
}

.loading-spinner {
  @apply animate-spin;
}

// Quest Content
.quest-content {
  @apply space-y-4;
}

.quest-list {
  @apply space-y-4;
}

.quest-card {
  @apply bg-white dark:bg-neutral-800 border border-gray-200 dark:border-gray-700;
  @apply rounded-xl p-6 flex items-start gap-4 transition-all duration-200;
  @apply hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600;

  &.completed {
    @apply bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800;
  }

  &.expired {
    @apply bg-gray-50 dark:bg-gray-900/50 border-gray-300 dark:border-gray-600 opacity-60;
  }

  &.weekly-quest {
    @apply border-l-4 border-l-purple-500;
  }
}

.quest-icon {
  @apply text-3xl flex-shrink-0;
}

.quest-info {
  @apply flex-1 min-w-0;
}

.quest-header-info {
  @apply flex items-start justify-between gap-3 mb-2;
}

.quest-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.difficulty-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium flex-shrink-0;
}

.quest-description {
  @apply text-gray-600 dark:text-gray-400 text-sm mb-4;
}

.quest-progress {
  @apply space-y-2;
}

.progress-info {
  @apply flex justify-between items-center text-sm;
}

.progress-text {
  @apply font-medium text-gray-900 dark:text-white;
}

.time-remaining {
  @apply text-gray-500 dark:text-gray-400;
}

.progress-bar {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden;
}

.progress-fill {
  @apply h-full bg-primary-100 rounded-full transition-all duration-500;
}

.quest-reward {
  @apply flex flex-col items-end gap-3 flex-shrink-0;
}

.reward-info {
  @apply flex items-center gap-2 text-sm;
}

.reward-icon {
  @apply text-lg;
}

.reward-text {
  @apply font-medium text-gray-900 dark:text-white;
}

.claim-button {
  @apply px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200;
  @apply bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400;
  @apply hover:bg-gray-200 dark:hover:bg-gray-600;

  &.completed {
    @apply bg-primary-100 text-white hover:bg-primary-200;
  }

  &:disabled {
    @apply opacity-50 cursor-not-allowed;
  }
}

// Empty State
.empty-state {
  @apply text-center py-12;
}

.empty-icon {
  @apply text-6xl mb-4;
}

.empty-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.empty-description {
  @apply text-gray-600 dark:text-gray-400;
}

// Responsive Design
@media (max-width: 768px) {
  .daily-quests-container {
    @apply p-4;
  }

  .level-info {
    @apply flex-col gap-4;
  }

  .stats-grid {
    @apply grid-cols-1;
  }

  .quest-card {
    @apply flex-col gap-4 p-4;
  }

  .quest-header-info {
    @apply flex-col items-start gap-2;
  }

  .quest-reward {
    @apply flex-row justify-between items-center w-full;
  }

  .tab-button {
    @apply flex-col gap-1 py-2;
  }

  .tab-label {
    @apply text-xs;
  }

  .user-level-card {
    @apply p-4;
  }

  .level-number {
    @apply text-3xl;
  }

  .exp-text {
    @apply flex-wrap;
  }

  .current-exp {
    @apply text-xl;
  }
}



// Hover effects for quest cards
.quest-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
  }
}

