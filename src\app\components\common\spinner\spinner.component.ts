import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, Input, NgZone, OnInit, PLATFORM_ID, ViewEncapsulation } from '@angular/core';
import { timer } from 'rxjs';

@Component({
  selector: 'app-spinner',
  templateUrl: './spinner.component.html',
  styleUrl: './spinner.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [CommonModule],
})
export class SpinnerComponent implements OnInit {
  loading = true;
  @Input() sizeSpinner = '12';
  constructor(@Inject(PLATFORM_ID) private platformId: object,
    private ngZone: NgZone,
    private cd: ChangeDetectorRef
  ) { }
  ngOnInit() {
    this.loading = true;
    if (isPlatformBrowser(this.platformId)) {
      this.ngZone.runOutsideAngular(() => {
        timer(1000).subscribe(() => {
          this.loading = false;
          this.cd.detectChanges();
        });
      });
    }
  }
}
