import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
    selector: 'app-eye-icon',
    templateUrl: './eye-icon.component.html',
    styleUrl: './eye-icon.component.scss',
    standalone: true,
  imports: [CommonModule],

})
export class EyeIconComponent {
  @Input() show = false;

  get iconClass() {
    return 'h-5 w-5 text-gray-400 hover:text-gray-500';
  }
}
