
using System.Threading.Channels;
using ComicAPI.Services;

namespace ComicAPI.Updater
{
    public interface IBackgroundTaskQueue
    {
        void QueueBackgroundWorkItem(Func<CancellationToken, Task> workItem);
        Task<Func<CancellationToken, Task>> DequeueAsync(CancellationToken cancellationToken);

        void OnStopping();
    }
    public class BackgroundTaskQueue : IBackgroundTaskQueue
    {

        private readonly Channel<Func<CancellationToken, Task>> _queue =
            Channel.CreateUnbounded<Func<CancellationToken, Task>>();

        public void QueueBackgroundWorkItem(Func<CancellationToken, Task> workItem)
        {
            if (workItem == null) throw new ArgumentNullException(nameof(workItem));
            _queue.Writer.TryWrite(workItem);
        }

        public async Task<Func<CancellationToken, Task>> DequeueAsync(CancellationToken cancellationToken)
        {
            return await _queue.Reader.ReadAsync(cancellationToken);
        }
        public void OnStopping()
        {
            _queue.Writer.Complete();
        }
    }


    public class HostedService : BackgroundService
    {
        private readonly IHostApplicationLifetime _appLifetime;

        private ulong tick = 0;
        private readonly IBackgroundTaskQueue _taskQueue;
        private readonly IServiceProvider _serviceProvider;
        private List<XTask> _updaters = new List<XTask>();
        private readonly ILogger<HostedService> _logger;

        private readonly TimeSpan _interval;
        public HostedService(IServiceProvider services, IBackgroundTaskQueue taskQueue, ILogger<HostedService> logger)
        {
            _serviceProvider = services;
            _taskQueue = taskQueue;
            _interval = TimeSpan.FromSeconds(1); // thời gian chạy task định kỳ
            _logger = logger;
            _appLifetime = services.GetRequiredService<IHostApplicationLifetime>();

        }
        private ulong GetTick()
        {
            return tick + 1;
        }
        public override Task StartAsync(CancellationToken cancellationToken)
        {
            Init();
            _appLifetime.ApplicationStopping.Register(OnStopping);
            base.StartAsync(cancellationToken);
            return Task.CompletedTask;
        }

        private void OnStopping()
        {
            // Thực hiện cleanup: đóng DB, flush log, v.v.
            _logger.LogInformation("Unified Background Service is stopping.");


        }
        private void Update(object? state) // Call every 1 second
        {
            tick = GetTick();
            // Console.WriteLine(tick);
            //Implement Task Update Here
            for (int i = 0; i < _updaters.Count; i++)
            {
                _updaters[i].Update(tick);
            }
        }

        void Init()
        {

            var tasks = new XTask(second: 60);
            tasks.Register(UpdateViewChapter);
            tasks.Register(UpdateExp);

            var tasks2 = new XTask(second: 60);
            tasks2.Register(UpdateViewComic);
            AddUpdater(tasks);
            AddUpdater(tasks2);

            // var task3 = new XTask(second: 1);
            // task3.Register(UpdateTaskQueue);
            // AddUpdater(task3);

        }

        // async void UpdateTaskQueue()
        // {
        //     var task = await _queueService.DequeueAsync(this.cancellationToken);
        //     if (task != null)
        //     {
        //         using (var scope = _services.CreateScope())
        //         {
        //             await task(scope.ServiceProvider, this.cancellationToken);
        //         }
        //     }
        // }

        async void UpdateViewComic()
        {

            using (var scope = _serviceProvider.CreateScope())
            {
                var comicService = scope.ServiceProvider.GetRequiredService<IComicService>();
                await comicService.SyncViewComic();

            }

        }
        async void UpdateViewChapter()
        {

            using (var scope = _serviceProvider.CreateScope())
            {
                var comicService = scope.ServiceProvider.GetRequiredService<IComicService>();
                await comicService.SyncViewChapter();

            }

        }
        async void UpdateExp()
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
                await userService.SyncUserExp();
            }

        }
        public void AddUpdater(XTask updater)
        {
            _updaters.Add(updater);
        }

        public void RemoveUpdater(XTask updater)
        {
            _updaters.Remove(updater);
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Unified Background Service is starting.");
            var intervalTask = RunIntervalTaskAsync(stoppingToken);
            var queueTask = ProcessQueueAsync(stoppingToken);

            await Task.WhenAll(intervalTask, queueTask);
            _logger.LogInformation("Unified Background Service is stopping.");
        }

        private async Task RunIntervalTaskAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    this.Update(tick);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "");
                }
                try
                {
                    await Task.Delay(_interval, stoppingToken);

                }
                catch (TaskCanceledException ex)
                {
                    _logger.LogInformation("Task canceled");
                }
            }
        }

        private async Task ProcessQueueAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var workItem = await _taskQueue.DequeueAsync(stoppingToken);
                    await workItem(stoppingToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred executing queued task.");
                }
            }
        }
    }
}