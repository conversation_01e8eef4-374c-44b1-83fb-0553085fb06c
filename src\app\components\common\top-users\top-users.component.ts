import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, Inject, OnInit, PLATFORM_ID, signal } from '@angular/core';
import { NumeralPipe } from '@pines/numeral.pipe';
import { IUserLite, LeaderboardService } from '@services/leaderboard.service';
import { LevelService } from '@services/level.service';
import { BaseComponent } from '../base/component-base';

@Component({
  selector: 'app-top-users',
  standalone: true,
  imports: [CommonModule,NumeralPipe],
  templateUrl: './top-users.component.html',
  styleUrl: './top-users.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TopUsersComponent extends BaseComponent implements OnInit {
  // Signals for reactive state
  private readonly topUsersSignal = signal<IUserLite[]>([]);
  private readonly isLoadingSignal = signal<boolean>(false);

  // Computed properties
  readonly topUsers = computed(() => this.topUsersSignal());
  readonly isLoading = computed(() => this.isLoadingSignal());
  readonly hasUsers = computed(() => this.topUsers().length > 0);

  constructor(
    private leaderboardService: LeaderboardService,
    @Inject(PLATFORM_ID) protected override platformId: object,
    private levelService : LevelService
  ) {
    super(platformId);
  }

  ngOnInit(): void {
    this.runInBrowser(() => {
      this.loadTopUsers();
    });
  }

  private loadTopUsers(): void {
    this.isLoadingSignal.set(true);
    this.leaderboardService.getTop5Users()
      .subscribe({
        next: (response) => {
          if (response.status === 1 && response.data) {
            this.topUsersSignal.set(response.data);
          } else {
          }
          this.isLoadingSignal.set(false);
        },
        error: (error) => {
          console.error('Error loading top users:', error);
          this.isLoadingSignal.set(false);
        }
      })

  }


  onUserClick(user: IUserLite): void {
    // Navigate to user profile or show user details
    // console.log('User clicked:', user);
    // You can implement navigation here
    // this.router.navigate(['/user', user.id]);
  }

  trackByUserId(index: number, user: IUserLite): number {
    return user.id;
  }

  getRankClass(rank: number): string {
    switch (rank) {
      case 1:
        return 'rank-gold';
      case 2:
        return 'rank-silver';
      case 3:
        return 'rank-bronze';
      default:
        return 'rank-default';
    }
  }

  getLevelTitle( experience: number, typeLevel: number): string {
    return this.levelService.getLevel(experience, typeLevel);
  }

  getUserName(user: IUserLite): string {
    return user.firstName + ' ' + (user.lastName ?? '');
  }


  getDefaultAvatar(): string {
    return '/default_avatar.jpg';
  }

  onImageError(event: any): void {
    event.target.src = this.getDefaultAvatar();
  }
}
