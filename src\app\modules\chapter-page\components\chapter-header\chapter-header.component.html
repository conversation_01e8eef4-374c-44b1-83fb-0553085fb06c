<!-- Chapter Header -->
<section class="chapter-header-container">
  <div class="chapter-header-card">
    <!-- Chapter Info -->
    <div class="chapter-info-section">
      <!-- Report Error Button -->
      <button class="report-error-button" (click)="onReportError()">
        <svg class="report-icon" viewBox="0 0 24 24">
          <path
            d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
          />
          <line x1="12" y1="9" x2="12" y2="13" />
          <line x1="12" y1="17" x2="12.01" y2="17" />
        </svg>
        <span class="report-text">Báo lỗi</span>
      </button>

      <!-- Comic Title -->
      <div class="comic-title-section">
        <h1 class="comic-title">
          <a
            [routerLink]="comicTitleLink()"
            [title]="comic.title"
            class="comic-title-link"
          >
            {{ comic.title }}
          </a>
        </h1>
      </div>

      <!-- Chapter Details -->
      <div class="chapter-details">
        <h2 class="chapter-title">{{ mainChapter.title }}</h2>
        <time class="chapter-date" [dateTime]="mainChapter.updateAt | date : 'yyyy-MM-dd'">
          Đăng lúc: {{ mainChapter.updateAt | date : 'dd/MM/yyyy' }}
        </time>
      </div>
    </div>

    <!-- Server Selection -->
    <div class="server-selection-section">
      <div class="server-list">
        <button
          *ngFor="
            let serverId of visibleServers();
            let i = index;
            trackBy: trackByServerId
          "
          (click)="onServerChange(serverId, i)"
          class="server-button"
          [class.server-button-active]="i === selectedServerId"
        >
          <svg class="server-icon" viewBox="0 0 24 24">
            <path d="M7 18a4.6 4.4 0 0 1 0 -9h0a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12" />
          </svg>
          <span class="server-text">Server {{ i + 1 }}</span>
        </button>

        <button
          (click)="onToggleServers()"
          *ngIf="hasMoreServers()"
          class="server-expand-button"
        >
          <svg
            class="expand-icon"
            [class.expand-icon-rotated]="showAllServers"
            viewBox="0 0 24 24"
          >
            <path d="M18 15l-6-6l-6 6h12" />
          </svg>
        </button>
      </div>
    </div>

    <div id="position-btn"></div>
  </div>
</section>
