import {
  ChangeDetectorRef,
  Component,
  Inject,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { isPlatformServer } from '@angular/common';
import { Comic, ComicList, IServiceResponse } from '@schema';
import { ComicService } from '@services/comic.service';
import { SeoService } from '@services/seo.service';
import { Subscription } from 'rxjs';
import { UrlService } from '@services/url.service';

@Component({
    selector: 'app-comic-hot',
    templateUrl: './comic-hot.component.html',
    styleUrl: './comic-hot.component.scss',
    standalone: false
})
export class ComicHotComponent implements OnInit {
  listComics?: Comic[] | null;
  totalpage!: number;
  page = 1;
  subscription!: Subscription;
  constructor(
    private comicService: ComicService,
    private route: ActivatedRoute,
    private router: Router,
    private cd: ChangeDetectorRef,
    private urlService: UrlService,
    private seoService: SeoService,
    @Inject(PLATFORM_ID) private platformId: object,
  ) {
    this.SetupSeo();
  }

  ssr() {
    return isPlatformServer(this.platformId);
  }
  ngOnInit(): void {
    this.route.queryParams.subscribe((params) => {
      this.page = Number(params['page']) || 1;
      this.FetchTopComics();
    });
  }
  FetchTopComics() {
    this.listComics = [];
    this.comicService.getHotComics(this.page).subscribe((res: IServiceResponse<ComicList>) => {
      if (res.data) {
        this.listComics = res.data.comics;
        this.totalpage = res.data.totalpage;
        this.cd.detectChanges();
      }
    });
  }

  onChangePage(page: number) {
    this.page = page;
    this.router.navigate([], {
      queryParams: { page: page },
      queryParamsHandling: 'merge',
      fragment: 'listComic',
    });
  }

  SetupSeo() {
    this.seoService.setTitle('Truyện tranh hot tại Mê truyện mới - MeTruyenMoi');
    this.seoService.addTags([
      {
        name: 'description',
        content:
          'Truyện tranh hot - Tất cả truyện đang hót nhất hiện nay đều có thể tìm thể tại Mê truyện mới - MeTruyenMoi',
      },
      {
        property: 'og:title',
        content: 'Truyện tranh hot tại Mê truyện mới - MeTruyenMoi',
      },
      {
        property: 'og:description',
        content:
          'Truyện tranh hot - Tất cả truyện đang hót nhất hiện nay đều có thể tìm thể tại Mê truyện mới - MeTruyenMoi',
      },
    ]);
    this.seoService.updateLink('canonical', `${this.urlService.BASE_URL}/truyen-hot`);

  }
}
