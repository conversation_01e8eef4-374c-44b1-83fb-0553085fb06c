import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { RouterLink } from '@angular/router';
import { Comic } from '@schema';
import { Subscription, timer } from 'rxjs';

@Component({
  selector: 'app-carousel-layout',
  templateUrl: './carousel-layout.component.html',
  styleUrl: './carousel-layout.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, RouterLink]
})
export class CarouselLayoutComponent {
  images: string[] = [];
  classes = [
    "grid-item col-span-1 row-span-2 col-start-1 row-start-1",
    "grid-item col-span-1 row-span-2 col-start-1 row-start-3 w-full",
    "grid-item col-span-2 col-start-2 row-span-full",
    "grid-item col-span-1 row-span-2 col-start-4 row-start-1",
    "grid-item col-span-1 row-span-2 col-start-4 row-start-3",
  ]
  @Input() comics?: Comic[] = [];
  @Output() comicHover = new EventEmitter<Comic>();
  timer?: Subscription
  trackByComicId(index: number, comic: Comic): number {
    return comic.id;
  }
  constructor() {
  }

  // ngOnInit(): void {
  //   this.preloadImages();
  // }

  // preloadImages(): void {
  //   this.comics?.forEach((comic, index) => {
  //   });
  // }
  OnComicHover(comic: Comic) {
    this.timer?.unsubscribe();
    this.timer = timer(750).subscribe(() => {
      this.comicHover.emit(comic);
    })
  }
  OnComicLeave() {
    this.comicHover.emit(undefined);
    this.timer?.unsubscribe();
  }
}
