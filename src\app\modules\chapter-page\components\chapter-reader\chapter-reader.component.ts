import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Inject,
  Input,
  OnInit,
  Output,
  PLATFORM_ID,
  ViewChild,
  computed,
  signal
} from '@angular/core';
import { OptimizedBaseComponent } from '@components/common/base/optimized-base.component';
import { Page } from '@schema';

export interface ScrollEvent {
  direction: number; // -1 for left/up, 1 for right/down
}

export interface ImageEvent {
  type: 'load' | 'error';
  event: Event;
}

@Component({
  selector: 'app-chapter-reader',
  templateUrl: './chapter-reader.component.html',
  styleUrl: './chapter-reader.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: false
})
export class ChapterReaderComponent extends OptimizedBaseComponent implements OnInit {
  // Input properties
  @Input() listImgs: Page[] = [];
  @Input() isLoading = false;
  @Input() isVertical = true;
  @Input() isNightMode = false;
  @Input() isFullScreen = false;
  @Input() zoomLevel = 0;
  @Input() defaultWidth = 0;
  @Input() comicUrl = '';

  // Output events
  @Output() scroll = new EventEmitter<ScrollEvent>();
  @Output() toggleFullscreen = new EventEmitter<void>();
  @Output() imageEvent = new EventEmitter<ImageEvent>();

  // ViewChild references
  @ViewChild('imageContainer', { static: true }) imageContainer!: ElementRef;

  // Component state
  private readonly _visibleImages = signal<Page[]>([]);
  private readonly _imageWidth = signal(0);

  // Computed properties
  readonly visibleImages = computed(() => {
    return this.listImgs || [];
  });

  readonly imageWidth = computed(() => {
    return this.defaultWidth * (1 + this.zoomLevel);
  });

  readonly containerClasses = computed(() => ({
    'scroll-navigation-hidden': this.isVertical || this.isFullScreen
  }));

  readonly skeletonPages = computed(() => [1, 2, 3, 4, 5]);

  constructor(
    protected override cd: ChangeDetectorRef,
    @Inject(PLATFORM_ID) protected override platformId: object
  ) {
    super(cd, platformId);
  }

  ngOnInit(): void {
    this.updateComputedValues();
  }

  // Scroll methods
  onScrollHorizontal(direction: number): void {
    this.scroll.emit({ direction });
  }

  // Image event handlers
  onImageLoad(event: Event): void {
    const imageEle = event.target as HTMLImageElement;
    imageEle.onload = null;
    imageEle.classList.remove('loading-state');
    this.imageEvent.emit({ type: 'load', event });
  }

  onImageError(event: Event): void {
    const imageEle = event.target as HTMLImageElement;
    imageEle.onerror = null;
    imageEle.classList.add('error-state');
    this.imageEvent.emit({ type: 'error', event });
  }

  onDoubleClick(): void {
    this.toggleFullscreen.emit();
  }

  // TrackBy functions for performance
  trackByPageUrl = (index: number, page: Page): string => {
    return page?.url ?? index.toString();
  };

  trackBySkeletonIndex = (index: number): number => index;

  // Image loading strategy
  getImageLoadingStrategy(index: number): 'eager' | 'lazy' {
    return index <= 5 ? 'eager' : 'lazy';
  }

  // Image classes
  getImageClasses(index: number): Record<string, boolean> {
    return {
      'chapter-page-horizontal': !this.isVertical,
      'night-mode': this.isNightMode
    };
  }

  // Alt text generation
  getImageAlt(index: number): string {
    return `${this.comicUrl}-chapter-page-${index + 1}`;
  }

  private updateComputedValues(): void {
    this._visibleImages.set(this.visibleImages());
    this._imageWidth.set(this.imageWidth());
  }
}
