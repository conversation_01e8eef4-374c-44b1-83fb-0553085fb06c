import { inject } from '@angular/core';
import { ResolveFn } from '@angular/router';
import { AccountService } from '@services/account.service';
import { ComicService } from '@services/comic.service';
import { catchError, map, of } from 'rxjs';




export const ComicResolver: ResolveFn<any> = (route) => {
  let id = route.paramMap.get('id');
  id = id?.split('-').pop() || '';
  if (!Number.isInteger(Number(id)))
    return of(null);
  return inject(ComicService).getComicById(id)
    .pipe(
      map((res) => {
        return res;
      }),
      catchError((err) => {
        return of(null);
      })
    );
};
export const ChapterImgsResolver: ResolveFn<any> = (route) => {
  const id = Number(route.paramMap.get('chapterid'));
  if (!Number.isInteger(Number(id)))
    return of(null);
  return inject(ComicService).getChapterImgs(id)
    .pipe(
      map((res) => {
        return res;
      }),
      catchError((err) => {
        return of(null);
      })
    );
};

export const FollowedComicResolver: ResolveFn<any> = (route, state) => {
  return inject(AccountService).GetFollowedComics();
};


export const RecommendComicResolver: ResolveFn<any> = (route, state) => {
  return inject(ComicService).getRecommendComics();
};



export const HotComicResolver: ResolveFn<any> = (route, state) => {
  const page = Number(route.paramMap.get('page')) || 1;
  return inject(ComicService).getHotComics(page);
};


