<div
  class="flex justify-center items-center absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
>
  <svg
    class="animate-spin text-neutral-400 opacity-90"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    stroke-width="2"
    stroke="currentColor"
    fill="none"
    stroke-linecap="round"
    stroke-linejoin="round"
    [ngStyle]="{ 'width.px': sizeSpinner, 'height.px': sizeSpinner }"
  >
    <path stroke="none" d="M0 0h24v24H0z" />
    <circle cx="12" cy="12" r="3" />
    <line x1="12" y1="21" x2="12" y2="21.01" />
    <line x1="3" y1="9" x2="3" y2="9.01" />
    <line x1="21" y1="9" x2="21" y2="9.01" />
    <path d="M8 20.1a9 9 0 0 1 -5 -7.1" />
    <path d="M16 20.1a9 9 0 0 0 5 -7.1" />
    <path d="M6.2 5a9 9 0 0 1 11.4 0" />
  </svg>
  <span class="sr-only">Loading...</span>
</div>
