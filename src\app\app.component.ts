import { isPlatformBrowser, isPlatformServer, ViewportScroller } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, PLATFORM_ID, ViewContainerRef } from '@angular/core';
import {
  Router,
  Scroll,
} from '@angular/router';
import { ToastService } from '@services/toast.service';
import { VersionCheckService } from '@services/version.service';
import { filter } from 'rxjs';
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  standalone: false,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AppComponent {
  isBrowser = false;
  constructor(
    private router: Router,
    private viewportScroller: ViewportScroller,
    private toastService: ToastService,
    private vcRef: ViewContainerRef,
    private cd: ChangeDetectorRef,
    private versionCheckService: VersionCheckService,
    @Inject(PLATFORM_ID) private platformId: object,
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
    if (isPlatformServer(this.platformId)) return;
    this.toastService.viewContainerRef = this.vcRef;
    this.router.events
      .pipe(filter((e) => e instanceof Scroll))
      .subscribe((e: any) => {
        if (e.position) {
          // backward navigation
          window.scrollTo({
            top: e.position[1],
            left: e.position[0],
            behavior: 'instant',
          }); // scrollToPosition(e.position);
        } else if (e.anchor) {
          viewportScroller.scrollToAnchor(e.anchor);
        } else {
          window.scrollTo({ top: 0, left: 0, behavior: 'instant' });
        }
      });

  }
  ngOnInit() {
    if (isPlatformServer(this.platformId)) return;
    this.versionCheckService.checkVersionPeriodically();
  }
}
