import { computed, Injectable, signal } from '@angular/core';
import {  } from '@schema';
import {  Subject } from 'rxjs';
import { ENHANCED_SETTINGS } from '../components/lazy/app-setting/constants/enhanced-setting-constants';
import {
  EnhancedSettingOption,
  SettingCategory,
  SettingChangeEvent,
  SettingGroup
} from '../components/lazy/app-setting/interfaces/setting-interfaces';
import { StorageService } from './storage.service';

@Injectable({
  providedIn: 'root'
})
export class SettingService {
  // Legacy support

  // Enhanced settings with signals
  private enhancedSettingsSignal = signal<Map<string, EnhancedSettingOption>>(new Map());
  private settingValuesSignal = signal<Map<string, any>>(new Map());
  private settingChangeSubject = new Subject<SettingChangeEvent>();

  // Computed properties
  readonly enhancedSettings = computed(() => this.enhancedSettingsSignal());
  readonly settingValues = computed(() => this.settingValuesSignal());
  public settingChanges$ = this.settingChangeSubject.asObservable();

  // Setting groups computed
  readonly settingGroups = computed(() => {
    const settings = this.enhancedSettingsSignal();
    const groups = new Map<SettingCategory, SettingGroup>();

    settings.forEach(setting => {
      if (!groups.has(setting.category)) {
        groups.set(setting.category, {
          id: setting.category,
          category: setting.category,
          name: this.getCategoryName(setting.category),
          description: this.getCategoryDescription(setting.category),
          icon: this.getCategoryIcon(setting.category),
          order: this.getCategoryOrder(setting.category),
          settings: []
        });
      }
      groups.get(setting.category)!.settings.push(setting);
    });

    // Sort settings within each group
    groups.forEach(group => {
      group.settings.sort((a, b) => a.order - b.order);
    });

    return Array.from(groups.values()).sort((a, b) => a.order - b.order);
  });

  constructor(private storageService: StorageService) {
    this.initializeSettings();
    this.loadSettingsFromStorage();
  }

  // Initialize enhanced settings
  private initializeSettings(): void {
    const settingsMap = new Map<string, EnhancedSettingOption>();
    const valuesMap = new Map<string, any>();

    ENHANCED_SETTINGS.forEach(setting => {
      settingsMap.set(setting.id, { ...setting });
      valuesMap.set(setting.id, setting.defaultValue);
    });

    this.enhancedSettingsSignal.set(settingsMap);
    this.settingValuesSignal.set(valuesMap);
  }

  // Load settings from storage
  private loadSettingsFromStorage(): void {
    // Try to load enhanced settings first
    if (typeof localStorage !== 'undefined') {
      const enhancedSettings = localStorage.getItem('enhanced-settings');
      if (enhancedSettings) {
        try {
          const parsed = JSON.parse(enhancedSettings);
          const currentValues = new Map(this.settingValuesSignal());

          Object.entries(parsed).forEach(([key, value]) => {
            if (currentValues.has(key)) {
              currentValues.set(key, value);
            }
          });

          this.settingValuesSignal.set(currentValues);
          return;
        } catch (error) {
          console.warn('Failed to parse enhanced settings:', error);
        }
      }
    }

    // Fallback to legacy settings
    const legacySettings = this.storageService.GetSetting();
    if (legacySettings) {
      // Convert legacy settings to enhanced format
      this.migrateLegacySettings(legacySettings);
    }
  }

  private migrateLegacySettings(legacySettings: any): void {
    // Migration logic for legacy settings
    const currentValues = new Map(this.settingValuesSignal());

    // Map legacy setting keys to enhanced setting IDs
    const migrationMap: Record<string, string> = {
      'theme': 'theme',
      'language': 'language',
      'fontFamily': 'fontFamily',
      'lineHeight': 'lineHeight',
      'scrollSpeed': 'scrollSpeed',
      'fixedToolbar': 'fixedToolbar'
    };

    Object.entries(migrationMap).forEach(([legacyKey, enhancedKey]) => {
      if (legacySettings[legacyKey] !== undefined && currentValues.has(enhancedKey)) {
        currentValues.set(enhancedKey, legacySettings[legacyKey]);
      }
    });

    this.settingValuesSignal.set(currentValues);
  }

  // Category helper methods
  private getCategoryName(category: SettingCategory): string {
    const names = {
      [SettingCategory.APPEARANCE]: 'Giao diện',
      [SettingCategory.READING]: 'Đọc truyện',
      [SettingCategory.BEHAVIOR]: 'Hành vi',
      [SettingCategory.ACCESSIBILITY]: 'Trợ năng',
      // [SettingCategory.ADVANCED]: 'Nâng cao'
    };
    return names[category] || category;
  }

  private getCategoryDescription(category: SettingCategory): string {
    const descriptions = {
      [SettingCategory.APPEARANCE]: 'Tùy chỉnh giao diện và hiển thị',
      [SettingCategory.READING]: 'Cài đặt trải nghiệm đọc truyện tranh',
      [SettingCategory.BEHAVIOR]: 'Hành vi và tương tác của ứng dụng',
      [SettingCategory.ACCESSIBILITY]: 'Hỗ trợ trợ năng và khả năng tiếp cận',
      // [SettingCategory.ADVANCED]: 'Cài đặt nâng cao cho người dùng chuyên nghiệp'
    };
    return descriptions[category] || '';
  }

  private getCategoryIcon(category: SettingCategory): string {
    const icons = {
      [SettingCategory.APPEARANCE]: 'palette',
      [SettingCategory.READING]: 'book-open',
      [SettingCategory.BEHAVIOR]: 'settings',
      [SettingCategory.ACCESSIBILITY]: 'accessibility',
      // [SettingCategory.ADVANCED]: 'tool'
    };
    return icons[category] || 'settings';
  }

  private getCategoryOrder(category: SettingCategory): number {
    const orders = {
      [SettingCategory.APPEARANCE]: 1,
      [SettingCategory.READING]: 2,
      [SettingCategory.BEHAVIOR]: 3,
      [SettingCategory.ACCESSIBILITY]: 4,
      // [SettingCategory.ADVANCED]: 5
    };
    return orders[category] || 999;
  }

  // Enhanced setting methods
  getEnhancedSetting(id: string): EnhancedSettingOption | undefined {
    return this.enhancedSettingsSignal().get(id);
  }

  getSettingValue(id: string): any {
    return this.settingValuesSignal().get(id);
  }

  setSettingValue(id: string, value: any): void {
    const setting = this.getEnhancedSetting(id);
    if (!setting) return;

    const oldValue = this.getSettingValue(id);
    const currentValues = new Map(this.settingValuesSignal());
    currentValues.set(id, value);
    this.settingValuesSignal.set(currentValues);
    // Emit change event
    this.settingChangeSubject.next({
      settingId: id,
      oldValue,
      newValue: value,
      timestamp: new Date()
    });

    // Save to storage
    this.saveSettingsToStorage();
  }

  private saveSettingsToStorage(): void {
    const settings: Record<string, any> = {};
    this.settingValuesSignal().forEach((value, key) => {
      settings[key] = value;
    });

    // Save enhanced settings to storage
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('enhanced-settings', JSON.stringify(settings));
    }

    // Maintain legacy compatibility
    this.storageService.SaveSetting(this.settingValuesSignal());
  }



  IsDarkTheme(): boolean {
    const themeValue = this.getSettingValue('theme');
    return themeValue === 'dark';
  }

}
