
using System.Security.Claims;
using ComicAPI.Models;
using ComicAPI.Reposibility;
using ComicAPI.Services;

namespace ComicAPI.Middleware;

public class UserMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UserMiddleware> _logger;

    public UserMiddleware(RequestDelegate next, ILogger<UserMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(
        HttpContext context,
        IUserService userService,
        IUserReposibility userReposibility,
        IUserActivityService userActivityService)
    {
        if (context.User?.Identity?.IsAuthenticated ?? false)
        {
            if (userService != null)
            {
                var strId = context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (int.TryParse(strId, out var userId))
                {
                    User? user = await userReposibility.GetUser(userId);
                    if (user != null)
                    {
                        userService.CurrentUser = user;

                        // Track user activity
                        await TrackUserActivity(context, userId, userActivityService);
                    }
                    else
                    {
                        context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                        await context.Response.WriteAsync("Unauthorized: Invalid user.");
                        return;
                    }
                }
                else
                {
                    _logger.LogWarning("Invalid user ID format in claims: {UserId}", strId);
                    context.Response.StatusCode = StatusCodes.Status401Unauthorized;
                    await context.Response.WriteAsync("Unauthorized: Invalid user ID.");
                    return;
                }
            }
        }

        await _next(context);
    }

    private async Task TrackUserActivity(HttpContext context, int userId, IUserActivityService userActivityService)
    {
        try
        {
            var ipAddress = GetClientIpAddress(context);
            var userAgent = context.Request.Headers.UserAgent.ToString();
            var endpoint = $"{context.Request.Method} {context.Request.Path}";

            // Skip tracking for certain endpoints to reduce noise
            if (ShouldSkipTracking(context.Request.Path))
            {
                return;
            }

            await userActivityService.TrackUserActivityAsync(userId, ipAddress, userAgent, endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error tracking user activity for UserId: {UserId}", userId);
            // Don't fail the request if activity tracking fails
        }
    }

    private static string? GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (for load balancers/proxies)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        // Fallback to connection remote IP
        return context.Connection.RemoteIpAddress?.ToString();
    }

    private static bool ShouldSkipTracking(PathString path)
    {
        var pathValue = path.Value?.ToLowerInvariant();
        if (string.IsNullOrEmpty(pathValue)) return true;

        // Skip tracking for these endpoints to reduce noise
        var skipPaths = new[]
        {
            "/health",
            "/metrics",
            "/favicon.ico",
            "/robots.txt",
            "/api/image/", // Image requests
            "/static/",    // Static files
            "/swagger"     // Swagger UI
        };

        return skipPaths.Any(skipPath => pathValue.Contains(skipPath));
    }
}
