const axios = require('axios')

const getPages = async () => {
    const urls = ['https://s135.hinhhinh.com/15756/48/0.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/1.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/2.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/3.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/4.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/5.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/6.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/7.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/8.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/9.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/10.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/11.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/12.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/13.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/14.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/15.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/16.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/17.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/18.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/19.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/20.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/21.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/22.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/23.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/24.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/25.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/26.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/27.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/28.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/29.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/30.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/31.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/32.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/33.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/34.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/35.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/36.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/37.jpg?gt=hdfgdfg', 'https://s135.hinhhinh.com/15756/48/38.jpg?gt=hdfgdfg']
    
    const urlMaps = urls.map(url => axios.get(url,{
        headers: {
            'Referer': 'truyenqqgo.com'
        }
    }))
    const res = await Promise.all(urlMaps)
    const data = res.map(r => r.data).filter(d => d != null)
    
    // console.log(data)
}
const stressTest = async () => {
    const getPage = []
    for (let i = 0; i < 2; i++) {
        getPage.push(getPages())
    }
console.time('test')
    await Promise.all(getPage)
console.timeEnd('test')
}
stressTest()