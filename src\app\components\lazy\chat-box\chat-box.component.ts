import { CommonModule } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  OnInit,
  ViewChild,
  ViewEncapsulation
} from '@angular/core';
import { EmojiComponent } from '@components/lazy/emoji/emoji.component';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { Chanel, IConversation, IMessage, IUser } from '@schema';
import { AccountService } from '@services/account.service';
import { ChatService } from '@services/chat.service';
import { ToastService, ToastType } from '@services/toast.service';
import { IPopupComponent } from 'src/app/core/interface';
@Component({
  imports: [CommonModule, EmojiComponent, ClickOutsideDirective,],
  selector: 'app-chat-box',
  templateUrl: './chat-box.component.html',
  styleUrl: './chat-box.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class ChatBoxComponent implements OnInit, AfterViewInit, IPopupComponent {
  hideChatChange = new EventEmitter<boolean>();
  isShowChatBox = true;
  isShowChatMenu = false;
  activeEmojiPicker = false;
  @ViewChild('chatBox') chatBoxRef?: ElementRef;
  @ViewChild('chatMenu') chatMenuRef?: ElementRef;
  loginUser: IUser | undefined = undefined
  conversations: IConversation[] = [];
  currentConversation: IConversation | null = null;
  messages: IMessage[] = [];
  textMessage: string | null = '';
  constructor(
    private cdref: ChangeDetectorRef,
    private chatService: ChatService,
    private accountService: AccountService,
    private toastService: ToastService,
  ) {

  }
  show(object: any): Promise<any> {
    this.isShowChatBox = true;
    this.cdref.detectChanges();
    return new Promise((resolve) => {
      resolve({});
    });
  }
  ngOnInit(): void {
    this.accountService.GetLocalUser().subscribe((user) => {
      this.loginUser = user

      this.loadConversations();
    });

  }
  ngAfterViewInit() {
    this.scrollToBottom();
  }



  toggleEmojiPicker() {
    this.activeEmojiPicker = !this.activeEmojiPicker;
  }

  sendMessage() {
    if (this.loginUser == null) {
      this.toastService.show(ToastType.Error, 'Vui lòng đăng nhập để sử dụng ChatBot!')
      return
    }
    // Check if we have a current conversation, use real chat
    if (this.currentConversation) {
      this.sendRealMessage();
      return;
    }
    // Fallback to old chatbot logic for AI Chat bot



  }
  processTemplate(text: string) {
    // 1. Chuyển đổi tiêu đề (H1, H2)
    // Cần xử lý từ lớn đến nhỏ để tránh trùng khớp sai
    text = text.replace(/^##\s*(.*)$/gm, '<h2>$1</h2>'); // H2
    text = text.replace(/^#\s*(.*)$/gm, '<h1>$1</h1>');  // H1

    // 2. In đậm: **text** hoặc __text__
    text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    text = text.replace(/__(.*?)__/g, '<strong>$1</strong>');
    // 3. In nghiêng: *text* hoặc _text_
    text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
    text = text.replace(/_(.*?)_/g, '<em>$1</em>');
    text = text.replace(/\n/g, '<br>');
    return text;
  }
  closeChat() {
    this.hideChatChange.emit(true);
  }
  setVisible(isVisible: boolean) {
    this.isShowChatBox = isVisible;
  }

  msgChange(event: any) {
    this.textMessage = event.target.value;
  }

  addEmoji(event: any): void {
    return;
  }

  scrollToBottom() {
    // const chatBox = document.getElementById('chatBox');
    const chatBox = this.chatBoxRef?.nativeElement;
    chatBox?.scrollTo({
      top: chatBox.scrollHeight,
      left: 0,
      behavior: 'smooth',
    });
  }

  // New methods for real chat functionality
  loadConversations() {
    this.chatService.getMyConversations().subscribe({
      next: (response) => {
        if (response.status === 1 && response.data) {
          this.conversations = response.data;
          this.currentConversation = this.conversations[0];
          if (this.currentConversation.channel == Chanel.Bot) {
            this.messages = [this.getChatBotInitMsg!];
          }

          this.cdref.detectChanges();
        }
      },
      error: (error) => {
        console.error('Error loading conversations:', error);
      }
    });


  }
  get getChatBotInitMsg(): IMessage | null {
    if (this.currentConversation?.channel != Chanel.Bot) return null;
    return {
      id: '',
      userId: this.currentConversation.hostId,
      content: `Chào <b>${this.loginUser?.firstName ?? 'Bạn'}</b> , mình sẽ trả lời bất kỳ câu hỏi nào của bạn liên quan đến truyện tranh ngay. 
                <br>Hãy ${this.loginUser ? '' : '<a class="text-blue-400 hover:underline" href="/auth/login">đăng nhập</a> để'} bắt đầu cuộc trò chuyện nào!
              `,
      conversationId: this.currentConversation.id,
      createdAt: new Date().toISOString(),
      user: {
        id: this.currentConversation.hostId,
        firstName: 'Chat Bot',
        avatar: '/option4.png',
        gender: 0,
      }
    }
  }

  selectConversation(conversation: IConversation) {
    if (conversation.id == this.currentConversation?.id) return;
    if (this.loginUser == null) {
      this.currentConversation = conversation;
      this.isShowChatMenu = false;
      if (conversation.channel == Chanel.Bot) {
        this.messages = [this.getChatBotInitMsg!];
      }
      else {
        this.messages = [];
      }
      this.cdref.detectChanges();

      return
    }
    this.chatService.getConversation(conversation.id).subscribe({
      next: (response) => {
        if (response.status === 1 && response.data) {
          this.currentConversation = response.data;
          this.messages = response.data.messages || [];
          this.messages.forEach((message) => {
            if (message.userId === this.loginUser!.id) {
              message.user = this.loginUser;
            }
          });
          if (this.currentConversation.channel == Chanel.Bot) {
            this.messages.push(this.getChatBotInitMsg!);
          }
          this.isShowChatMenu = false;
          this.cdref.detectChanges();
          this.scrollToBottom();
        }
      },
      error: (error) => {
        this.toastService.show(ToastType.Error, 'Không thể tải cuộc trò chuyện');
      }
    });
  }

  sendRealMessage() {
    if (!this.textMessage || !this.loginUser) return;

    const content = this.textMessage.trim();
    if (!content) return;

    // Check if this is world chat
    if (this.currentConversation) {
      // Send regular message
      const request = {
        content: content,
        conversationId: this.currentConversation.id,
      };

      this.chatService.sendMessage(request).subscribe({
        next: (response) => {
          if (response.status === 1 && response.data) {
            this.messages.push(response.data);
            this.textMessage = '';
            this.cdref.detectChanges();
            this.scrollToBottom();
            if (this.currentConversation!.channel == Chanel.Bot) {
              this.getBotMessage(response.data.id);
            }
          }
        },
        error: (error) => {
          console.error('Error sending message:', error);
          this.toastService.show(ToastType.Error, 'Không thể gửi tin nhắn');
        }
      });
    }
  }

  getBotMessage(userMessageId: string) {
    const eventSource = this.chatService.ChatWithBot(userMessageId);
    if (!eventSource) {
      this.toastService.show(ToastType.Error, 'Vui lòng đăng nhập để sử dụng Chat Box')
      return
    }
    let isFirstChunk = true;
    eventSource.onmessage = (event: any) => {
      let text = event.data
      try {
        const parsedData = JSON.parse(event.data);
        if (parsedData.text === '[DONE]') {
          eventSource.close();
        } else if (parsedData.text) {
          const partialText =
            this.processTemplate(parsedData.text);

          if (isFirstChunk) {
            let botmsg: IMessage =
            {
              id: '',
              userId: this.currentConversation!.hostId,
              content: partialText,
              conversationId: this.currentConversation!.id,
              createdAt: new Date().toISOString(),
              user: {
                id: this.currentConversation!.hostId,
                firstName: 'Chat Bot',
                avatar: '/option4.png',
                gender: 0,
              }
            }
            this.messages.push(botmsg);
            isFirstChunk = false;
          } else {
            this.messages[this.messages.length - 1].content += partialText;
          }


          this.cdref.detectChanges(); // Cập nhật giao diện
          this.scrollToBottom(); // Cuộn xuống cuối
        }
      } catch (e) {
        console.error('Failed to parse event data:', e);
      }
    };
    eventSource.onerror = (error: any) => {
      console.error('EventSource error:', error);
      eventSource.close();
    };
    this.textMessage = '';
    this.cdref.detectChanges();
    this.scrollToBottom();


  }



}
