import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RankComponent } from './rank.component';
import { RouterModule } from '@angular/router';
import { PaginationComponent } from '@components/common/pagination/pagination.component';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';
import { GridComicComponent } from '@components/common/grid-comic/grid-comic.component';
import { NumeralPipe } from '@pines/numeral.pipe';
import { ComicDescriptionlPipe } from '@pines/description.pipe';
import { ClickOutsideDirective } from '@directives/click-outside.directive';
import { Selection2Component } from '@components/common/selection-2/selection-2.component';



@NgModule({
  declarations: [RankComponent],
  imports: [
    RouterModule.forChild([{ path: '', component: RankComponent }]),
    CommonModule,
    PaginationComponent,
    BreadcrumbComponent,
    GridComicComponent,
    NumeralPipe,
    ComicDescriptionlPipe,
    ClickOutsideDirective,
    Selection2Component
  ]
})
export class RankModule { }
