import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';

import { ContactComponent } from './contact.component';

const routes: Routes = [
  {
    path: '',
    component: ContactComponent,
    data: {
      title: '<PERSON>ên hệ - Hỗ trợ và tư vấn',
      description: 'Liên hệ với đội ngũ hỗ trợ MeTruyenMoi. Chúng tôi sẵn sàng giải đáp mọi thắc mắc và hỗ trợ bạn 24/7.'
    }
  }
];

@NgModule({
  declarations: [
    ContactComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes)
  ]
})
export class ContactModule { }
