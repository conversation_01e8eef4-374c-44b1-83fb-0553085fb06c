<!-- Modern Pagination Component - Comic Website Design -->
<nav class="pagination-container" aria-label="Pagination navigation">
  <!-- Main Pagination Controls -->
  <div class="pagination-wrapper">
    <!-- Page Info -->
    <div class="pagination-info">
      <div class="page-info-content">
        <svg class="page-info-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
          <polyline points="10,9 9,9 8,9"/>
        </svg>
        <span class="page-info-text">
          Trang <span class="current-page-highlight">{{ currentPage }}</span>
          / <span class="total-pages">{{ totalpage }}</span>
        </span>
      </div>
    </div>

    <!-- Navigation Controls -->
    <div class="pagination-nav">
      <!-- Previous Button -->
      <button
        type="button"
        (click)="OnChangePage((currentPage - 1).toString())"
        class="pagination-prev"
        [disabled]="currentPage <= 1"
        [title]="'Trang trước'"
      >
        <svg class="pagination-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <polyline points="15,18 9,12 15,6"/>
        </svg>
        <span class="pagination-text">Trước</span>
      </button>

      <!-- Page Numbers -->
      <div class="pagination-pages">
        <button
          type="button"
          *ngFor="let page of pages; trackBy: trackByPage"
          (click)="OnChangePage(page)"
          class="pagination-page"
          [class.pagination-page-active]="currentPage.toString() === page"
          [class.pagination-ellipsis]="page === '...'"
          [attr.aria-current]="currentPage.toString() === page ? 'page' : null"
          [title]="page === '...' ? 'Nhấp để tìm kiếm trang' : 'Trang ' + page"
        >
          <span *ngIf="page !== '...'" class="page-number">{{ page }}</span>
          <svg *ngIf="page === '...'" class="ellipsis-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="1"/>
            <circle cx="19" cy="12" r="1"/>
            <circle cx="5" cy="12" r="1"/>
          </svg>
        </button>
      </div>

      <!-- Next Button -->
      <button
        type="button"
        (click)="OnChangePage((currentPage + 1).toString())"
        class="pagination-next"
        [disabled]="currentPage >= totalpage"
        [title]="'Trang sau'"
      >
        <span class="pagination-text">Sau</span>
        <svg class="pagination-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <polyline points="9,18 15,12 9,6"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- Enhanced Search Section -->
  <div *ngIf="showSearch" class="pagination-search">
    <div class="search-container">
      <div class="search-header">
        <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <span class="search-title">Tìm kiếm trang</span>
      </div>

      <div class="search-form">
        <div class="search-input-group">
          <label for="pageSearch" class="search-label">Nhập số trang:</label>
          <input
            id="pageSearch"
            type="number"
            name="pageSearch"
            [min]="1"
            [max]="totalpage"
            #searchInput
            class="search-input"
            [placeholder]="currentPage.toString()"
            (keyup.enter)="OnChangePage(searchInput.value)"
            required
          />
          <span class="search-range">1 - {{ totalpage }}</span>
        </div>

        <div class="search-actions">
          <button
            type="button"
            (click)="OnChangePage(searchInput.value)"
            class="search-go-button"
            [disabled]="!searchInput.value"
          >
            <svg class="search-go-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
            <span>Đi đến</span>
          </button>

          <button
            type="button"
            (click)="onFocus(false)"
            class="search-cancel-button"
          >
            <svg class="search-cancel-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
            <span>Hủy</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</nav>
