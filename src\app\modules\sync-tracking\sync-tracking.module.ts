import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';

// Main component
import { SyncTrackingComponent } from './sync-tracking.component';

// Sub-components
import { SyncFormComponent } from './components/sync-form/sync-form.component';
import { SyncProgressComponent } from './components/sync-progress/sync-progress.component';
import { SyncResultsComponent } from './components/sync-results/sync-results.component';

// Services
import { SyncTrackingService } from './services/sync-tracking.service';
import { BreadcrumbComponent } from '@components/common/breadcrumb/breadcrumb.component';

@NgModule({
  declarations: [
    SyncTrackingComponent,
    SyncFormComponent,
    SyncProgressComponent,
    SyncResultsComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    BreadcrumbComponent,
    RouterModule.forChild([
      {
        path: '',
        component: SyncTrackingComponent,
        data: {
          title: 'Đồng bộ theo dõi truyện',
          description: 'Đồng bộ danh sách theo dõi truyện giữa NetTruyen và TruyenQQ'
        }
      }
    ])
  ],
  providers: [
    SyncTrackingService
  ]
})
export class SyncTrackingModule { }
