

.item-card {
  @apply relative bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700;
  @apply transition-all duration-200 ease-in-out cursor-pointer;
  @apply hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600;
  @apply transform hover:scale-105;

  &--small {
    @apply p-2 min-h-[80px];
    
    .item-card__icon-container {
      @apply w-12 h-12;
    }
    
    .item-card__name {
      @apply text-sm font-medium;
    }
    
    .item-card__rarity-text {
      @apply text-xs;
    }
  }

  &--medium {
    @apply p-3 min-h-[120px];
    
    .item-card__icon-container {
      @apply w-16 h-16;
    }
    
    .item-card__name {
      @apply text-base font-semibold;
    }
  }

  &--large {
    @apply p-4 min-h-[160px];
    
    .item-card__icon-container {
      @apply w-20 h-20;
    }
    
    .item-card__name {
      @apply text-lg font-bold;
    }
  }

  // Rarity styles
  &--common {
    .item-card__rarity-border {
      @apply border-gray-400;
    }
  }

  &--uncommon {
    .item-card__rarity-border {
      @apply border-green-500;
    }
  }

  &--rare {
    .item-card__rarity-border {
      @apply border-blue-500;
    }
  }

  &--epic {
    .item-card__rarity-border {
      @apply border-purple-500;
    }
  }

  &--legendary {
    .item-card__rarity-border {
      @apply border-red-500;
    }
  }

  // State styles
  &--selected {
    @apply ring-2 ring-blue-500 border-blue-500;
  }

  &--hovered {
    @apply shadow-lg;
  }

  &--equipped {
    @apply bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600;
  }

  &--expired {
    @apply opacity-60 grayscale;
  }

  &--expiring {
    @apply border-yellow-400 dark:border-yellow-500;
  }
}

.item-card__selection {
  @apply absolute top-2 left-2 z-10;
}

.item-card__checkbox {
  @apply w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded;
  @apply focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800;
  @apply dark:bg-gray-700 dark:border-gray-600;
}

.item-card__icon {
  @apply flex justify-center mb-2;
}

.item-card__icon-container {
  @apply relative rounded-lg overflow-hidden;
}

.item-card__image {
  @apply w-full h-full object-cover;
}

.item-card__rarity-border {
  @apply absolute inset-0 border-2 rounded-lg pointer-events-none;
}

.item-card__equipped-badge {
  @apply absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full;
  @apply flex items-center justify-center text-white text-xs;
}

.item-card__quantity {
  @apply absolute -bottom-1 -right-1 bg-gray-900 text-white text-xs;
  @apply px-1.5 py-0.5 rounded-full min-w-[20px] text-center;
}

.item-card__expiry-warning {
  @apply absolute -top-1 -left-1 w-5 h-5 bg-yellow-500 rounded-full;
  @apply flex items-center justify-center text-white text-xs;
}

.item-card__expired-overlay {
  @apply absolute inset-0 bg-black bg-opacity-60 rounded-lg;
  @apply flex items-center justify-center text-white text-xs font-semibold;
}

.item-card__info {
  @apply flex-1 min-w-0;
}

.item-card__name {
  @apply text-gray-900 dark:text-gray-100 truncate mb-1;
}

.item-card__rarity {
  @apply mb-1;
}

.item-card__rarity-text {
  @apply text-gray-600 dark:text-gray-400 font-medium;
}

.item-card__category {
  @apply flex items-center gap-1 text-gray-500 dark:text-gray-400 text-sm mb-2;
}

.item-card__description {
  @apply text-gray-600 dark:text-gray-400 text-sm line-clamp-2 mb-2;
}

.item-card__expiry {
  @apply flex items-center gap-1 text-yellow-600 dark:text-yellow-400 text-xs;
}

.item-card__actions {
  @apply flex items-center gap-2 mt-2;
}

.item-card__quick-action {
  @apply flex items-center gap-1 px-2 py-1 rounded text-sm;
  @apply bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300;
  @apply hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;

  &--primary {
    @apply bg-blue-500 text-white hover:bg-blue-600;
  }
}

.item-card__actions-menu {
  @apply relative;
}

.item-card__actions-toggle {
  @apply w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-700;
  @apply flex items-center justify-center text-gray-600 dark:text-gray-400;
  @apply hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors;
}

.item-card__actions-dropdown {
  @apply absolute right-0 top-full mt-1 bg-white dark:bg-gray-800;
  @apply border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg;
  @apply min-w-[120px] z-20;
}

.item-card__action-item {
  @apply w-full flex items-center gap-2 px-3 py-2 text-sm text-left;
  @apply text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  @apply first:rounded-t-lg last:rounded-b-lg;

  &--primary {
    @apply text-blue-600 dark:text-blue-400;
  }
}

.item-card__loading {
  @apply absolute inset-0 bg-white bg-opacity-80 dark:bg-gray-800 dark:bg-opacity-80;
  @apply flex items-center justify-center rounded-lg;
}

.item-card__spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin;
}

// Icon styles (assuming icon font or SVG icons)
[class^="icon-"], [class*=" icon-"] {
  @apply inline-block w-4 h-4;
}

// Responsive adjustments
@screen sm {
  .item-card--small {
    @apply min-h-[90px];
  }
  
  .item-card--medium {
    @apply min-h-[130px];
  }
  
  .item-card--large {
    @apply min-h-[170px];
  }
}

// Dark mode specific adjustments
@media (prefers-color-scheme: dark) {
  .item-card {
    &--equipped {
      @apply bg-blue-900/30;
    }
  }
}
