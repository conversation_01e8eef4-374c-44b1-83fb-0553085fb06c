import {
  ComponentRef,
  Injectable,
  Type,
  ViewContainerRef,
} from '@angular/core';

import { Comic } from '@schema';
import { IPopupComponent } from 'src/app/core/interface';

@Injectable({
  providedIn: 'root',
})
export class PopupService {

  private components: Map<number, ComponentRef<IPopupComponent>> = new Map<
    number,
    ComponentRef<IPopupComponent>
  >();
  private _viewContainerRef?: ViewContainerRef;
  constructor() { }

  async showConfirmPopup({
    title,
    message,
    confirmButtonText,
    cancelButtonText,
  }: {
    title?: string;
    message?: string;
    confirmButtonText?: string;
    cancelButtonText?: string;
  }) {
    const { ConfirmPopupComponent } = await import('@components/lazy/confirm-popup/confirm-popup.component');

    const componentRef = this.createDynamicComponent(
      ConfirmPopupComponent
    );
    return componentRef?.instance.show({
      title,
      message,
      confirmButtonText,
      cancelButtonText,
    });
  }
  async showUserInfo({ userID }: { userID: number }) {
    const { UserInfoPopupComponent } = await import('@components/lazy/user-info-popup/user-info-popup.component');

    const componentRef = this.createDynamicComponent(
      UserInfoPopupComponent
    );
    return componentRef?.instance.show({ userID });
  }

  async showReportComic({
    comicID,
    chapterID,
  }: {
    comicID: number;
    chapterID?: number;
  }) {

    const { ReportErrorComponent } = await import('@components/lazy/report-error/report-error.component');
    const componentRef =
      this.createDynamicComponent(ReportErrorComponent);
    return componentRef?.instance.show({ comicID, chapterID });
  }
  async showFeedback() {
    const { FeedbackComponent } = await import('@components/lazy/feedback/feedback.component');
    const componentRef =
      this.createDynamicComponent(FeedbackComponent);

    return await componentRef?.instance.show();
  }

  async showRateComic({
    comicID,
    initialRating,
  }: {
    comicID: number;
    initialRating: number;
  }) {
    const { StarRatingComponent } = await import('@components/lazy/star-rating/star-rating.component');
    const componentRef =
      this.createDynamicComponent(StarRatingComponent);
    return componentRef?.instance.show({ comicID, initialRating });
  }
  async showDetailComic({ comic }: { comic: Comic }) {
    const { PopupDetailComicComponent } = await import('@components/lazy/popup-detail-comic/popup-detail-comic.component')

    const componentRef = this.createDynamicComponent(
      PopupDetailComicComponent
    );
    return componentRef?.instance.show({ comic });
  }

  async showSetting() {
    const { AppSettingComponent } = await import('@components/lazy/app-setting/app-setting.component');
    return this.createDynamicComponent(AppSettingComponent).instance.show({ selectedGroup: 1 });
  }

  set viewContainerRef(viewContainerRef: ViewContainerRef | undefined) {
    this._viewContainerRef = viewContainerRef;
    if (!viewContainerRef) {
      this.components.clear();
    }
  }

  hashCode = function (s: string) {
    let h = 0
    const l = s.length
    let i = 0;
    if (l > 0)
      while (i < l)
        h = (h << 5) - h + s.charCodeAt(i++) | 0;
    return h;
  };

  public createDynamicComponent<T extends IPopupComponent>(
    componentType: Type<T>,
  ): ComponentRef<T> {
    const key = this.hashCode(componentType.toString());

    if (this.components.has(key)) {
      const componentRef = this.components.get(
        key,
      ) as ComponentRef<T>;
      if (!componentRef.hostView.destroyed) return componentRef;
      this.components.delete(key);
    }
    const componentRef =
      this._viewContainerRef?.createComponent<T>(componentType)!;
    this.components.set(key, componentRef);
    return componentRef;
  }
}
